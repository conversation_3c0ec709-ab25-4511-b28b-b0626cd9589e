// ===== GLOBAL VARIABLES =====
let currentSection = 'dashboard';
let charts = {};
let chatbotOpen = false;
let selectedTable = null;
let animationFrameId = null;

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    initializeApp();
    
    // Emergency fallback - hide loader after 5 seconds no matter what
    setTimeout(() => {
        const loader = document.getElementById('loading-screen');
        if (loader && !loader.classList.contains('hidden')) {
            console.warn('Emergency loader hide activated');
            loader.style.display = 'none';
        }
    }, 5000);
});

function initializeApp() {
    // Show loading screen
    showLoadingScreen();
    
    // Backup timer to hide loader after max 3 seconds
    setTimeout(() => {
        hideLoadingScreen();
        console.warn('Forced loading screen hide after timeout');
    }, 3000);
    
    // Initialize components
    setTimeout(() => {
        try {
            initializeNavigation();
            initializeCounters();
            initializeCharts();
            initializeChatbot();
            initializeRealTimeUpdates();
            initializeInteractiveElements();
            hideLoadingScreen();
        } catch (error) {
            console.error('Initialization error:', error);
            hideLoadingScreen(); // Hide loader even if there's an error
        }
    }, 1000); // Reduced from 2000ms to 1000ms
}

// ===== LOADING SCREEN =====
function showLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        loadingScreen.classList.remove('hidden');
    }
}

function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        loadingScreen.classList.add('hidden');
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
        console.log('Loading screen hidden successfully');
    } else {
        console.error('Loading screen element not found');
    }
}

// ===== NAVIGATION =====
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', () => {
            const section = item.getAttribute('data-section');
            switchSection(section);
            
            // Update active state
            navItems.forEach(nav => nav.classList.remove('active'));
            item.classList.add('active');
        });
    });
}

function switchSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;
        
        // Trigger section-specific initialization
        initializeSectionFeatures(sectionName);
    }
}

function initializeSectionFeatures(sectionName) {
    switch(sectionName) {
        case 'dashboard':
            updateDashboardData();
            break;
        case 'menu-optimization':
            initializeMenuOptimization();
            break;
        case 'table-management':
            initializeTableManagement();
            break;
        case 'analytics':
            initializeAnalytics();
            break;
    }
}

// ===== ANIMATED COUNTERS =====
function initializeCounters() {
    const counters = document.querySelectorAll('[data-count]');
    
    counters.forEach(counter => {
        animateCounter(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-count'));
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// ===== CHARTS INITIALIZATION =====
function initializeCharts() {
    // Check if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initializeRevenueChart();
        initializeProfitMarginChart();
        initializeForecastChart();
        initializeBehaviorChart();
    } else {
        console.warn('Chart.js not loaded - charts will be displayed as placeholders');
    }
}

function initializeRevenueChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;
    
    charts.revenue = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'],
            datasets: [{
                label: 'Przychody (PLN)',
                data: [420, 680, 950, 1500, 2100, 1800, 1200, 1600, 2400, 3200, 3800, 3400],
                borderColor: 'rgba(102, 126, 234, 1)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.8)'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.8)',
                        callback: function(value) {
                            return 'PLN ' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animations: {
                tension: {
                    duration: 1000,
                    easing: 'easeInOutCubic',
                    from: 1,
                    to: 0.4,
                    loop: false
                }
            }
        }
    });
}

function initializeProfitMarginChart() {
    const ctx = document.getElementById('profitMarginChart');
    if (!ctx) return;
    
    charts.profitMargin = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Pizza Margherita', 'SaBatka Caesar', 'Spaghetti Carbonara', 'Risotto z grzybami', 'Aoso[ grillowany'],
            datasets: [{
                data: [34, 28, 22, 12, 4],
                backgroundColor: [
                    'rgba(252, 129, 129, 0.8)',
                    'rgba(255, 215, 0, 0.8)',
                    'rgba(104, 211, 145, 0.8)',
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(159, 122, 234, 0.8)'
                ],
                borderColor: [
                    'rgba(252, 129, 129, 1)',
                    'rgba(255, 215, 0, 1)',
                    'rgba(104, 211, 145, 1)',
                    'rgba(102, 126, 234, 1)',
                    'rgba(159, 122, 234, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'rgba(255, 255, 255, 0.8)',
                        padding: 20,
                        usePointStyle: true
                    }
                }
            },
            animations: {
                animateRotate: true,
                animateScale: true
            }
        }
    });
}

function initializeForecastChart() {
    const ctx = document.getElementById('forecastChart');
    if (!ctx) return;
    
    charts.forecast = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Pon', 'Wt', 'Zr', 'Czw', 'Pt', 'Sob', 'Nd'],
            datasets: [
                {
                    label: 'Rzeczywiste przychody',
                    data: [15847, 14200, 16800, 18500, 16200, 22400, 19800],
                    borderColor: 'rgba(104, 211, 145, 1)',
                    backgroundColor: 'rgba(104, 211, 145, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Prognoza AI',
                    data: [15800, 14100, 16900, 18600, 15800, 22200, 19900],
                    borderColor: 'rgba(102, 126, 234, 1)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.8)'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.8)'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.8)',
                        callback: function(value) {
                            return 'PLN ' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

function initializeBehaviorChart() {
    const ctx = document.getElementById('behaviorChart');
    if (!ctx) return;
    
    charts.behavior = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['Szybko[ obsBugi', 'Jako[ jedzenia', 'Atmosfera', 'Stosunek jako[ci do ceny', 'ObsBuga', 'Czysto['],
            datasets: [{
                label: 'Ocena klient�w',
                data: [8.5, 9.2, 8.8, 8.1, 9.0, 9.5],
                borderColor: 'rgba(102, 126, 234, 1)',
                backgroundColor: 'rgba(102, 126, 234, 0.2)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 10,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    angleLines: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    pointLabels: {
                        color: 'rgba(255, 255, 255, 0.8)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.6)',
                        backdropColor: 'transparent'
                    }
                }
            }
        }
    });
}

// ===== REAL-TIME UPDATES =====
function initializeRealTimeUpdates() {
    updateDashboardData();
    
    // Update every 30 seconds
    setInterval(updateDashboardData, 30000);
}

function updateDashboardData() {
    // Simulate real-time data updates
    updateMetricCards();
    updateRevenueChart();
    updateAIRecommendations();
}

function updateMetricCards() {
    // Simulate slight changes in metrics
    const revenueElement = document.querySelector('.metric-card.revenue .amount');
    const occupancyElement = document.querySelector('.metric-card.occupancy .amount');
    
    if (revenueElement) {
        const currentRevenue = parseInt(revenueElement.textContent.replace(/,/g, ''));
        const newRevenue = currentRevenue + Math.floor(Math.random() * 100) - 50;
        animateCounter(revenueElement, newRevenue);
    }
    
    if (occupancyElement) {
        const newOccupancy = 75 + Math.floor(Math.random() * 10);
        occupancyElement.textContent = newOccupancy;
        
        const progressBar = document.querySelector('.metric-card.occupancy .progress-bar');
        if (progressBar) {
            progressBar.style.width = newOccupancy + '%';
        }
    }
}

function updateRevenueChart() {
    if (!charts.revenue) return;
    
    // Add new data point
    const chart = charts.revenue;
    const now = new Date();
    const timeLabel = now.getHours() + ':' + (now.getMinutes() < 10 ? '0' : '') + now.getMinutes();
    
    if (chart.data.labels.length > 12) {
        chart.data.labels.shift();
        chart.data.datasets[0].data.shift();
    }
    
    chart.data.labels.push(timeLabel);
    chart.data.datasets[0].data.push(2000 + Math.floor(Math.random() * 2000));
    chart.update('none');
}

function updateAIRecommendations() {
    const recommendations = [
        {
            icon: 'fas fa-fire',
            title: 'Promocja ZBotej Godziny',
            description: '17:30-18:30: Zaoferuj 15% zni|ki na makarony - zwikszy to sprzeda| o ~23%',
            impact: '+PLN 340',
            priority: 'high-priority'
        },
        {
            icon: 'fas fa-chart-line',
            title: 'Optymalizacja menu',
            description: 'Przenie[ "Risotto z grzybami" wy|ej w menu - zwikszy to zam�wienia o 18%',
            impact: '+PLN 180/dzieD',
            priority: 'medium-priority'
        },
        {
            icon: 'fas fa-weather-cloud',
            title: 'Prognoza pogody',
            description: 'Jutro deszcz - przygotuj wicej ciepBych daD i promocj na dostaw',
            impact: '+PLN 220',
            priority: 'medium-priority'
        }
    ];
    
    const container = document.querySelector('.ai-recommendations');
    if (container) {
        container.innerHTML = '';
        
        recommendations.forEach(rec => {
            const div = document.createElement('div');
            div.className = `recommendation ${rec.priority}`;
            div.innerHTML = `
                <div class="rec-icon">
                    <i class="${rec.icon}"></i>
                </div>
                <div class="rec-content">
                    <h4>${rec.title}</h4>
                    <p>${rec.description}</p>
                    <div class="rec-impact">Potencjalny zysk: ${rec.impact}</div>
                </div>
                <button class="btn-small btn-primary" onclick="applyRecommendation('${rec.title}')">Zastosuj</button>
            `;
            container.appendChild(div);
        });
    }
}

// ===== TABLE MANAGEMENT =====
function initializeTableManagement() {
    setupFloorPlan();
    updateReservations();
}

function setupFloorPlan() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        table.addEventListener('click', function() {
            const tableId = this.getAttribute('data-table');
            selectTable(tableId);
        });
    });
}

function selectTable(tableId) {
    // Remove previous selection
    document.querySelectorAll('.table').forEach(table => {
        table.classList.remove('selected');
    });
    
    // Select new table
    const table = document.querySelector(`[data-table="${tableId}"]`);
    if (table) {
        table.classList.add('selected');
        selectedTable = tableId;
        showTableDetails(tableId);
    }
}

function showTableDetails(tableId) {
    const table = document.querySelector(`[data-table="${tableId}"]`);
    if (!table) return;
    
    const status = table.classList.contains('available') ? 'Dostpny' : 
                   table.classList.contains('occupied') ? 'Zajty' : 'Zarezerwowany';
    const capacity = table.querySelector('.table-capacity').textContent;
    
    // Show toast notification
    showToast(`St�B ${tableId} (${capacity} osoby) - Status: ${status}`, 'info');
}

function toggleTableStatus(tableId) {
    const table = document.querySelector(`[data-table="${tableId}"]`);
    if (!table) return;
    
    // Cycle through statuses
    if (table.classList.contains('available')) {
        table.classList.remove('available');
        table.classList.add('occupied');
    } else if (table.classList.contains('occupied')) {
        table.classList.remove('occupied');
        table.classList.add('reserved');
    } else {
        table.classList.remove('reserved');
        table.classList.add('available');
    }
}

function updateReservations() {
    // Simulate reservation updates
    const reservationsList = document.querySelector('.reservations-list');
    if (!reservationsList) return;
    
    // Add animation class to reservation items
    const items = reservationsList.querySelectorAll('.reservation-item');
    items.forEach((item, index) => {
        setTimeout(() => {
            item.style.animation = 'slideInUp 0.5s ease forwards';
        }, index * 100);
    });
}

// ===== MENU OPTIMIZATION =====
function initializeMenuOptimization() {
    updateMenuHeatmap();
    runMenuOptimization();
}

function updateMenuHeatmap() {
    const heatmapItems = document.querySelectorAll('.heatmap-item');
    
    heatmapItems.forEach(item => {
        const popularity = parseInt(item.getAttribute('data-popularity'));
        
        // Animate the heat effect
        setTimeout(() => {
            item.style.animation = 'heatmapGrow 1s ease forwards';
        }, Math.random() * 500);
    });
}

function optimizeMenu() {
    showToast('Optymalizacja menu w toku...', 'info');
    
    // Simulate optimization process
    setTimeout(() => {
        showToast('Menu zostaBo zoptymalizowane! Oczekiwany wzrost: +12%', 'success');
        updateMenuHeatmap();
    }, 2000);
}

function runMenuOptimization() {
    // Simulate AI analysis
    const insights = [
        'Pizza Margherita generuje 34% mar|y - zwiksz promocj',
        'SaBatka Caesar ma wysok popularno[ przy dobrej mar|y',
        'Risotto z grzybami - rozwa| zmian pozycji w menu',
        'Aoso[ grillowany - niska popularno[, przeanalizuj cen'
    ];
    
    console.log('AI Menu Insights:', insights);
}

// ===== ANALYTICS =====
function initializeAnalytics() {
    updateAnalyticsData();
    initializePeakHoursAnimation();
}

function updateAnalyticsData() {
    // Update behavior metrics with animation
    const metrics = document.querySelectorAll('.behavior-metric .metric-value');
    
    metrics.forEach(metric => {
        const originalValue = metric.textContent;
        metric.style.animation = 'counterUpdate 1s ease';
        
        setTimeout(() => {
            metric.style.animation = '';
        }, 1000);
    });
}

function initializePeakHoursAnimation() {
    const hourBars = document.querySelectorAll('.hour-bar');
    
    hourBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.animation = 'growUp 1s ease forwards';
        }, index * 100);
    });
}

function exportAnalytics() {
    showToast('Eksportowanie danych analitycznych...', 'info');
    
    // Simulate export process
    setTimeout(() => {
        const data = {
            revenue: 'PLN 15,847',
            customers: 342,
            avgOrderValue: 'PLN 87',
            satisfaction: '94%',
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `gastroai-analytics-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        showToast('Dane zostaBy wyeksportowane!', 'success');
    }, 1500);
}

// ===== CHATBOT =====
function initializeChatbot() {
    const chatbotContainer = document.getElementById('chatbot');
    const inputField = document.getElementById('chatbot-input-field');
    
    if (inputField) {
        inputField.addEventListener('keypress', handleChatbotEnter);
    }
}

function toggleChatbot() {
    const chatbot = document.getElementById('chatbot');
    const fab = document.querySelector('.chatbot-fab');
    
    if (!chatbot) return;
    
    chatbotOpen = !chatbotOpen;
    
    if (chatbotOpen) {
        chatbot.classList.add('active');
        fab.style.display = 'none';
    } else {
        chatbot.classList.remove('active');
        fab.style.display = 'flex';
    }
}

function handleChatbotEnter(event) {
    if (event.key === 'Enter') {
        sendChatbotMessage();
    }
}

function sendChatbotMessage() {
    const input = document.getElementById('chatbot-input-field');
    const message = input.value.trim();
    
    if (!message) return;
    
    addChatbotMessage(message, 'user');
    input.value = '';
    
    // Simulate AI response
    setTimeout(() => {
        const response = generateAIResponse(message);
        addChatbotMessage(response, 'bot');
    }, 1000);
}

function addChatbotMessage(message, sender) {
    const messagesContainer = document.getElementById('chatbot-messages');
    if (!messagesContainer) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    if (sender === 'user') {
        messageDiv.innerHTML = `
            <div class="message-content">
                <p>${message}</p>
                <span class="message-time">Teraz</span>
            </div>
        `;
    } else {
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <p>${message}</p>
                <span class="message-time">Teraz</span>
            </div>
        `;
    }
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function generateAIResponse(userMessage) {
    const responses = {
        'rezerwacje': 'Aktualnie masz 3 rezerwacje na dzi[. Czy chcesz doda now rezerwacj?',
        'menu': 'Pizza Margherita jest dzi[ najcz[ciej zamawiana. Czy chcesz zobaczy peBn analiz menu?',
        'analityka': 'Dzisiejsze przychody wynosz PLN 15,847 (+12.5% w por�wnaniu do wczoraj). Czy chcesz szczeg�Bowy raport?',
        'stoBy': 'Obecnie 78% stoB�w jest zajtych. St�B 6 bdzie wolny za 15 minut.',
        'default': 'Dziki za wiadomo[! Jak mog Ci pom�c z zarzdzaniem restauracj? Mo|esz zapyta o rezerwacje, menu, analityk lub stoBy.'
    };
    
    const lowerMessage = userMessage.toLowerCase();
    
    for (const [key, response] of Object.entries(responses)) {
        if (lowerMessage.includes(key)) {
            return response;
        }
    }
    
    return responses.default;
}

function chatbotAction(action) {
    switch(action) {
        case 'reservations':
            switchSection('table-management');
            toggleChatbot();
            showToast('Przechodz do zarzdzania stoBami', 'info');
            break;
        case 'menu':
            switchSection('menu-optimization');
            toggleChatbot();
            showToast('Przechodz do optymalizacji menu', 'info');
            break;
        case 'analytics':
            switchSection('analytics');
            toggleChatbot();
            showToast('Przechodz do analityki', 'info');
            break;
    }
}

// ===== INTERACTIVE ELEMENTS =====
function initializeInteractiveElements() {
    initializeTooltips();
    initializeDropdowns();
    initializeModals();
}

function initializeTooltips() {
    const elements = document.querySelectorAll('[title]');
    
    elements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function initializeDropdowns() {
    const userMenu = document.querySelector('.user-menu');
    
    if (userMenu) {
        userMenu.addEventListener('click', toggleUserMenu);
    }
}

function initializeModals() {
    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-overlay')) {
            closeModal();
        }
    });
}

// ===== UTILITY FUNCTIONS =====
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideInRight 0.3s ease';
    }, 100);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

function refreshDashboard() {
    showToast('Od[wie|anie danych...', 'info');
    
    // Simulate refresh
    setTimeout(() => {
        updateDashboardData();
        showToast('Dane zostaBy od[wie|one!', 'success');
    }, 1500);
}

function generateReport() {
    showToast('Generowanie raportu...', 'info');
    
    // Simulate report generation
    setTimeout(() => {
        const report = {
            date: new Date().toISOString().split('T')[0],
            revenue: 'PLN 15,847',
            orders: 182,
            customers: 342,
            satisfaction: '94%',
            topDishes: ['Pizza Margherita', 'SaBatka Caesar', 'Spaghetti Carbonara']
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `gastroai-report-${report.date}.json`;
        a.click();
        
        showToast('Raport zostaB wygenerowany!', 'success');
    }, 2000);
}

function addReservation() {
    showToast('Dodawanie nowej rezerwacji...', 'info');
    
    // Simulate adding reservation
    setTimeout(() => {
        showToast('Rezerwacja zostaBa dodana!', 'success');
        updateReservations();
    }, 1000);
}

function editReservation(id) {
    showToast(`Edytowanie rezerwacji #${id}`, 'info');
}

function confirmReservation(id) {
    showToast(`Rezerwacja #${id} zostaBa potwierdzona`, 'success');
}

function applyRecommendation(title) {
    showToast(`Zastosowano rekomendacj: ${title}`, 'success');
}

function toggleChartView(chartType) {
    showToast(`Rozwijanie wykresu: ${chartType}`, 'info');
}

function toggleFloorPlan() {
    const floorPlan = document.querySelector('.floor-plan');
    if (floorPlan) {
        floorPlan.classList.toggle('expanded');
        showToast('PrzeBczono widok planu sali', 'info');
    }
}

// ===== KEYBOARD SHORTCUTS =====
document.addEventListener('keydown', function(e) {
    // Alt + D - Dashboard
    if (e.altKey && e.code === 'KeyD') {
        switchSection('dashboard');
        document.querySelector('[data-section="dashboard"]').classList.add('active');
    }
    
    // Alt + M - Menu
    if (e.altKey && e.code === 'KeyM') {
        switchSection('menu-optimization');
    }
    
    // Alt + T - Tables
    if (e.altKey && e.code === 'KeyT') {
        switchSection('table-management');
    }
    
    // Alt + A - Analytics
    if (e.altKey && e.code === 'KeyA') {
        switchSection('analytics');
    }
    
    // Alt + C - Chatbot
    if (e.altKey && e.code === 'KeyC') {
        toggleChatbot();
    }
    
    // Escape - Close modals/chatbot
    if (e.code === 'Escape') {
        if (chatbotOpen) {
            toggleChatbot();
        }
    }
});

// ===== PERFORMANCE MONITORING =====
function trackPerformance() {
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('GastroAI Performance:', {
                    loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart
                });
            }, 0);
        });
    }
}

// Initialize performance tracking
trackPerformance();

// ===== ERROR HANDLING =====
window.addEventListener('error', function(e) {
    console.error('GastroAI Error:', e.error);
    showToast('WystpiB bBd. Spr�buj od[wie|y stron.', 'error');
});

// ===== SERVICE WORKER REGISTRATION =====
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registered successfully');
            })
            .catch(function(error) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// ===== RESPONSIVE UTILITIES =====
function isMobile() {
    return window.innerWidth <= 768;
}

function isTablet() {
    return window.innerWidth > 768 && window.innerWidth <= 1024;
}

function handleResize() {
    if (isMobile() && chatbotOpen) {
        const chatbot = document.getElementById('chatbot');
        if (chatbot) {
            chatbot.style.bottom = '1rem';
            chatbot.style.left = '1rem';
            chatbot.style.right = '1rem';
            chatbot.style.width = 'auto';
        }
    }
}

window.addEventListener('resize', handleResize);

// ===== DATA PERSISTENCE =====
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(`gastroai-${key}`, JSON.stringify(data));
    } catch (e) {
        console.warn('Could not save to localStorage:', e);
    }
}

function loadFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(`gastroai-${key}`);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.warn('Could not load from localStorage:', e);
        return null;
    }
}

// ===== CSS ANIMATIONS (Added via JavaScript) =====
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    @keyframes heatmapGrow {
        from { transform: scaleX(0); }
        to { transform: scaleX(1); }
    }
    
    @keyframes counterUpdate {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); color: #4ecdc4; }
        100% { transform: scale(1); }
    }
    
    .toast {
        position: fixed;
        top: 100px;
        right: -400px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        min-width: 300px;
    }
    
    .toast-success { border-left: 4px solid #68d391; }
    .toast-error { border-left: 4px solid #fc8181; }
    .toast-info { border-left: 4px solid #4299e1; }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: #2d3748;
        font-weight: 500;
    }
    
    .table.selected {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        border-color: #667eea !important;
    }
`;

document.head.appendChild(style);

console.log('=� GastroAI System Initialized Successfully!');

// ===== QR MENU FUNCTIONALITY =====
function showQRMenu() {
    const modal = document.getElementById('qr-menu-modal');
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        showToast('Menu cyfrowe zostało otwarte', 'info');
    }
}

function closeQRMenu() {
    const modal = document.getElementById('qr-menu-modal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
        showToast('Menu zostało zamknięte', 'info');
    }
}

function openFullMenu() {
    showToast('Otwieranie pełnego menu...', 'info');
    setTimeout(() => {
        const fullMenuData = {
            restaurantName: 'GastroAI Restaurant',
            categories: [
                { name: 'Pizze', items: [{ name: 'Pizza Margherita', price: '35 PLN' }] },
                { name: 'Sałatki', items: [{ name: 'Sałatka Caesar', price: '28 PLN' }] }
            ]
        };
        const blob = new Blob([JSON.stringify(fullMenuData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'gastroai-full-menu.json';
        a.click();
        showToast('Pełne menu zostało pobrane\!', 'success');
    }, 1500);
}

function generateQRCode() {
    showToast('Generowanie nowego kodu QR...', 'info');
    setTimeout(() => {
        const qrImage = document.querySelector('.qr-code img');
        if (qrImage) {
            const newPattern = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"%3E%3Crect width="200" height="200" fill="%23ffffff"/%3E%3Crect x="20" y="20" width="30" height="30" fill="%23000000"/%3E%3C/svg%3E';
            qrImage.src = newPattern;
        }
        showToast('Nowy kod QR został wygenerowany\!', 'success');
    }, 1000);
}
