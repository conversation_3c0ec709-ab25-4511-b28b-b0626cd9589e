/**
 * Content Suite - BrandMe.pl
 * AI-Powered Content Creation Tools
 */

class ContentSuite {
    constructor() {
        this.currentTool = 'linkedin-posts';
        this.currentTab = 'quick';
        this.generatedContent = {};
        this.currentDate = new Date();
        this.contentCalendar = {};
        this.selectedPlatforms = ['linkedin'];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeTools();
        this.loadUserData();
        this.initializeCalendar();
    }

    bindEvents() {
        // Tool navigation
        document.querySelectorAll('.tool-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const tool = e.currentTarget.getAttribute('data-tool');
                this.switchTool(tool);
            });
        });

        // Category toggles
        document.querySelectorAll('.category-header').forEach(header => {
            header.addEventListener('click', (e) => {
                this.toggleCategory(e.currentTarget.parentElement);
            });
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.currentTarget.getAttribute('data-tab');
                this.switchTab(tab);
            });
        });

        // LinkedIn post generation
        this.bindLinkedInEvents();
        
        // Bio generator
        this.bindBioGeneratorEvents();
        
        // Content calendar
        this.bindCalendarEvents();
        
        // AI assistant
        this.bindAIAssistantEvents();
        
        // Export and publish
        this.bindGlobalActions();
        
        // Range input updates
        this.bindRangeInputs();
    }

    // ===== TOOL NAVIGATION ===== //
    switchTool(toolName) {
        // Update active tool item
        document.querySelectorAll('.tool-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');

        // Update active tool content
        document.querySelectorAll('.content-tool').forEach(tool => {
            tool.classList.remove('active');
        });
        document.getElementById(toolName).classList.add('active');

        this.currentTool = toolName;
        
        // Initialize tool-specific functionality
        this.initializeTool(toolName);
    }

    toggleCategory(category) {
        const tools = category.querySelector('.category-tools');
        const isVisible = tools.style.display !== 'none';
        
        tools.style.display = isVisible ? 'none' : 'block';
        category.classList.toggle('collapsed', isVisible);
    }

    switchTab(tabName) {
        // Update active tab button
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update active tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
    }

    initializeTool(toolName) {
        switch (toolName) {
            case 'linkedin-posts':
                this.initializeLinkedInTool();
                break;
            case 'bio-generator':
                this.initializeBioGenerator();
                break;
            case 'content-calendar':
                this.generateCalendar();
                break;
            // Add more tools as needed
        }
    }

    initializeTools() {
        // Initialize first tool
        this.initializeTool(this.currentTool);
    }

    // ===== LINKEDIN POST GENERATOR ===== //
    bindLinkedInEvents() {
        // Generate buttons
        document.getElementById('generate-linkedin-post')?.addEventListener('click', () => {
            this.generateLinkedInPost('quick');
        });
        
        document.getElementById('generate-advanced-post')?.addEventListener('click', () => {
            this.generateLinkedInPost('advanced');
        });

        // Tone selection
        document.querySelectorAll('.tone-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.tone-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Template selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const template = e.currentTarget.getAttribute('data-template');
                this.useTemplate(template);
            });
        });

        // Action buttons
        document.getElementById('regenerate-post')?.addEventListener('click', () => {
            this.regenerateCurrentPost();
        });
        
        document.getElementById('copy-post')?.addEventListener('click', () => {
            this.copyPostToClipboard();
        });
        
        document.getElementById('save-post')?.addEventListener('click', () => {
            this.savePost();
        });
        
        document.getElementById('schedule-post')?.addEventListener('click', () => {
            this.schedulePost();
        });
    }

    initializeLinkedInTool() {
        // Auto-focus on topic input
        const topicInput = document.getElementById('post-topic');
        if (topicInput) {
            setTimeout(() => topicInput.focus(), 100);
        }
    }

    async generateLinkedInPost(mode = 'quick') {
        const button = document.getElementById(mode === 'quick' ? 'generate-linkedin-post' : 'generate-advanced-post');
        const originalText = button.innerHTML;
        
        // Validate inputs
        if (!this.validateLinkedInInputs(mode)) return;
        
        // Show loading state
        button.innerHTML = '<i class="fas fa-magic fa-spin"></i> Generuję...';
        button.disabled = true;

        try {
            // Simulate AI processing
            await this.simulateAIProcessing();
            
            // Generate content
            const postData = this.getLinkedInInputData(mode);
            const generatedPost = await this.generatePostContent(postData);
            
            // Display result
            this.displayLinkedInPost(generatedPost);
            
            // Update analytics
            this.updatePostAnalytics(generatedPost);
            
            this.showNotification('Post LinkedIn został wygenerowany!', 'success');
            
        } catch (error) {
            this.showNotification('Błąd generowania postu', 'error');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    validateLinkedInInputs(mode) {
        if (mode === 'quick') {
            const topic = document.getElementById('post-topic').value;
            if (!topic.trim()) {
                this.showNotification('Wprowadź temat postu', 'error');
                return false;
            }
        } else {
            const keyMessage = document.getElementById('key-message').value;
            if (!keyMessage.trim()) {
                this.showNotification('Wprowadź kluczową wiadomość', 'error');
                return false;
            }
        }
        return true;
    }

    getLinkedInInputData(mode) {
        const baseData = {
            mode: mode,
            postType: document.getElementById('post-type')?.value || 'insight',
            tone: document.querySelector('.tone-btn.active')?.getAttribute('data-tone') || 'professional',
            callToAction: document.getElementById('call-to-action')?.value || 'engagement'
        };

        if (mode === 'quick') {
            baseData.topic = document.getElementById('post-topic').value;
        } else {
            baseData.targetAudience = document.getElementById('target-audience').value;
            baseData.keyMessage = document.getElementById('key-message').value;
            baseData.postLength = document.getElementById('post-length').value;
            baseData.includeEmojis = document.getElementById('include-emojis').checked;
            baseData.hashtagsCount = document.getElementById('hashtags-count').value;
        }

        return baseData;
    }

    async generatePostContent(postData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        const templates = {
            insight: {
                professional: [
                    `💡 Insight z branży {industry}:

{topic} to temat, który ostatnio często pojawia się w dyskusjach profesjonalistów.

Na podstawie mojego doświadczenia mogę powiedzieć, że kluczowe jest:
• {point1}
• {point2}
• {point3}

{callToAction}

{hashtags}`,
                    
                    `🔍 Moja obserwacja na temat {topic}:

Po {experience} latach w branży zauważam trend, który warto omówić.

{keyMessage}

Trzy najważniejsze wnioski:
1️⃣ {insight1}
2️⃣ {insight2}
3️⃣ {insight3}

{callToAction}

{hashtags}`
                ]
            },
            story: {
                professional: [
                    `📖 Historia z pierwszej ręki:

Niedawno miałem sytuację związaną z {topic}, która nauczyła mnie ważnej lekcji.

{story}

Co z tego wyniosłem:
→ {lesson1}
→ {lesson2}
→ {lesson3}

{callToAction}

{hashtags}`,
                    
                    `🎯 Case study z mojej praktyki:

{topic} - wyzwanie, które niedawno rozwiązywałem dla klienta.

Sytuacja:
{situation}

Rozwiązanie:
{solution}

Rezultat:
{result}

{callToAction}

{hashtags}`
                ]
            },
            question: {
                professional: [
                    `❓ Pytanie do społeczności:

{topic} - temat, który budzi sporo emocji w naszej branży.

Moja perspektywa:
{perspective}

Ale jestem ciekaw Waszych opinii:
• {question1}
• {question2}
• {question3}

Podzielcie się swoimi doświadczeniami w komentarzach! 👇

{hashtags}`,
                    
                    `🤔 Dyskusja ekspercka:

{topic} - czy to rzeczywiście przyszłość naszej branży?

Argumenty ZA:
✅ {pro1}
✅ {pro2}

Argumenty PRZECIW:
❌ {con1}
❌ {con2}

Co myślicie? Jakie są Wasze doświadczenia?

{hashtags}`
                ]
            }
        };

        // Select appropriate template
        const postType = postData.postType || 'insight';
        const tone = postData.tone || 'professional';
        const templateCategory = templates[postType] || templates.insight;
        const templateList = templateCategory[tone] || templateCategory.professional;
        const template = templateList[Math.floor(Math.random() * templateList.length)];

        // Generate content based on input
        const generatedContent = this.fillTemplate(template, postData);
        
        return {
            text: generatedContent,
            type: postType,
            tone: tone,
            estimatedReach: this.calculateEstimatedReach(postData),
            engagementScore: this.calculateEngagementScore(postData),
            hashtags: this.generateHashtags(postData)
        };
    }

    fillTemplate(template, data) {
        const replacements = {
            '{topic}': data.topic || data.keyMessage || 'rozwój zawodowy',
            '{industry}': this.getUserIndustry() || 'technologii',
            '{experience}': this.getUserExperience() || '5',
            '{keyMessage}': data.keyMessage || 'kluczowa obserwacja z mojej praktyki',
            '{point1}': this.generatePoint(data, 1),
            '{point2}': this.generatePoint(data, 2),
            '{point3}': this.generatePoint(data, 3),
            '{insight1}': this.generateInsight(data, 1),
            '{insight2}': this.generateInsight(data, 2),
            '{insight3}': this.generateInsight(data, 3),
            '{lesson1}': this.generateLesson(data, 1),
            '{lesson2}': this.generateLesson(data, 2),
            '{lesson3}': this.generateLesson(data, 3),
            '{question1}': this.generateQuestion(data, 1),
            '{question2}': this.generateQuestion(data, 2),
            '{question3}': this.generateQuestion(data, 3),
            '{story}': this.generateStory(data),
            '{situation}': this.generateSituation(data),
            '{solution}': this.generateSolution(data),
            '{result}': this.generateResult(data),
            '{perspective}': this.generatePerspective(data),
            '{pro1}': this.generatePro(data, 1),
            '{pro2}': this.generatePro(data, 2),
            '{con1}': this.generateCon(data, 1),
            '{con2}': this.generateCon(data, 2),
            '{callToAction}': this.generateCallToAction(data.callToAction),
            '{hashtags}': this.generateHashtagString(data)
        };

        let result = template;
        Object.keys(replacements).forEach(key => {
            result = result.replace(new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacements[key]);
        });

        return result;
    }

    generatePoint(data, index) {
        const points = [
            'Zrozumienie potrzeb użytkowników',
            'Ciągłe doskonalenie procesów',
            'Inwestycja w rozwój zespołu',
            'Analiza danych i metrics',
            'Budowanie długoterminowych relacji'
        ];
        return points[index - 1] || points[0];
    }

    generateInsight(data, index) {
        const insights = [
            'Technologia to tylko narzędzie - liczy się sposób jej wykorzystania',
            'Najlepsze rozwiązania powstają z połączenia doświadczenia i innowacji',
            'Kluczem do sukcesu jest koncentracja na value dla klienta'
        ];
        return insights[index - 1] || insights[0];
    }

    generateLesson(data, index) {
        const lessons = [
            'Czasami warto zatrzymać się i przemyśleć strategię',
            'Feedback od zespołu jest bezcenny',
            'Proste rozwiązania często są najefektywniejsze'
        ];
        return lessons[index - 1] || lessons[0];
    }

    generateQuestion(data, index) {
        const questions = [
            'Jakie są Wasze doświadczenia z tym zagadnieniem?',
            'Jak rozwiązujecie podobne wyzwania w swoich organizacjach?',
            'Czy widzicie podobne trendy w swojej branży?'
        ];
        return questions[index - 1] || questions[0];
    }

    generateStory(data) {
        return `Pracowałem nad projektem związanym z ${data.topic || 'optymalizacją procesów'}. Początkowo wydawało się to proste, ale po głębszej analizie okazało się znacznie bardziej złożone.`;
    }

    generateSituation(data) {
        return `Klient borykał się z problemem związanym z ${data.topic || 'efektywnością zespołu'}. Dotychczasowe rozwiązania nie przynosiły oczekiwanych rezultatów.`;
    }

    generateSolution(data) {
        return `Zaproponowałem holistyczne podejście łączące analizę danych, optymalizację procesów i development zespołu. Kluczowe było zrozumienie root cause problemu.`;
    }

    generateResult(data) {
        return `Po 3 miesiącach implementacji osiągnęliśmy 40% wzrost efektywności i znaczną poprawę satysfakcji zespołu. ROI przekroczył oczekiwania o 150%.`;
    }

    generatePerspective(data) {
        return `Z mojego doświadczenia wynika, że ${data.topic || 'nowoczesne technologie'} mają ogromny potencjał, ale wymagają przemyślanego podejścia do implementacji.`;
    }

    generatePro(data, index) {
        const pros = [
            'Znacznie zwiększa efektywność pracy',
            'Otwiera nowe możliwości biznesowe'
        ];
        return pros[index - 1] || pros[0];
    }

    generateCon(data, index) {
        const cons = [
            'Wymaga znacznych inwestycji w szkolenia',
            'Może spotkać się z oporem zespołu'
        ];
        return cons[index - 1] || cons[0];
    }

    generateCallToAction(type) {
        const ctas = {
            engagement: 'Co myślicie? Podzielcie się swoimi doświadczeniami w komentarzach! 👇',
            share: 'Jeśli ten post był pomocny, udostępnij go swojej sieci! 🔄',
            connect: 'Połączmy się i kontynuujmy tę dyskusję! 🤝',
            visit: 'Więcej szczegółów znajdziecie na mojej stronie. Link w bio! 🔗',
            none: ''
        };
        return ctas[type] || ctas.engagement;
    }

    generateHashtags(data) {
        const industryHashtags = {
            technology: ['#Tech', '#Innovation', '#DigitalTransformation', '#AI', '#Development'],
            marketing: ['#Marketing', '#DigitalMarketing', '#PersonalBrand', '#ContentMarketing', '#SEO'],
            business: ['#Business', '#Leadership', '#Strategy', '#Growth', '#Entrepreneurship'],
            default: ['#PersonalBrand', '#ProfessionalDevelopment', '#Career', '#Leadership', '#Growth']
        };

        const industry = this.getUserIndustry() || 'default';
        const hashtags = industryHashtags[industry] || industryHashtags.default;
        const count = parseInt(data.hashtagsCount) || 5;
        
        return hashtags.slice(0, count);
    }

    generateHashtagString(data) {
        const hashtags = this.generateHashtags(data);
        return hashtags.join(' ');
    }

    displayLinkedInPost(postData) {
        const postTextElement = document.getElementById('generated-post-text');
        if (postTextElement) {
            postTextElement.textContent = postData.text;
        }
        
        // Store generated content
        this.generatedContent.currentPost = postData;
        
        // Update character count if needed
        this.updateCharacterCount(postData.text);
    }

    updatePostAnalytics(postData) {
        // Update estimated metrics based on post content
        const baseReach = 1000;
        const reachMultiplier = this.calculateReachMultiplier(postData);
        const estimatedReach = Math.round(baseReach * reachMultiplier);
        
        const baseLikes = Math.round(estimatedReach * 0.07); // 7% engagement rate
        const baseComments = Math.round(baseLikes * 0.15); // 15% of likes
        const engagementRate = ((baseLikes + baseComments) / estimatedReach * 100).toFixed(1);

        // Update UI
        const metrics = document.querySelectorAll('.metric-value');
        if (metrics.length >= 4) {
            metrics[0].textContent = estimatedReach.toLocaleString();
            metrics[1].textContent = baseLikes;
            metrics[2].textContent = baseComments;
            metrics[3].textContent = `${engagementRate}%`;
        }

        // Update optimization tips
        this.updateOptimizationTips(postData);
    }

    calculateReachMultiplier(postData) {
        let multiplier = 1;
        
        // Post type impact
        const typeMultipliers = {
            insight: 1.3,
            story: 1.5,
            question: 1.7,
            achievement: 1.2,
            'industry-news': 1.1
        };
        multiplier *= typeMultipliers[postData.type] || 1;
        
        // Tone impact
        const toneMultipliers = {
            professional: 1.2,
            inspirational: 1.4,
            casual: 1.0,
            humorous: 1.3
        };
        multiplier *= toneMultipliers[postData.tone] || 1;
        
        // Hashtag impact
        if (postData.hashtags && postData.hashtags.length > 0) {
            multiplier *= 1.2;
        }
        
        return multiplier;
    }

    updateOptimizationTips(postData) {
        const tips = [
            'Publikuj w godzinach 8:00-10:00 dla maksymalnego zasięgu',
            'Dodaj zdjęcie lub grafikę dla 50% więcej engagement',
            'Reaguj na komentarze w pierwszej godzinie po publikacji'
        ];

        // Add specific tips based on post analysis
        if (!postData.text.includes('?')) {
            tips.unshift('Dodaj pytanie na końcu, aby zwiększyć engagement');
        }
        
        if (postData.hashtags.length < 3) {
            tips.push('Użyj 3-5 hashtagów branżowych dla lepszej widoczności');
        }
        
        if (postData.text.length < 100) {
            tips.push('Rozszerz treść - dłuższe posty często mają lepszy engagement');
        }

        const suggestionsList = document.getElementById('optimization-suggestions');
        if (suggestionsList) {
            suggestionsList.innerHTML = tips.slice(0, 4).map(tip => `<li>${tip}</li>`).join('');
        }
    }

    regenerateCurrentPost() {
        if (this.generatedContent.currentPost) {
            // Use the same data but generate new content
            this.generateLinkedInPost(this.currentTab);
        }
    }

    copyPostToClipboard() {
        if (this.generatedContent.currentPost) {
            navigator.clipboard.writeText(this.generatedContent.currentPost.text).then(() => {
                this.showNotification('Post skopiowany do schowka!', 'success');
            }).catch(() => {
                this.showNotification('Błąd kopiowania', 'error');
            });
        }
    }

    savePost() {
        if (this.generatedContent.currentPost) {
            // Save to localStorage or send to server
            const savedPosts = JSON.parse(localStorage.getItem('brandme_saved_posts') || '[]');
            savedPosts.push({
                ...this.generatedContent.currentPost,
                savedAt: new Date().toISOString(),
                platform: 'linkedin'
            });
            localStorage.setItem('brandme_saved_posts', JSON.stringify(savedPosts));
            
            this.showNotification('Post został zapisany!', 'success');
        }
    }

    schedulePost() {
        if (this.generatedContent.currentPost) {
            // Open scheduling modal or redirect to scheduling interface
            this.showNotification('Funkcja planowania zostanie wkrótce dodana!', 'info');
        }
    }

    useTemplate(templateName) {
        // Pre-fill form based on selected template
        const templates = {
            'success-story': {
                type: 'achievement',
                topic: 'Moje ostatnie osiągnięcie zawodowe',
                tone: 'inspirational',
                cta: 'share'
            },
            'industry-insight': {
                type: 'insight',
                topic: 'Najnowsze trendy w mojej branży',
                tone: 'professional',
                cta: 'engagement'
            },
            'behind-scenes': {
                type: 'story',
                topic: 'Kulisy mojej codziennej pracy',
                tone: 'casual',
                cta: 'engagement'
            },
            'lesson-learned': {
                type: 'story',
                topic: 'Ważna lekcja z mojego doświadczenia',
                tone: 'professional',
                cta: 'engagement'
            },
            'team-appreciation': {
                type: 'achievement',
                topic: 'Podziękowanie dla mojego zespołu',
                tone: 'inspirational',
                cta: 'share'
            },
            'thought-leadership': {
                type: 'insight',
                topic: 'Moja opinia ekspercka na temat...',
                tone: 'professional',
                cta: 'engagement'
            }
        };

        const template = templates[templateName];
        if (template) {
            // Fill form fields
            const topicInput = document.getElementById('post-topic');
            const typeSelect = document.getElementById('post-type');
            const ctaSelect = document.getElementById('call-to-action');
            
            if (topicInput) topicInput.value = template.topic;
            if (typeSelect) typeSelect.value = template.type;
            if (ctaSelect) ctaSelect.value = template.cta;
            
            // Update tone selection
            document.querySelectorAll('.tone-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-tone') === template.tone) {
                    btn.classList.add('active');
                }
            });
            
            this.showNotification(`Szablon "${templateName}" został załadowany`, 'info');
        }
    }

    // ===== BIO GENERATOR ===== //
    bindBioGeneratorEvents() {
        // Platform selection
        document.querySelectorAll('.platform-option input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.updateSelectedPlatforms();
            });
        });

        // Personality selection
        document.querySelectorAll('.personality-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.personality-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Generate bio button
        document.getElementById('generate-bio')?.addEventListener('click', () => {
            this.generateBios();
        });

        // Copy bio buttons
        document.querySelectorAll('.copy-bio-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const platform = e.currentTarget.getAttribute('data-platform');
                this.copyBioToClipboard(platform);
            });
        });

        // Copy all bios
        document.getElementById('copy-all-bio')?.addEventListener('click', () => {
            this.copyAllBios();
        });

        // Export bios
        document.getElementById('export-bio')?.addEventListener('click', () => {
            this.exportBios();
        });
    }

    initializeBioGenerator() {
        this.updateSelectedPlatforms();
    }

    updateSelectedPlatforms() {
        this.selectedPlatforms = [];
        document.querySelectorAll('.platform-option input[type="checkbox"]:checked').forEach(checkbox => {
            this.selectedPlatforms.push(checkbox.value);
        });
    }

    async generateBios() {
        if (this.selectedPlatforms.length === 0) {
            this.showNotification('Wybierz przynajmniej jedną platformę', 'error');
            return;
        }

        if (!this.validateBioInputs()) return;

        const button = document.getElementById('generate-bio');
        const originalText = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-magic fa-spin"></i> Generuję bio...';
        button.disabled = true;

        try {
            const bioData = this.getBioInputData();
            
            for (const platform of this.selectedPlatforms) {
                await this.simulateAIProcessing(500); // Shorter delay for each platform
                const bio = await this.generateBioForPlatform(platform, bioData);
                this.displayBio(platform, bio);
            }
            
            this.showNotification('Bio zostało wygenerowane dla wszystkich platform!', 'success');
            
        } catch (error) {
            this.showNotification('Błąd generowania bio', 'error');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    validateBioInputs() {
        const name = document.getElementById('bio-name').value;
        const title = document.getElementById('bio-title').value;
        
        if (!name.trim()) {
            this.showNotification('Wprowadź imię i nazwisko', 'error');
            return false;
        }
        
        if (!title.trim()) {
            this.showNotification('Wprowadź obecną pozycję', 'error');
            return false;
        }
        
        return true;
    }

    getBioInputData() {
        return {
            name: document.getElementById('bio-name').value,
            title: document.getElementById('bio-title').value,
            company: document.getElementById('bio-company').value,
            industry: document.getElementById('bio-industry').value,
            experience: document.getElementById('bio-experience').value,
            skills: document.getElementById('bio-skills').value,
            achievements: document.getElementById('bio-achievements').value,
            goal: document.getElementById('bio-goal').value,
            personality: document.querySelector('.personality-btn.active')?.getAttribute('data-personality') || 'professional',
            interests: document.getElementById('bio-interests').value
        };
    }

    async generateBioForPlatform(platform, data) {
        const templates = {
            linkedin: this.generateLinkedInBio(data),
            twitter: this.generateTwitterBio(data),
            instagram: this.generateInstagramBio(data),
            website: this.generateWebsiteBio(data)
        };

        return templates[platform] || templates.linkedin;
    }

    generateLinkedInBio(data) {
        const experienceText = this.getExperienceText(data.experience);
        const companyText = data.company ? ` w ${data.company}` : '';
        const skillsText = data.skills ? `\n\n🎯 Specjalizacje:\n${data.skills.split(',').map(skill => `• ${skill.trim()}`).join('\n')}` : '';
        const achievementsText = data.achievements ? `\n\n🏆 Osiągnięcia:\n${data.achievements}` : '';
        const interestsText = data.interests ? `\n\n💡 Zainteresowania: ${data.interests}` : '';
        
        let bio = `${data.title}${companyText} | ${experienceText} doświadczenia w branży ${data.industry || 'technologii'}`;
        
        if (data.goal) {
            const goalTexts = {
                'networking': '\n\n🤝 Pasjonuję się networkingiem i dzieleniem się wiedzą z profesjonalistami z branży.',
                'job-seeking': '\n\n🚀 Otwarty na nowe wyzwania zawodowe i możliwości rozwoju.',
                'business': '\n\n💼 Pomagam firmom osiągać cele biznesowe przez innowacyjne rozwiązania.',
                'thought-leadership': '\n\n💭 Dzielę się przemyśleniami i trendami branżowymi.',
                'personal-brand': '\n\n⭐ Buduję silną markę osobistą w branży.'
            };
            bio += goalTexts[data.goal] || '';
        }
        
        bio += skillsText + achievementsText + interestsText;
        
        bio += '\n\n📩 Napisz do mnie, jeśli chcesz porozmawiać o współpracy!';
        
        return bio;
    }

    generateTwitterBio(data) {
        const companyText = data.company ? ` @${data.company.replace(/\s+/g, '')}` : '';
        const emoji = this.getPersonalityEmoji(data.personality);
        
        let bio = `${emoji} ${data.title}${companyText}`;
        
        if (data.skills) {
            const topSkills = data.skills.split(',').slice(0, 2).map(s => s.trim()).join(' | ');
            bio += ` | ${topSkills}`;
        }
        
        if (data.interests) {
            const topInterests = data.interests.split(',').slice(0, 2).map(i => i.trim()).join(' & ');
            bio += ` 💭 ${topInterests}`;
        }
        
        return bio;
    }

    generateInstagramBio(data) {
        const emoji = this.getPersonalityEmoji(data.personality);
        const companyText = data.company ? `\n🏢 ${data.company}` : '';
        
        let bio = `${emoji} ${data.title}${companyText}`;
        
        if (data.skills) {
            const skills = data.skills.split(',').slice(0, 3);
            bio += `\n✨ ${skills.join(' • ')}`;
        }
        
        if (data.interests) {
            bio += `\n💡 ${data.interests}`;
        }
        
        bio += '\n📩 DM for business inquiries';
        
        return bio;
    }

    generateWebsiteBio(data) {
        const experienceText = this.getExperienceText(data.experience);
        const companyText = data.company ? ` w ${data.company}` : '';
        
        let bio = `Jestem ${data.title}${companyText} z ${experienceText} doświadczenia w branży ${data.industry || 'technologii'}. `;
        
        if (data.achievements) {
            bio += `W ciągu mojej kariery ${data.achievements.toLowerCase()} `;
        }
        
        if (data.skills) {
            bio += `Specjalizuję się w: ${data.skills}. `;
        }
        
        const goalTexts = {
            'networking': 'Pasjonuję się networkingiem i mentorингiem młodych profesjonalistów.',
            'job-seeking': 'Poszukuję nowych wyzwań zawodowych, gdzie mogę wykorzystać swoje doświadczenie.',
            'business': 'Pomagam firmom osiągać cele biznesowe przez innowacyjne rozwiązania i strategiczne myślenie.',
            'thought-leadership': 'Dzielę się swoją wiedzą i przemyśleniami na temat trendów branżowych.',
            'personal-brand': 'Aktywnie buduję swoją markę osobistą w branży.'
        };
        
        if (data.goal) {
            bio += goalTexts[data.goal] || '';
        }
        
        if (data.interests) {
            bio += ` Poza pracą interesuję się ${data.interests.toLowerCase()}.`;
        }
        
        bio += ' Skontaktuj się ze mną, jeśli chcesz porozmawiać o współpracy lub wymienić się doświadczeniami!';
        
        return bio;
    }

    getExperienceText(experience) {
        const expMap = {
            '1-3': 'kilka lat',
            '3-7': '5+ lat',
            '7-15': '10+ lat',
            '15+': 'ponad 15 lat'
        };
        return expMap[experience] || '5+ lat';
    }

    getPersonalityEmoji(personality) {
        const emojiMap = {
            professional: '💼',
            friendly: '👋',
            creative: '🎨',
            authoritative: '🎯'
        };
        return emojiMap[personality] || '💼';
    }

    displayBio(platform, bioText) {
        const resultElement = document.getElementById(`${platform}-bio-result`);
        const textArea = resultElement?.querySelector('.bio-text');
        const charCount = resultElement?.querySelector('.char-count');
        
        if (resultElement && textArea) {
            resultElement.style.display = 'block';
            textArea.value = bioText;
            
            // Update character count
            if (charCount) {
                const count = bioText.length;
                const limits = {
                    linkedin: 2600,
                    twitter: 160,
                    instagram: 150,
                    website: 'słów'
                };
                
                if (platform === 'website') {
                    const wordCount = bioText.split(/\s+/).length;
                    charCount.textContent = `${wordCount} słów`;
                } else {
                    const limit = limits[platform];
                    charCount.textContent = `${count}/${limit}`;
                    
                    // Color coding for character limits
                    if (count > limit * 0.9) {
                        charCount.style.color = 'var(--error-500)';
                    } else if (count > limit * 0.7) {
                        charCount.style.color = 'var(--warning-500)';
                    } else {
                        charCount.style.color = 'var(--success-500)';
                    }
                }
            }
        }
    }

    copyBioToClipboard(platform) {
        const textArea = document.querySelector(`#${platform}-bio-result .bio-text`);
        if (textArea) {
            navigator.clipboard.writeText(textArea.value).then(() => {
                this.showNotification(`Bio ${platform} skopiowane do schowka!`, 'success');
            }).catch(() => {
                this.showNotification('Błąd kopiowania', 'error');
            });
        }
    }

    copyAllBios() {
        const bios = [];
        this.selectedPlatforms.forEach(platform => {
            const textArea = document.querySelector(`#${platform}-bio-result .bio-text`);
            if (textArea && textArea.value) {
                bios.push(`=== ${platform.toUpperCase()} ===\n${textArea.value}\n`);
            }
        });
        
        if (bios.length > 0) {
            navigator.clipboard.writeText(bios.join('\n')).then(() => {
                this.showNotification('Wszystkie bio skopiowane do schowka!', 'success');
            }).catch(() => {
                this.showNotification('Błąd kopiowania', 'error');
            });
        }
    }

    exportBios() {
        const bios = {};
        this.selectedPlatforms.forEach(platform => {
            const textArea = document.querySelector(`#${platform}-bio-result .bio-text`);
            if (textArea && textArea.value) {
                bios[platform] = textArea.value;
            }
        });
        
        if (Object.keys(bios).length > 0) {
            const dataStr = JSON.stringify(bios, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'brandme-bios.json';
            link.click();
            
            URL.revokeObjectURL(url);
            this.showNotification('Bio zostały wyeksportowane!', 'success');
        }
    }

    // ===== CONTENT CALENDAR ===== //
    bindCalendarEvents() {
        // Month navigation
        document.getElementById('prev-month')?.addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.generateCalendar();
        });
        
        document.getElementById('next-month')?.addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.generateCalendar();
        });

        // Quick actions
        document.getElementById('add-content-idea')?.addEventListener('click', () => {
            this.openContentIdeaModal();
        });
        
        document.getElementById('generate-month-plan')?.addEventListener('click', () => {
            this.generateMonthPlan();
        });
        
        document.getElementById('import-holidays')?.addEventListener('click', () => {
            this.importHolidays();
        });
        
        document.getElementById('bulk-schedule')?.addEventListener('click', () => {
            this.openBulkScheduleModal();
        });
    }

    generateCalendar() {
        const monthNames = [
            'Styczeń', 'Luty', 'Marzec', 'Kwiecień', 'Maj', 'Czerwiec',
            'Lipiec', 'Sierpień', 'Wrzesień', 'Październik', 'Listopad', 'Grudzień'
        ];
        
        // Update month title
        const monthTitle = document.getElementById('current-month');
        if (monthTitle) {
            monthTitle.textContent = `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;
        }
        
        // Generate calendar grid
        const calendarGrid = document.getElementById('calendar-grid');
        if (!calendarGrid) return;
        
        calendarGrid.innerHTML = '';
        
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday
        
        for (let i = 0; i < 42; i++) { // 6 weeks
            const cellDate = new Date(startDate);
            cellDate.setDate(startDate.getDate() + i);
            
            const dayElement = this.createCalendarDay(cellDate, month);
            calendarGrid.appendChild(dayElement);
        }
    }

    createCalendarDay(date, currentMonth) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        
        if (date.getMonth() !== currentMonth) {
            dayElement.classList.add('other-month');
        }
        
        if (this.isToday(date)) {
            dayElement.classList.add('today');
        }
        
        const dayNumber = document.createElement('div');
        dayNumber.className = 'day-number';
        dayNumber.textContent = date.getDate();
        
        const dayContent = document.createElement('div');
        dayContent.className = 'day-content';
        
        // Add content items for this day
        const dateKey = this.formatDateKey(date);
        const dayItems = this.contentCalendar[dateKey] || [];
        
        dayItems.forEach(item => {
            const contentItem = document.createElement('div');
            contentItem.className = `content-item ${item.platform}`;
            contentItem.textContent = item.title;
            contentItem.title = item.description || item.title;
            dayContent.appendChild(contentItem);
        });
        
        dayElement.appendChild(dayNumber);
        dayElement.appendChild(dayContent);
        
        // Add click handler
        dayElement.addEventListener('click', () => {
            this.openDayDetails(date);
        });
        
        return dayElement;
    }

    isToday(date) {
        const today = new Date();
        return date.toDateString() === today.toDateString();
    }

    formatDateKey(date) {
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
    }

    openContentIdeaModal() {
        const modal = document.getElementById('contentIdeaModal');
        if (modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');
        }
    }

    generateMonthPlan() {
        // Generate AI-powered content plan for the month
        this.showNotification('Generuję plan na miesiąc...', 'info');
        
        setTimeout(() => {
            this.addSampleContentToCalendar();
            this.generateCalendar();
            this.showNotification('Plan na miesiąc został wygenerowany!', 'success');
        }, 2000);
    }

    addSampleContentToCalendar() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        // Add sample content ideas
        const sampleContent = [
            { day: 5, platform: 'linkedin', title: 'Industry Insight', description: 'Post o najnowszych trendach w branży' },
            { day: 8, platform: 'instagram', title: 'Behind the Scenes', description: 'Story z codziennej pracy' },
            { day: 12, platform: 'blog', title: 'Tutorial Article', description: 'Artykuł edukacyjny dla społeczności' },
            { day: 15, platform: 'video', title: 'Expert Interview', description: 'Video z ekspertem branżowym' },
            { day: 19, platform: 'linkedin', title: 'Success Story', description: 'Case study z ostatniego projektu' },
            { day: 22, platform: 'newsletter', title: 'Monthly Update', description: 'Miesięczny newsletter dla subskrybentów' },
            { day: 26, platform: 'instagram', title: 'Team Appreciation', description: 'Post doceniający zespół' },
            { day: 29, platform: 'linkedin', title: 'Thought Leadership', description: 'Opinia ekspercka na aktualny temat' }
        ];
        
        sampleContent.forEach(item => {
            const date = new Date(year, month, item.day);
            const dateKey = this.formatDateKey(date);
            
            if (!this.contentCalendar[dateKey]) {
                this.contentCalendar[dateKey] = [];
            }
            
            this.contentCalendar[dateKey].push({
                platform: item.platform,
                title: item.title,
                description: item.description,
                status: 'planned'
            });
        });
    }

    importHolidays() {
        this.showNotification('Importuję święta i dni ważne...', 'info');
        
        setTimeout(() => {
            // Add Polish holidays and important days
            const holidays = this.getPolishHolidays(this.currentDate.getFullYear());
            
            holidays.forEach(holiday => {
                const dateKey = this.formatDateKey(new Date(holiday.date));
                if (!this.contentCalendar[dateKey]) {
                    this.contentCalendar[dateKey] = [];
                }
                
                this.contentCalendar[dateKey].push({
                    platform: 'holiday',
                    title: holiday.name,
                    description: 'Okazja do tematycznego contentu',
                    status: 'holiday'
                });
            });
            
            this.generateCalendar();
            this.showNotification('Święta zostały zaimportowane!', 'success');
        }, 1000);
    }

    getPolishHolidays(year) {
        return [
            { date: `${year}-01-01`, name: 'Nowy Rok' },
            { date: `${year}-01-06`, name: 'Święto Trzech Króli' },
            { date: `${year}-05-01`, name: 'Święto Pracy' },
            { date: `${year}-05-03`, name: 'Święto Konstytucji 3 Maja' },
            { date: `${year}-08-15`, name: 'Wniebowzięcie NMP' },
            { date: `${year}-11-01`, name: 'Wszystkich Świętych' },
            { date: `${year}-11-11`, name: 'Święto Niepodległości' },
            { date: `${year}-12-25`, name: 'Boże Narodzenie' },
            { date: `${year}-12-26`, name: 'Drugi dzień Świąt' }
        ];
    }

    openDayDetails(date) {
        // Open modal with day details and ability to add/edit content
        const dateKey = this.formatDateKey(date);
        const dayItems = this.contentCalendar[dateKey] || [];
        
        console.log(`Opening details for ${dateKey}:`, dayItems);
        // Implement modal for day details
    }

    // ===== AI ASSISTANT ===== //
    bindAIAssistantEvents() {
        document.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const action = e.currentTarget.getAttribute('data-action');
                this.executeAIAction(action);
            });
        });
    }

    executeAIAction(action) {
        switch (action) {
            case 'analyze-profile':
                this.analyzeUserProfile();
                break;
            case 'generate-ideas':
                this.generateContentIdeas();
                break;
            case 'optimize-content':
                this.optimizeCurrentContent();
                break;
        }
    }

    analyzeUserProfile() {
        this.showNotification('AI analizuje Twój profil...', 'info');
        
        setTimeout(() => {
            const analysis = `
                📊 Analiza profilu:
                • Branża: Silna obecność w ${this.getUserIndustry() || 'technologii'}
                • Engagement: Średni poziom interakcji
                • Konsystencja: Regularne publikowanie
                
                💡 Rekomendacje:
                • Zwiększ częstotliwość postów o 30%
                • Dodaj więcej treści video
                • Wykorzystaj trending hashtagi
            `;
            
            this.showNotification('Analiza profilu zakończona!', 'success');
            console.log('Profile analysis:', analysis);
        }, 2000);
    }

    generateContentIdeas() {
        this.showNotification('AI generuje pomysły na treści...', 'info');
        
        setTimeout(() => {
            const ideas = [
                'Case study z ostatniego projektu',
                'Przewodnik dla początkujących w Twojej branży', 
                'Behind the scenes z codziennej pracy',
                'Opinia o najnowszych trendach branżowych',
                'Tips & tricks dla profesjonalistów'
            ];
            
            console.log('Generated ideas:', ideas);
            this.showNotification('Wygenerowano 5 pomysłów na treści!', 'success');
        }, 1500);
    }

    optimizeCurrentContent() {
        if (this.generatedContent.currentPost) {
            this.showNotification('AI optymalizuje treść...', 'info');
            
            setTimeout(() => {
                this.updateOptimizationTips(this.generatedContent.currentPost);
                this.showNotification('Treść została zoptymalizowana!', 'success');
            }, 1000);
        } else {
            this.showNotification('Najpierw wygeneruj treść do optymalizacji', 'error');
        }
    }

    // ===== GLOBAL ACTIONS ===== //
    bindGlobalActions() {
        document.getElementById('exportContent')?.addEventListener('click', () => {
            this.exportAllContent();
        });
        
        document.getElementById('publishContent')?.addEventListener('click', () => {
            this.publishToSocialMedia();
        });
    }

    exportAllContent() {
        const allContent = {
            generatedPosts: this.generatedContent,
            contentCalendar: this.contentCalendar,
            savedPosts: JSON.parse(localStorage.getItem('brandme_saved_posts') || '[]'),
            exportDate: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(allContent, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `brandme-content-export-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('Zawartość została wyeksportowana!', 'success');
    }

    publishToSocialMedia() {
        this.showNotification('Funkcja publikacji będzie dostępna wkrótce!', 'info');
    }

    // ===== RANGE INPUTS ===== //
    bindRangeInputs() {
        document.querySelectorAll('input[type="range"]').forEach(range => {
            const updateValue = () => {
                const valueDisplay = range.parentElement.querySelector('.range-value');
                if (valueDisplay) {
                    valueDisplay.textContent = range.value;
                }
            };
            
            range.addEventListener('input', updateValue);
            updateValue(); // Initialize
        });
    }

    // ===== UTILITY METHODS ===== //
    loadUserData() {
        // Load user data from localStorage or API
        const userData = JSON.parse(localStorage.getItem('brandme_user_data') || '{}');
        this.userData = userData;
    }

    getUserIndustry() {
        return this.userData?.industry || localStorage.getItem('brandme_user_industry') || 'technology';
    }

    getUserExperience() {
        return this.userData?.experience || localStorage.getItem('brandme_user_experience') || '5';
    }

    updateCharacterCount(text) {
        // Update character count displays if they exist
        const charCountElements = document.querySelectorAll('.char-count');
        charCountElements.forEach(element => {
            if (element.textContent.includes('/')) {
                const parts = element.textContent.split('/');
                if (parts.length === 2) {
                    element.textContent = `${text.length}/${parts[1]}`;
                }
            }
        });
    }

    calculateEstimatedReach(postData) {
        // Simple calculation based on post characteristics
        const baseReach = 1000;
        let multiplier = 1;
        
        if (postData.includeEmojis) multiplier += 0.2;
        if (postData.hashtagsCount > 3) multiplier += 0.3;
        if (postData.postLength === 'medium') multiplier += 0.1;
        if (postData.postLength === 'long') multiplier += 0.2;
        
        return Math.round(baseReach * multiplier);
    }

    calculateEngagementScore(postData) {
        // Calculate engagement score based on content analysis
        let score = 5.0;
        
        if (postData.tone === 'inspirational') score += 1.5;
        if (postData.callToAction !== 'none') score += 1.0;
        if (postData.includeEmojis) score += 0.5;
        
        return Math.min(score, 10.0);
    }

    async simulateAIProcessing(duration = 1500) {
        return new Promise(resolve => setTimeout(resolve, duration));
    }

    showNotification(message, type = 'info') {
        // Reuse notification system from main.js
        if (window.BrandMePlatform && window.brandMePlatform) {
            window.brandMePlatform.showNotification(message, type);
        } else {
            // Fallback
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize Content Suite
const contentSuite = new ContentSuite();

// Global function to close modals
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.classList.remove('modal-open');
    }
}

// Export for potential external use
window.ContentSuite = ContentSuite;