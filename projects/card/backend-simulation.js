// SmartCard.pl - Backend API Simulation
// This simulates backend endpoints for development/demo purposes

class BackendSimulation {
    constructor() {
        this.users = new Map();
        this.subscriptions = new Map();
        this.analytics = new Map();
        this.init();
    }

    init() {
        // Intercept fetch requests to our API endpoints
        this.interceptFetch();
        
        // Load existing data from localStorage
        this.loadData();
        
        console.log('🔧 Backend simulation initialized');
    }

    interceptFetch() {
        const originalFetch = window.fetch;
        
        window.fetch = async (url, options = {}) => {
            // Check if this is one of our API endpoints
            if (url.startsWith('/api/')) {
                return this.handleAPIRequest(url, options);
            }
            
            // Otherwise, use original fetch
            return originalFetch(url, options);
        };
    }

    async handleAPIRequest(url, options) {
        const method = options.method || 'GET';
        const body = options.body ? JSON.parse(options.body) : null;
        
        console.log(`🔄 API Request: ${method} ${url}`, body);
        
        // Simulate network delay
        await this.delay(500 + Math.random() * 1000);
        
        try {
            let response;
            
            switch (url) {
                case '/api/create-checkout-session':
                    response = await this.createCheckoutSession(body);
                    break;
                    
                case '/api/webhook/stripe':
                    response = await this.handleStripeWebhook(body);
                    break;
                    
                case '/api/user/subscription':
                    response = await this.getUserSubscription(body);
                    break;
                    
                case '/api/analytics/track':
                    response = await this.trackAnalytics(body);
                    break;
                    
                case '/api/cards':
                    if (method === 'POST') {
                        response = await this.createCard(body);
                    } else {
                        response = await this.getCards(body);
                    }
                    break;
                    
                default:
                    throw new Error(`Unknown endpoint: ${url}`);
            }
            
            return new Response(JSON.stringify(response), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            });
            
        } catch (error) {
            console.error('API Error:', error);
            return new Response(JSON.stringify({ error: error.message }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }
    }

    async createCheckoutSession(data) {
        const { priceId, userId, successUrl, cancelUrl } = data;
        
        // Simulate Stripe session creation
        const sessionId = 'cs_test_' + Math.random().toString(36).substr(2, 9);
        
        // Store session data for later verification
        const sessionData = {
            id: sessionId,
            priceId,
            userId,
            status: 'open',
            createdAt: new Date().toISOString()
        };
        
        localStorage.setItem(`stripe_session_${sessionId}`, JSON.stringify(sessionData));
        
        // In real implementation, this would redirect to Stripe
        // For demo, we'll simulate immediate success
        setTimeout(() => {
            this.simulateSuccessfulPayment(sessionId, userId, priceId);
        }, 3000);
        
        return {
            id: sessionId,
            url: `https://checkout.stripe.com/pay/${sessionId}` // This would be real Stripe URL
        };
    }

    simulateSuccessfulPayment(sessionId, userId, priceId) {
        // Simulate successful payment
        const planMapping = {
            'price_pro_monthly': 'pro',
            'price_business_monthly': 'business'
        };
        
        const plan = planMapping[priceId] || 'pro';
        
        // Update user subscription
        const subscription = {
            id: 'sub_' + Math.random().toString(36).substr(2, 9),
            status: 'active',
            plan: plan,
            currentPeriodStart: new Date().toISOString(),
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            stripeSessionId: sessionId
        };
        
        this.subscriptions.set(userId, subscription);
        this.saveData();
        
        // Trigger success event
        window.dispatchEvent(new CustomEvent('paymentSuccess', {
            detail: { sessionId, subscription }
        }));
        
        console.log('💳 Payment simulation completed:', subscription);
    }

    async getUserSubscription(data) {
        const { userId } = data;
        const subscription = this.subscriptions.get(userId);
        
        return {
            subscription: subscription || null
        };
    }

    async trackAnalytics(data) {
        const { userId, event, properties } = data;
        
        if (!this.analytics.has(userId)) {
            this.analytics.set(userId, []);
        }
        
        const analyticsData = this.analytics.get(userId);
        analyticsData.push({
            event,
            properties,
            timestamp: new Date().toISOString()
        });
        
        this.analytics.set(userId, analyticsData);
        this.saveData();
        
        return { success: true };
    }

    async createCard(data) {
        const { userId, cardData } = data;
        
        const card = {
            id: 'card_' + Math.random().toString(36).substr(2, 9),
            ...cardData,
            userId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        // Store card data
        const userCards = JSON.parse(localStorage.getItem(`cards_${userId}`) || '[]');
        userCards.push(card);
        localStorage.setItem(`cards_${userId}`, JSON.stringify(userCards));
        
        return { card };
    }

    async getCards(data) {
        const { userId } = data;
        const cards = JSON.parse(localStorage.getItem(`cards_${userId}`) || '[]');
        
        return { cards };
    }

    loadData() {
        // Load subscriptions
        const subscriptionsData = localStorage.getItem('backend_subscriptions');
        if (subscriptionsData) {
            const parsed = JSON.parse(subscriptionsData);
            this.subscriptions = new Map(parsed);
        }
        
        // Load analytics
        const analyticsData = localStorage.getItem('backend_analytics');
        if (analyticsData) {
            const parsed = JSON.parse(analyticsData);
            this.analytics = new Map(parsed);
        }
    }

    saveData() {
        // Save subscriptions
        localStorage.setItem('backend_subscriptions', 
            JSON.stringify(Array.from(this.subscriptions.entries())));
        
        // Save analytics
        localStorage.setItem('backend_analytics', 
            JSON.stringify(Array.from(this.analytics.entries())));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Utility method to check subscription status
    isSubscriptionActive(userId) {
        const subscription = this.subscriptions.get(userId);
        if (!subscription) return false;
        
        const now = new Date();
        const endDate = new Date(subscription.currentPeriodEnd);
        
        return subscription.status === 'active' && endDate > now;
    }

    // Get user's current plan
    getUserPlan(userId) {
        const subscription = this.subscriptions.get(userId);
        if (!subscription || !this.isSubscriptionActive(userId)) {
            return 'free';
        }
        return subscription.plan;
    }
}

// Initialize backend simulation
const backendSimulation = new BackendSimulation();

// Listen for payment success events
window.addEventListener('paymentSuccess', (event) => {
    const { sessionId, subscription } = event.detail;
    
    // Update payment system
    if (window.paymentSystem) {
        window.paymentSystem.handleSuccessfulPayment(sessionId);
    }
});

// Export for global access
window.backendSimulation = backendSimulation;

console.log('🚀 SmartCard.pl Backend Simulation loaded!');
