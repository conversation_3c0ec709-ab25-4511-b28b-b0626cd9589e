<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoVerse DAO - Decentralized Future</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00d4ff',
                        secondary: '#ff6b35',
                        accent: '#8b5cf6'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;900&display=swap');
        body { font-family: 'Orbitron', monospace; }
        .gradient-bg { 
            background: linear-gradient(135deg, #000000 0%, #1a0033 25%, #000066 50%, #1a0033 75%, #000000 100%);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
        }
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .glass-effect { 
            background: rgba(0, 212, 255, 0.05); 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(0, 212, 255, 0.2);
        }
        .neon-glow { 
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5), 0 0 40px rgba(0, 212, 255, 0.3);
        }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .animate-pulse-slow { animation: pulse 3s ease-in-out infinite; }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }
        .text-glow { text-shadow: 0 0 10px currentColor; }
        .crypto-grid {
            background-image: 
                linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }
    </style>
</head>
<body class="gradient-bg text-white overflow-x-hidden">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-black/40 backdrop-blur-md z-50 border-b border-primary/20">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-lg neon-glow flex items-center justify-center">
                        <span class="text-xl font-bold">Ξ</span>
                    </div>
                    <span class="text-2xl font-bold text-glow">CryptoVerse DAO</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#tokenomics" class="text-white/80 hover:text-primary transition-colors">Tokenomics</a>
                    <a href="#roadmap" class="text-white/80 hover:text-primary transition-colors">Roadmap</a>
                    <a href="#community" class="text-white/80 hover:text-primary transition-colors">Community</a>
                    <a href="#whitepaper" class="text-white/80 hover:text-primary transition-colors">Whitepaper</a>
                </div>
                <button class="bg-gradient-to-r from-primary to-accent text-black px-6 py-2 rounded-lg font-bold hover:neon-glow transition-all">
                    Connect Wallet
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-16 px-4 min-h-screen flex items-center relative crypto-grid">
        <div class="container mx-auto relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="inline-flex items-center px-4 py-2 bg-primary/20 rounded-full text-primary text-sm font-bold mb-6 neon-glow">
                        🚀 PRESALE LIVE - 40% BONUS
                    </div>
                    <h1 class="text-5xl md:text-7xl font-black mb-6 text-glow">
                        ENTER THE
                        <span class="bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
                            CRYPTOVERSE
                        </span>
                    </h1>
                    <p class="text-xl text-white/80 mb-8 leading-relaxed">
                        Join the most advanced decentralized autonomous organization. 
                        Govern the future of DeFi, NFTs, and Web3 infrastructure through democratic consensus.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <button class="bg-gradient-to-r from-primary to-accent text-black px-8 py-4 rounded-lg font-bold text-lg neon-glow hover:scale-105 transition-transform">
                            BUY $CRYPTO NOW
                        </button>
                        <button class="border-2 border-primary text-primary px-8 py-4 rounded-lg font-bold text-lg hover:bg-primary/10 transition-colors">
                            READ WHITEPAPER
                        </button>
                    </div>
                    <div class="grid grid-cols-3 gap-6 text-center">
                        <div>
                            <div class="text-2xl font-bold text-primary">$50M+</div>
                            <div class="text-sm text-white/60">Total Value Locked</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-accent">25K+</div>
                            <div class="text-sm text-white/60">DAO Members</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-secondary">99.9%</div>
                            <div class="text-sm text-white/60">Uptime</div>
                        </div>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="animate-float">
                        <div class="glass-effect rounded-2xl p-8 neon-glow">
                            <div class="text-center mb-6">
                                <div class="text-6xl mb-4 animate-pulse-slow">⬢</div>
                                <h3 class="text-xl font-bold text-primary mb-2">CRYPTO Token</h3>
                                <div class="text-3xl font-bold">$0.045</div>
                                <div class="text-green-400 text-sm">+24.5% (24h)</div>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-white/60">Market Cap</span>
                                    <span class="text-white font-bold">$125M</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-white/60">Circulating Supply</span>
                                    <span class="text-white font-bold">2.8B CRYPTO</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-white/60">Holders</span>
                                    <span class="text-white font-bold">45,231</span>
                                </div>
                            </div>
                            
                            <button class="w-full bg-gradient-to-r from-primary to-accent text-black py-3 rounded-lg font-bold mt-6 hover:neon-glow transition-all">
                                TRADE NOW
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-4 h-4 bg-primary rounded-full animate-pulse opacity-60"></div>
        <div class="absolute top-40 right-20 w-6 h-6 bg-accent rounded-full animate-pulse opacity-40"></div>
        <div class="absolute bottom-20 left-1/4 w-3 h-3 bg-secondary rounded-full animate-pulse opacity-50"></div>
    </section>

    <!-- Tokenomics Section -->
    <section id="tokenomics" class="py-16 px-4">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-glow">TOKENOMICS</h2>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    Carefully designed token distribution for sustainable growth and community governance
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="glass-effect rounded-xl p-6 text-center neon-glow">
                    <div class="text-4xl mb-4">🏛️</div>
                    <h3 class="text-xl font-bold mb-2 text-primary">DAO Treasury</h3>
                    <div class="text-3xl font-bold mb-2">30%</div>
                    <p class="text-white/60 text-sm">Reserved for governance and ecosystem development</p>
                </div>

                <div class="glass-effect rounded-xl p-6 text-center neon-glow">
                    <div class="text-4xl mb-4">🚀</div>
                    <h3 class="text-xl font-bold mb-2 text-accent">Public Sale</h3>
                    <div class="text-3xl font-bold mb-2">25%</div>
                    <p class="text-white/60 text-sm">Available for community purchase and distribution</p>
                </div>

                <div class="glass-effect rounded-xl p-6 text-center neon-glow">
                    <div class="text-4xl mb-4">💎</div>
                    <h3 class="text-xl font-bold mb-2 text-secondary">Staking Rewards</h3>
                    <div class="text-3xl font-bold mb-2">20%</div>
                    <p class="text-white/60 text-sm">Incentivizing long-term holders and validators</p>
                </div>

                <div class="glass-effect rounded-xl p-6 text-center neon-glow">
                    <div class="text-4xl mb-4">👥</div>
                    <h3 class="text-xl font-bold mb-2 text-primary">Team & Advisors</h3>
                    <div class="text-3xl font-bold mb-2">15%</div>
                    <p class="text-white/60 text-sm">Vested over 4 years with 1-year cliff</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Roadmap Section -->
    <section id="roadmap" class="py-16 px-4 bg-black/20">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-glow">ROADMAP</h2>
                <p class="text-xl text-white/80">Our journey to decentralized dominance</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-black font-bold">✓</span>
                        </div>
                        <h3 class="text-lg font-bold">Q1 2024</h3>
                    </div>
                    <ul class="space-y-2 text-sm text-white/80">
                        <li>• Token Launch</li>
                        <li>• DEX Listing</li>
                        <li>• Community Building</li>
                        <li>• Initial Governance</li>
                    </ul>
                </div>

                <div class="glass-effect rounded-xl p-6 neon-glow border-primary">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-3 animate-pulse">
                            <span class="text-black font-bold">⚡</span>
                        </div>
                        <h3 class="text-lg font-bold text-primary">Q2 2024</h3>
                    </div>
                    <ul class="space-y-2 text-sm text-white/80">
                        <li>• NFT Marketplace</li>
                        <li>• Staking Platform</li>
                        <li>• Mobile App Beta</li>
                        <li>• CEX Listings</li>
                    </ul>
                </div>

                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold">3</span>
                        </div>
                        <h3 class="text-lg font-bold">Q3 2024</h3>
                    </div>
                    <ul class="space-y-2 text-sm text-white/80">
                        <li>• DeFi Integration</li>
                        <li>• Cross-chain Bridge</li>
                        <li>• DAO Governance v2</li>
                        <li>• Metaverse Land</li>
                    </ul>
                </div>

                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold">4</span>
                        </div>
                        <h3 class="text-lg font-bold">Q4 2024</h3>
                    </div>
                    <ul class="space-y-2 text-sm text-white/80">
                        <li>• Layer 2 Solution</li>
                        <li>• Enterprise Partnerships</li>
                        <li>• Global Expansion</li>
                        <li>• Ecosystem Fund</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Community Section -->
    <section id="community" class="py-16 px-4">
        <div class="container mx-auto text-center">
            <h2 class="text-4xl md:text-5xl font-bold mb-6 text-glow">JOIN THE REVOLUTION</h2>
            <p class="text-xl text-white/80 mb-12 max-w-2xl mx-auto">
                Connect with thousands of crypto enthusiasts, developers, and visionaries building the decentralized future.
            </p>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="text-3xl mb-2">💬</div>
                    <div class="text-2xl font-bold text-primary">Discord</div>
                    <div class="text-white/60">25K+ Members</div>
                </div>
                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="text-3xl mb-2">🐦</div>
                    <div class="text-2xl font-bold text-primary">Twitter</div>
                    <div class="text-white/60">50K+ Followers</div>
                </div>
                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="text-3xl mb-2">📱</div>
                    <div class="text-2xl font-bold text-primary">Telegram</div>
                    <div class="text-white/60">30K+ Members</div>
                </div>
                <div class="glass-effect rounded-xl p-6 neon-glow">
                    <div class="text-3xl mb-2">📺</div>
                    <div class="text-2xl font-bold text-primary">YouTube</div>
                    <div class="text-white/60">15K+ Subscribers</div>
                </div>
            </div>

            <div class="max-w-md mx-auto">
                <h3 class="text-2xl font-bold mb-4">Get Exclusive Updates</h3>
                <form class="flex gap-4">
                    <input
                        type="email"
                        placeholder="Enter your email"
                        class="flex-1 px-4 py-3 bg-white/10 border border-primary/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-primary"
                    />
                    <button
                        type="submit"
                        class="bg-gradient-to-r from-primary to-accent text-black px-6 py-3 rounded-lg font-bold hover:neon-glow transition-all"
                    >
                        Subscribe
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-8 px-4 border-t border-primary/20">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-lg neon-glow flex items-center justify-center">
                        <span class="text-lg font-bold">Ξ</span>
                    </div>
                    <span class="text-xl font-bold">CryptoVerse DAO</span>
                </div>
                <div class="text-white/60 text-sm">
                    © 2024 CryptoVerse DAO. Decentralized and unstoppable.
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Wallet connection simulation
        document.querySelector('button').addEventListener('click', function() {
            if (this.textContent === 'Connect Wallet') {
                this.textContent = 'Wallet Connected';
                this.classList.add('bg-green-500');
            }
        });

        // Newsletter subscription
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thanks for subscribing to CryptoVerse updates!');
        });
    </script>
</body>
</html>
