/**
 * FitGenius.pl - Mobile Optimization
 * Enterprise-grade mobile experience with touch interactions
 */

/* ===================================
   Mobile-First Touch Optimizations
   =================================== */

/* Touch target sizes - minimum 44px for accessibility */
@media (max-width: 768px) {
    .btn-primary,
    .btn-secondary,
    .nav-link,
    .feature-cta,
    .plan-cta,
    .goal-option,
    .time-option,
    .equipment-option,
    .level-option {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
    }
    
    /* Touch-friendly spacing */
    .nav-menu {
        gap: var(--spacing-lg);
    }
    
    .hero-actions {
        gap: var(--spacing-lg);
    }
    
    .form-navigation {
        gap: var(--spacing-lg);
    }
}

/* ===================================
   Mobile Navigation Improvements
   =================================== */

@media (max-width: 768px) {
    .navbar {
        padding: var(--spacing-sm) 0;
    }
    
    .nav-container {
        position: relative;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--background-main);
        backdrop-filter: blur(20px);
        border-top: 1px solid var(--glass-border);
        flex-direction: column;
        align-items: center;
        padding: var(--spacing-xl) var(--spacing-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: 1000;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-item {
        width: 100%;
        text-align: center;
        margin-bottom: var(--spacing-md);
    }
    
    .nav-link {
        display: block;
        width: 100%;
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-lg);
    }
    
    .nav-actions {
        flex-direction: column;
        width: 100%;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--glass-border);
    }
    
    .nav-actions .btn-primary,
    .nav-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .hamburger {
        display: flex;
        z-index: 1001;
        transition: all var(--transition-normal);
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* ===================================
   Hero Section Mobile Optimization
   =================================== */

@media (max-width: 768px) {
    .hero {
        padding: calc(80px + var(--spacing-2xl)) 0 var(--spacing-2xl);
        text-align: center;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .hero-visual {
        order: -1;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
    }
    
    .hero-description {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-xl);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }
    
    .stat-item {
        background: var(--glass-background);
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
        border: 1px solid var(--glass-border);
        backdrop-filter: blur(10px);
    }
    
    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .hero-actions .btn-primary,
    .hero-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .trust-badges {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .badge {
        width: 100%;
        text-align: center;
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* ===================================
   Mobile Phone Mockup Optimization
   =================================== */

@media (max-width: 768px) {
    .phone-mockup {
        width: 250px;
        height: 500px;
        padding: 15px;
        transform: none;
        animation: none;
    }
    
    .app-preview {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    .app-header {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .quick-stats {
        gap: var(--spacing-sm);
    }
    
    .stat-card {
        padding: var(--spacing-sm);
    }
    
    .stat-icon {
        font-size: var(--font-size-base);
    }
    
    .stat-value {
        font-size: var(--font-size-base);
    }
}

/* ===================================
   Features Grid Mobile Layout
   =================================== */

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .feature-card {
        padding: var(--spacing-xl);
    }
    
    .feature-icon {
        font-size: var(--font-size-3xl);
        margin-bottom: var(--spacing-md);
    }
    
    .feature-title {
        font-size: var(--font-size-lg);
    }
    
    .feature-cta {
        margin-top: var(--spacing-lg);
        font-size: var(--font-size-base);
        padding: var(--spacing-md);
    }
}

/* ===================================
   Workout Generator Mobile UX
   =================================== */

@media (max-width: 768px) {
    .generator-container {
        padding: var(--spacing-lg);
        margin: 0 var(--spacing-sm);
    }
    
    .form-step h3 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-lg);
    }
    
    .goal-options,
    .equipment-options,
    .level-options {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .goal-option,
    .equipment-option,
    .level-option {
        padding: var(--spacing-lg);
        text-align: center;
    }
    
    .goal-icon,
    .equipment-icon,
    .level-icon {
        font-size: var(--font-size-2xl);
    }
    
    .time-options {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .time-option {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
    
    .form-navigation {
        flex-direction: column-reverse;
        gap: var(--spacing-md);
    }
    
    .form-navigation .btn-primary,
    .form-navigation .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .workout-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .workout-duration,
    .workout-difficulty,
    .workout-calories {
        text-align: center;
    }
    
    .exercise-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .exercise-icon {
        font-size: var(--font-size-xl);
    }
    
    .workout-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .workout-actions .btn-primary,
    .workout-actions .btn-secondary {
        width: 100%;
    }
}

/* ===================================
   Analytics Dashboard Mobile
   =================================== */

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .dashboard-card {
        padding: var(--spacing-lg);
    }
    
    .dashboard-card h3 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .chart-stats {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .calorie-ring {
        width: 120px;
        height: 120px;
    }
    
    .ring-number {
        font-size: var(--font-size-xl);
    }
    
    .calendar-grid {
        gap: 2px;
    }
    
    .calendar-day {
        font-size: var(--font-size-xs);
        min-height: 30px;
    }
    
    .composition-chart {
        margin: 0 auto;
        max-width: 250px;
    }
}

/* ===================================
   Pricing Mobile Optimization
   =================================== */

@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .pricing-card {
        padding: var(--spacing-xl);
    }
    
    .pricing-card.popular {
        transform: none;
        order: -1;
    }
    
    .pricing-card.popular:hover {
        transform: translateY(-5px);
    }
    
    .plan-price {
        margin-bottom: var(--spacing-lg);
    }
    
    .price-amount {
        font-size: var(--font-size-3xl);
    }
    
    .plan-features {
        margin-bottom: var(--spacing-xl);
    }
    
    .plan-features li {
        padding: var(--spacing-sm) 0;
        font-size: var(--font-size-sm);
    }
    
    .guarantee-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
}

/* ===================================
   Testimonials Mobile Layout
   =================================== */

@media (max-width: 768px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .testimonial-card {
        padding: var(--spacing-xl);
    }
    
    .testimonial-author {
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .author-details {
        order: 2;
        flex: none;
    }
    
    .testimonial-results {
        order: 3;
        margin-left: 0;
    }
}

/* ===================================
   Contact Form Mobile UX
   =================================== */

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .contact-info {
        order: 2;
    }
    
    .contact-form {
        order: 1;
        padding: var(--spacing-xl);
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .contact-icon {
        font-size: var(--font-size-xl);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: var(--spacing-md);
    }
    
    .form-group textarea {
        min-height: 120px;
    }
}

/* ===================================
   Footer Mobile Layout
   =================================== */

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .footer-section {
        align-items: center;
    }
    
    .footer-logo {
        margin-bottom: var(--spacing-lg);
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-links {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-md);
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .footer-badges {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* ===================================
   Modal and Popup Mobile UX
   =================================== */

@media (max-width: 768px) {
    .feature-modal .modal-content,
    .workout-tracker-modal .tracker-content,
    .share-modal .share-content,
    .ai-coach-modal .coach-content,
    .detailed-coach-modal .coach-summary {
        width: 95%;
        max-width: none;
        padding: var(--spacing-xl);
        margin: var(--spacing-md);
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .achievement-notification,
    .level-up-notification,
    .challenge-completion-notification {
        max-width: 90%;
        left: 50%;
        right: auto;
        transform: translateX(-50%) translateY(100%);
    }
    
    .achievement-notification.show,
    .level-up-notification.show {
        transform: translateX(-50%) translateY(0);
    }
    
    .xp-notification,
    .daily-reward-notification,
    .coaching-message {
        max-width: 90%;
        left: 50%;
        right: auto;
        transform: translateX(-50%) translateY(100%);
        font-size: var(--font-size-sm);
        padding: var(--spacing-md);
    }
    
    .xp-notification.show,
    .daily-reward-notification.show,
    .coaching-message.show {
        transform: translateX(-50%) translateY(0);
    }
}

/* ===================================
   Touch Gestures and Interactions
   =================================== */

@media (max-width: 768px) {
    /* Swipe indicators */
    .swipeable {
        position: relative;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .swipeable::-webkit-scrollbar {
        display: none;
    }
    
    .swipeable::after {
        content: '← Przesuń →';
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        opacity: 0.8;
        pointer-events: none;
    }
    
    /* Pull to refresh indicator */
    .pull-to-refresh {
        position: relative;
        overflow-y: auto;
    }
    
    .pull-to-refresh::before {
        content: '↓ Pociągnij aby odświeżyć';
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%) translateY(-100%);
        background: var(--glass-background);
        backdrop-filter: blur(10px);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-xs);
        transition: transform var(--transition-normal);
        z-index: 1000;
    }
    
    .pull-to-refresh.pulling::before {
        transform: translateX(-50%) translateY(0);
    }
}

/* ===================================
   Mobile-Specific Animations
   =================================== */

@media (max-width: 768px) {
    /* Reduce motion for performance */
    .feature-card,
    .testimonial-card,
    .pricing-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .feature-card:active,
    .testimonial-card:active,
    .pricing-card:active {
        transform: scale(0.98);
    }
    
    /* Touch feedback for buttons */
    .btn-primary:active,
    .btn-secondary:active,
    .feature-cta:active,
    .plan-cta:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    /* Simplified hover effects for touch devices */
    @media (hover: none) {
        .feature-card:hover,
        .pricing-card:hover,
        .testimonial-card:hover {
            transform: none;
            box-shadow: var(--shadow-md);
        }
        
        .btn-primary:hover,
        .btn-secondary:hover {
            transform: none;
        }
    }
}

/* ===================================
   iOS Safari Specific Fixes
   =================================== */

@supports (-webkit-touch-callout: none) {
    /* iOS specific styles */
    input[type="text"],
    input[type="email"],
    textarea,
    select {
        -webkit-appearance: none;
        border-radius: var(--radius-lg);
    }
    
    /* Fix for iOS Safari 100vh issue */
    .full-height {
        height: 100vh;
        height: -webkit-fill-available;
    }
    
    /* Prevent zoom on form inputs */
    input[type="text"],
    input[type="email"],
    input[type="number"],
    textarea {
        font-size: 16px;
    }
}

/* ===================================
   Android Chrome Specific Fixes
   =================================== */

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    /* Android Chrome specific styles */
    .glass-effect {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
}

/* ===================================
   High DPI Display Optimization
   =================================== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* High DPI optimizations */
    .logo-icon,
    .feature-icon,
    .stat-icon {
        transform: translateZ(0);
        -webkit-font-smoothing: antialiased;
    }
    
    /* Sharper borders for high DPI */
    .glass-effect {
        border: 0.5px solid var(--glass-border);
    }
}

/* ===================================
   Landscape Mode Optimizations
   =================================== */

@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: calc(60px + var(--spacing-xl)) 0 var(--spacing-xl);
    }
    
    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
        align-items: center;
    }
    
    .hero-visual {
        order: 1;
    }
    
    .hero-content {
        order: 0;
    }
    
    .phone-mockup {
        width: 200px;
        height: 400px;
    }
    
    .modal-content,
    .coach-content,
    .coach-summary {
        max-height: 85vh;
        overflow-y: auto;
    }
}

/* ===================================
   Accessibility Improvements Mobile
   =================================== */

@media (max-width: 768px) {
    /* Larger tap targets for better accessibility */
    .nav-link,
    .btn-primary,
    .btn-secondary,
    .feature-cta,
    .plan-cta {
        min-height: 48px;
        min-width: 48px;
    }
    
    /* Better focus indicators for mobile */
    .nav-link:focus,
    .btn-primary:focus,
    .btn-secondary:focus,
    .feature-cta:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    /* Improved readability */
    .hero-title,
    .section-title {
        line-height: 1.3;
    }
    
    .hero-description,
    .section-description,
    .feature-description {
        line-height: 1.6;
    }
}

/* ===================================
   Performance Optimizations Mobile
   =================================== */

@media (max-width: 768px) {
    /* Reduce complexity for mobile performance */
    .phone-mockup {
        animation: none;
        transform: none;
    }
    
    .hero::before {
        animation-duration: 12s;
    }
    
    body::before {
        animation-duration: 30s;
    }
    
    /* Simplify shadows for performance */
    .glass-effect {
        box-shadow: var(--shadow-md);
    }
    
    .btn-primary,
    .btn-secondary {
        box-shadow: var(--shadow-sm);
    }
    
    /* Optimize backdrop filters */
    .glass-effect {
        backdrop-filter: blur(10px);
    }
}

/* ===================================
   Dark Mode Mobile Adjustments
   =================================== */

@media (max-width: 768px) and (prefers-color-scheme: dark) {
    .navbar {
        background: rgba(15, 23, 42, 0.95);
    }
    
    .nav-menu {
        background: var(--background-main);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .modal-content,
    .coach-content {
        background: rgba(30, 41, 59, 0.95);
    }
}

/* ===================================
   Print Styles for Mobile
   =================================== */

@media print {
    .navbar,
    .hamburger,
    .nav-actions,
    .hero-visual,
    .phone-mockup,
    .floating-particle,
    .achievement-notification,
    .coaching-message {
        display: none !important;
    }
    
    .hero-container,
    .features-grid,
    .pricing-grid,
    .testimonials-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg) !important;
    }
    
    .section-title,
    .hero-title {
        color: black !important;
        background: none !important;
        -webkit-text-fill-color: black !important;
    }
}