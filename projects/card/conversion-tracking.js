// SmartCard.pl - Conversion Tracking & Analytics
class ConversionTracking {
    constructor() {
        this.events = [];
        this.sessionId = this.generateSessionId();
        this.userId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.trackPageView();
        this.setupHeatmapTracking();
        this.startSessionTracking();
        console.log('📊 Conversion Tracking initialized');
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    setupEventListeners() {
        // Track button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-upgrade-plan]')) {
                this.trackEvent('upgrade_button_clicked', {
                    plan: e.target.getAttribute('data-upgrade-plan'),
                    location: this.getElementLocation(e.target)
                });
            }

            if (e.target.matches('.pricing-btn')) {
                this.trackEvent('pricing_button_clicked', {
                    plan: this.getPlanFromButton(e.target),
                    location: this.getElementLocation(e.target)
                });
            }

            if (e.target.id === 'startCreating') {
                this.trackEvent('start_creating_clicked', {
                    location: this.getElementLocation(e.target)
                });
            }

            if (e.target.matches('.nav-cta')) {
                this.trackEvent('nav_cta_clicked', {
                    location: this.getElementLocation(e.target)
                });
            }
        });

        // Track form interactions
        document.addEventListener('input', (e) => {
            if (e.target.matches('#cardName, #cardEmail, #cardPhone')) {
                this.trackEvent('form_field_filled', {
                    field: e.target.id,
                    hasValue: e.target.value.length > 0
                });
            }
        });

        // Track scroll depth
        this.setupScrollTracking();

        // Track time on page
        this.setupTimeTracking();

        // Track user registration
        window.addEventListener('userRegistered', (e) => {
            this.userId = e.detail.user.id;
            this.trackEvent('user_registered', {
                userId: this.userId,
                email: e.detail.user.email
            });
        });

        // Track payment events
        window.addEventListener('paymentSuccess', (e) => {
            this.trackEvent('payment_completed', {
                plan: e.detail.subscription.plan,
                amount: this.getPlanPrice(e.detail.subscription.plan)
            });
        });
    }

    trackPageView() {
        this.trackEvent('page_view', {
            url: window.location.href,
            referrer: document.referrer,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        });
    }

    trackEvent(eventName, properties = {}) {
        const event = {
            id: this.generateEventId(),
            name: eventName,
            properties: {
                ...properties,
                sessionId: this.sessionId,
                userId: this.userId,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                }
            }
        };

        this.events.push(event);
        this.saveToStorage();
        this.sendToAnalytics(event);

        console.log('📊 Event tracked:', eventName, properties);
    }

    generateEventId() {
        return 'evt_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
    }

    setupScrollTracking() {
        let maxScroll = 0;
        const milestones = [25, 50, 75, 90, 100];
        const tracked = new Set();

        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
            );

            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
            }

            milestones.forEach(milestone => {
                if (scrollPercent >= milestone && !tracked.has(milestone)) {
                    tracked.add(milestone);
                    this.trackEvent('scroll_depth', {
                        percentage: milestone
                    });
                }
            });
        });
    }

    setupTimeTracking() {
        const startTime = Date.now();
        const intervals = [30, 60, 120, 300]; // seconds
        const tracked = new Set();

        setInterval(() => {
            const timeSpent = Math.floor((Date.now() - startTime) / 1000);
            
            intervals.forEach(interval => {
                if (timeSpent >= interval && !tracked.has(interval)) {
                    tracked.add(interval);
                    this.trackEvent('time_on_page', {
                        seconds: interval
                    });
                }
            });
        }, 10000); // Check every 10 seconds
    }

    setupHeatmapTracking() {
        // Track clicks for heatmap
        document.addEventListener('click', (e) => {
            this.trackEvent('click_heatmap', {
                x: e.clientX,
                y: e.clientY,
                element: e.target.tagName,
                className: e.target.className,
                id: e.target.id
            });
        });

        // Track mouse movement (sampled)
        let lastMouseTrack = 0;
        document.addEventListener('mousemove', (e) => {
            const now = Date.now();
            if (now - lastMouseTrack > 5000) { // Sample every 5 seconds
                lastMouseTrack = now;
                this.trackEvent('mouse_movement', {
                    x: e.clientX,
                    y: e.clientY
                });
            }
        });
    }

    startSessionTracking() {
        // Track session start
        this.trackEvent('session_start', {
            sessionId: this.sessionId
        });

        // Track session end on page unload
        window.addEventListener('beforeunload', () => {
            this.trackEvent('session_end', {
                sessionId: this.sessionId,
                duration: Date.now() - this.sessionStartTime
            });
        });

        this.sessionStartTime = Date.now();
    }

    getElementLocation(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
        };
    }

    getPlanFromButton(button) {
        const card = button.closest('.pricing-card');
        if (card) {
            const titleElement = card.querySelector('h3');
            return titleElement ? titleElement.textContent.toLowerCase() : 'unknown';
        }
        return 'unknown';
    }

    getPlanPrice(plan) {
        const prices = {
            'free': 0,
            'pro': 29,
            'business': 99
        };
        return prices[plan] || 0;
    }

    async sendToAnalytics(event) {
        // In production, send to Google Analytics, Mixpanel, etc.
        try {
            // Google Analytics 4 example
            if (typeof gtag !== 'undefined') {
                gtag('event', event.name, {
                    custom_parameter_1: JSON.stringify(event.properties),
                    value: event.properties.amount || 0
                });
            }

            // Send to our backend
            if (window.backendSimulation) {
                await fetch('/api/analytics/track', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: this.userId,
                        event: event.name,
                        properties: event.properties
                    })
                });
            }
        } catch (error) {
            console.error('Analytics error:', error);
        }
    }

    saveToStorage() {
        try {
            localStorage.setItem('conversion_events', JSON.stringify(this.events));
        } catch (error) {
            console.error('Storage error:', error);
        }
    }

    loadFromStorage() {
        try {
            const stored = localStorage.getItem('conversion_events');
            if (stored) {
                this.events = JSON.parse(stored);
            }
        } catch (error) {
            console.error('Storage load error:', error);
        }
    }

    // Funnel analysis
    getFunnelData() {
        const funnel = {
            page_view: 0,
            start_creating_clicked: 0,
            form_field_filled: 0,
            user_registered: 0,
            upgrade_button_clicked: 0,
            payment_completed: 0
        };

        this.events.forEach(event => {
            if (funnel.hasOwnProperty(event.name)) {
                funnel[event.name]++;
            }
        });

        return funnel;
    }

    // Conversion rate calculation
    getConversionRates() {
        const funnel = this.getFunnelData();
        const rates = {};

        const steps = Object.keys(funnel);
        for (let i = 1; i < steps.length; i++) {
            const currentStep = steps[i];
            const previousStep = steps[i - 1];
            
            if (funnel[previousStep] > 0) {
                rates[currentStep] = (funnel[currentStep] / funnel[previousStep] * 100).toFixed(2);
            }
        }

        return rates;
    }

    // A/B testing support
    getVariant(testName, variants) {
        const userId = this.userId || this.sessionId;
        const hash = this.hashCode(userId + testName);
        const variantIndex = Math.abs(hash) % variants.length;
        
        this.trackEvent('ab_test_variant', {
            testName,
            variant: variants[variantIndex]
        });
        
        return variants[variantIndex];
    }

    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash;
    }

    // Export data for analysis
    exportData() {
        return {
            events: this.events,
            funnel: this.getFunnelData(),
            conversionRates: this.getConversionRates(),
            sessionId: this.sessionId,
            userId: this.userId
        };
    }
}

// Initialize conversion tracking
const conversionTracking = new ConversionTracking();
window.conversionTracking = conversionTracking;

// Export for analytics dashboard
window.getAnalyticsData = () => conversionTracking.exportData();
