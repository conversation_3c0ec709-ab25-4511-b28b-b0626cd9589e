/**
 * FitGenius.pl - Gamification & Social Features
 * Advanced achievement system, challenges, and community features
 */

const GamificationEngine = {
    // User progression system
    userLevel: {
        currentLevel: 1,
        currentXP: 0,
        totalXP: 0,
        xpToNextLevel: 100,
        levelMultiplier: 1.5
    },
    
    // Achievement definitions
    achievements: {
        // Workout achievements
        firstWorkout: {
            id: 'first_workout',
            name: '<PERSON><PERSON><PERSON>',
            description: 'Ukończ swój pierwszy trening',
            icon: '🎯',
            xp: 50,
            type: 'workout',
            condition: (stats) => stats.workoutsCompleted >= 1,
            rarity: 'common'
        },
        weekWarrior: {
            id: 'week_warrior',
            name: 'Tygodniowy Wojownik',
            description: '7 dni treningu z rzędu',
            icon: '🔥',
            xp: 200,
            type: 'streak',
            condition: (stats) => stats.currentStreak >= 7,
            rarity: 'rare'
        },
        centurion: {
            id: 'centurion',
            name: 'Centurion',
            description: '100 ukończonych treningów',
            icon: '💯',
            xp: 1000,
            type: 'workout',
            condition: (stats) => stats.workoutsCompleted >= 100,
            rarity: 'epic'
        },
        strengthBeast: {
            id: 'strength_beast',
            name: 'Bestia Siły',
            description: 'Zwiększ siłę o 50% w którymś ćwiczeniu',
            icon: '💪',
            xp: 500,
            type: 'progress',
            condition: (stats) => stats.strengthIncrease >= 50,
            rarity: 'rare'
        },
        marathoner: {
            id: 'marathoner',
            name: 'Maratończyk',
            description: 'Spał łącznie 10,000 kalorii',
            icon: '🏃‍♂️',
            xp: 800,
            type: 'calories',
            condition: (stats) => stats.totalCaloriesBurned >= 10000,
            rarity: 'epic'
        },
        earlyBird: {
            id: 'early_bird',
            name: 'Ranny Ptaszek',
            description: 'Trenuj przed 7:00 przez 5 dni',
            icon: '🌅',
            xp: 300,
            type: 'time',
            condition: (stats) => stats.earlyWorkouts >= 5,
            rarity: 'uncommon'
        },
        perfectWeek: {
            id: 'perfect_week',
            name: 'Perfekcyjny Tydzień',
            description: 'Zrealizuj wszystkie zaplanowane treningi przez tydzień',
            icon: '✨',
            xp: 400,
            type: 'consistency',
            condition: (stats) => stats.weeklyCompletionRate >= 100,
            rarity: 'rare'
        },
        socialButterfly: {
            id: 'social_butterfly',
            name: 'Towarzyski Motyl',
            description: 'Udostępnij 10 treningów',
            icon: '🦋',
            xp: 250,
            type: 'social',
            condition: (stats) => stats.workoutsShared >= 10,
            rarity: 'uncommon'
        }
    },
    
    // Challenge system
    challenges: {
        active: [],
        completed: [],
        available: {
            weeklyBurn: {
                id: 'weekly_burn',
                name: 'Tygodniowy Płomień',
                description: 'Spał 2000 kalorii w tym tygodniu',
                icon: '🔥',
                duration: 7, // days
                target: 2000,
                type: 'calories',
                reward: { xp: 300, badge: 'fire_badge' },
                difficulty: 'medium'
            },
            monthlyMiles: {
                id: 'monthly_miles',
                name: 'Miesięczne Kilometry',
                description: 'Przebiegaj łącznie 50km w tym miesiącu',
                icon: '🏃',
                duration: 30,
                target: 50,
                type: 'distance',
                reward: { xp: 600, badge: 'runner_badge' },
                difficulty: 'hard'
            },
            strengthStorm: {
                id: 'strength_storm',
                name: 'Burza Siły',
                description: 'Wykonaj 15 treningów siłowych w tym miesiącu',
                icon: '⚡',
                duration: 30,
                target: 15,
                type: 'workouts',
                reward: { xp: 500, badge: 'strength_badge' },
                difficulty: 'medium'
            },
            consistencyKing: {
                id: 'consistency_king',
                name: 'Król Konsystencji',
                description: 'Trenuj codziennie przez 2 tygodnie',
                icon: '👑',
                duration: 14,
                target: 14,
                type: 'streak',
                reward: { xp: 800, badge: 'consistency_badge' },
                difficulty: 'hard'
            }
        }
    },
    
    // Social features
    social: {
        leaderboards: {
            weekly: [],
            monthly: [],
            allTime: []
        },
        friends: [],
        workoutPartners: [],
        communityStats: {
            totalUsers: 50000,
            totalWorkouts: 1000000,
            totalCaloriesBurned: 50000000,
            averageStreak: 12
        }
    },
    
    // Motivational system
    motivation: {
        dailyQuotes: [
            "💪 'Nie ma windy do sukcesu - musisz wziąć schody.'",
            "🔥 'Jedyną niemożliwą rzeczą jest ta, której nie próbujesz.'",
            "🏆 'Twoje ciało może to znieść. To twój umysł musisz przekonać.'",
            "⚡ 'Nie szukaj wymówek, szukaj wyników.'",
            "🎯 'Każdy ekspert był kiedyś początkującym.'",
            "💯 'Twój największy rywał to Ty z wczoraj.'",
            "🌟 'Sukces to suma małych wysiłków powtarzanych dzień po dniu.'"
        ],
        encouragementMessages: [
            "Świetnie! Kolejny trening za Tobą! 🎉",
            "Imponujące! Twoja konsystencja jest inspirująca! ✨",
            "Brawo! Każdy trening to krok bliżej celu! 🚀",
            "Fantastycznie! Widzę Twój postęp! 📈",
            "Niesamowite! Utrzymuj ten rytm! 🔥"
        ],
        milestoneMessages: {
            5: "🎯 5 treningów za Tobą! Dobry początek!",
            10: "🔥 10 treningów! Momentum rośnie!",
            25: "💪 25 treningów! Jesteś na dobrej drodze!",
            50: "⚡ 50 treningów! Połowa setki za Tobą!",
            100: "🏆 100 treningów! Jesteś prawdziwym wojownikiem!"
        }
    },
    
    // Initialize gamification system
    init() {
        this.loadUserProgress();
        this.setupEventListeners();
        this.initializeChallenges();
        this.updateProgressDisplay();
        this.setupDailyRewards();
        this.initializeSocialFeatures();
        console.log('🎮 Gamification Engine initialized');
    },
    
    setupEventListeners() {
        // Listen for workout completion
        document.addEventListener('workoutCompleted', (e) => {
            this.handleWorkoutCompletion(e.detail);
        });
        
        // Listen for achievement unlocks
        document.addEventListener('achievementUnlocked', (e) => {
            this.displayAchievementNotification(e.detail);
        });
        
        // Challenge acceptance
        document.querySelectorAll('.accept-challenge').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const challengeId = e.target.dataset.challengeId;
                this.acceptChallenge(challengeId);
            });
        });
        
        // Leaderboard refresh
        const leaderboardBtn = document.querySelector('.refresh-leaderboard');
        if (leaderboardBtn) {
            leaderboardBtn.addEventListener('click', () => this.refreshLeaderboard());
        }
    },
    
    loadUserProgress() {
        const savedProgress = FitGenius.utils.loadFromLocalStorage('user_gamification');
        if (savedProgress) {
            this.userLevel = { ...this.userLevel, ...savedProgress.userLevel };
            this.challenges.active = savedProgress.activeChallenges || [];
            this.challenges.completed = savedProgress.completedChallenges || [];
        }
    },
    
    saveUserProgress() {
        const progressData = {
            userLevel: this.userLevel,
            activeChallenges: this.challenges.active,
            completedChallenges: this.challenges.completed,
            lastSaved: new Date().toISOString()
        };
        FitGenius.utils.saveToLocalStorage('user_gamification', progressData);
    },
    
    // Experience and leveling system
    addXP(amount, source = 'general') {
        this.userLevel.currentXP += amount;
        this.userLevel.totalXP += amount;
        
        // Check for level up
        if (this.userLevel.currentXP >= this.userLevel.xpToNextLevel) {
            this.levelUp();
        }
        
        // Show XP notification
        this.showXPNotification(amount, source);
        
        // Update displays
        this.updateProgressDisplay();
        this.saveUserProgress();
        
        // Track XP gain
        FitGenius.analytics.trackEvent('xp_gained', {
            amount: amount,
            source: source,
            newTotal: this.userLevel.totalXP,
            currentLevel: this.userLevel.currentLevel
        });
    },
    
    levelUp() {
        const oldLevel = this.userLevel.currentLevel;
        this.userLevel.currentLevel++;
        this.userLevel.currentXP -= this.userLevel.xpToNextLevel;
        this.userLevel.xpToNextLevel = Math.floor(this.userLevel.xpToNextLevel * this.userLevel.levelMultiplier);
        
        // Show level up animation
        this.showLevelUpNotification(oldLevel, this.userLevel.currentLevel);
        
        // Award level up bonus
        this.awardLevelUpBonus(this.userLevel.currentLevel);
        
        FitGenius.analytics.trackEvent('level_up', {
            oldLevel: oldLevel,
            newLevel: this.userLevel.currentLevel,
            totalXP: this.userLevel.totalXP
        });
    },
    
    showXPNotification(amount, source) {
        const notification = document.createElement('div');
        notification.className = 'xp-notification';
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
            z-index: 10002;
            transform: translateX(100%) scale(0.8);
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.2rem;">⭐</span>
                <span>+${amount} XP</span>
                <span style="font-size: 0.8rem; opacity: 0.8;">(${source})</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0) scale(1)';
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%) scale(0.8)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 400);
        }, 3000);
    },
    
    showLevelUpNotification(oldLevel, newLevel) {
        const notification = document.createElement('div');
        notification.className = 'level-up-notification';
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            z-index: 10003;
            box-shadow: 0 16px 64px rgba(99, 102, 241, 0.4);
            transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        `;
        
        notification.innerHTML = `
            <div style="font-size: 3rem; margin-bottom: 1rem; animation: bounce 1s infinite;">🎉</div>
            <h2 style="margin-bottom: 0.5rem; font-size: 2rem;">LEVEL UP!</h2>
            <p style="margin-bottom: 1rem; font-size: 1.2rem;">Poziom ${oldLevel} → ${newLevel}</p>
            <p style="opacity: 0.9;">Odblokowałeś nowe funkcje!</p>
            <button onclick="this.parentElement.remove()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border: none; border-radius: 0.5rem; color: white; cursor: pointer;">Kontynuuj</button>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 100);
        
        // Auto remove after 10 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.remove();
            }
        }, 10000);
    },
    
    awardLevelUpBonus(level) {
        // Award bonus based on level
        const bonuses = {
            5: { type: 'feature', name: 'Zaawansowane analizy' },
            10: { type: 'feature', name: 'Spersonalizowane plany żywieniowe' },
            15: { type: 'feature', name: 'Coaching AI Premium' },
            20: { type: 'badge', name: 'Elite Member' }
        };
        
        if (bonuses[level]) {
            this.unlockBonus(bonuses[level]);
        }
    },
    
    // Achievement system
    checkAchievements() {
        const userStats = this.calculateUserStats();
        const newAchievements = [];
        
        Object.values(this.achievements).forEach(achievement => {
            if (!this.hasAchievement(achievement.id) && achievement.condition(userStats)) {
                newAchievements.push(achievement);
                this.unlockAchievement(achievement);
            }
        });
        
        return newAchievements;
    },
    
    hasAchievement(achievementId) {
        const unlockedAchievements = FitGenius.utils.loadFromLocalStorage('user_achievements') || [];
        return unlockedAchievements.some(a => a.id === achievementId);
    },
    
    unlockAchievement(achievement) {
        const unlockedAchievements = FitGenius.utils.loadFromLocalStorage('user_achievements') || [];
        
        const unlockedAchievement = {
            ...achievement,
            unlockedAt: new Date().toISOString(),
            progress: 100
        };
        
        unlockedAchievements.push(unlockedAchievement);
        FitGenius.utils.saveToLocalStorage('user_achievements', unlockedAchievements);
        
        // Award XP
        this.addXP(achievement.xp, 'achievement');
        
        // Show achievement notification
        this.displayAchievementNotification(achievement);
        
        // Custom event
        document.dispatchEvent(new CustomEvent('achievementUnlocked', { 
            detail: achievement 
        }));
        
        FitGenius.analytics.trackEvent('achievement_unlocked', {
            achievementId: achievement.id,
            xpAwarded: achievement.xp,
            rarity: achievement.rarity
        });
    },
    
    calculateUserStats() {
        // Calculate user statistics for achievement checking
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        const analyticsData = FitGenius.utils.loadFromLocalStorage('user_analytics_data') || {};
        
        return {
            workoutsCompleted: workoutHistory.length,
            currentStreak: this.calculateCurrentStreak(),
            totalCaloriesBurned: workoutHistory.reduce((sum, w) => sum + (w.calories || 0), 0),
            strengthIncrease: this.calculateStrengthIncrease(),
            earlyWorkouts: workoutHistory.filter(w => 
                new Date(w.date).getHours() < 7
            ).length,
            weeklyCompletionRate: this.calculateWeeklyCompletionRate(),
            workoutsShared: analyticsData.workoutsShared || 0
        };
    },
    
    calculateCurrentStreak() {
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        if (workoutHistory.length === 0) return 0;
        
        const sortedWorkouts = workoutHistory.sort((a, b) => new Date(b.date) - new Date(a.date));
        let streak = 0;
        let currentDate = new Date();
        
        for (const workout of sortedWorkouts) {
            const workoutDate = new Date(workout.date);
            const daysDiff = Math.floor((currentDate - workoutDate) / (1000 * 60 * 60 * 24));
            
            if (daysDiff === streak) {
                streak++;
            } else if (daysDiff > streak + 1) {
                break;
            }
        }
        
        return streak;
    },
    
    calculateStrengthIncrease() {
        // Mock calculation - in real app this would use actual strength data
        return Math.floor(Math.random() * 100);
    },
    
    calculateWeeklyCompletionRate() {
        // Mock calculation
        return Math.floor(Math.random() * 100);
    },
    
    displayAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 1.5rem;
            border-radius: 1rem;
            box-shadow: 0 16px 64px rgba(16, 185, 129, 0.3);
            z-index: 10001;
            transform: translateX(100%);
            transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            max-width: 300px;
            min-width: 280px;
        `;
        
        notification.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">${achievement.icon}</div>
                <div style="font-size: 1.2rem; font-weight: bold; margin-bottom: 0.5rem;">Osiągnięcie Odblokowane!</div>
                <div style="font-size: 1rem; margin-bottom: 0.25rem;">${achievement.name}</div>
                <div style="font-size: 0.875rem; opacity: 0.9; margin-bottom: 0.5rem;">${achievement.description}</div>
                <div style="font-size: 0.875rem; background: rgba(255,255,255,0.2); padding: 0.25rem 0.5rem; border-radius: 1rem; display: inline-block;">
                    +${achievement.xp} XP
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 500);
        }, 6000);
    },
    
    // Challenge system
    initializeChallenges() {
        this.updateActiveChallenges();
        this.generateWeeklyChallenges();
    },
    
    updateActiveChallenges() {
        // Check progress on active challenges
        this.challenges.active.forEach(challenge => {
            const progress = this.calculateChallengeProgress(challenge);
            challenge.progress = progress;
            
            if (progress >= challenge.target) {
                this.completeChallenge(challenge);
            }
        });
        
        // Remove expired challenges
        this.challenges.active = this.challenges.active.filter(challenge => {
            const expiryDate = new Date(challenge.startDate);
            expiryDate.setDate(expiryDate.getDate() + challenge.duration);
            return new Date() < expiryDate;
        });
    },
    
    acceptChallenge(challengeId) {
        const challenge = this.challenges.available[challengeId];
        if (!challenge) return;
        
        const activeChallenge = {
            ...challenge,
            startDate: new Date().toISOString(),
            progress: 0,
            accepted: true
        };
        
        this.challenges.active.push(activeChallenge);
        this.saveUserProgress();
        
        FitGenius.forms.showNotification(`Wyzwanie "${challenge.name}" przyjęte!`, 'success');
        
        FitGenius.analytics.trackEvent('challenge_accepted', {
            challengeId: challengeId,
            difficulty: challenge.difficulty
        });
    },
    
    calculateChallengeProgress(challenge) {
        const userStats = this.calculateUserStats();
        
        switch (challenge.type) {
            case 'calories':
                // Calculate calories burned since challenge start
                return this.getCaloriesSince(challenge.startDate);
            case 'workouts':
                return this.getWorkoutsSince(challenge.startDate);
            case 'streak':
                return Math.min(userStats.currentStreak, challenge.target);
            default:
                return 0;
        }
    },
    
    getCaloriesSince(startDate) {
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        return workoutHistory
            .filter(w => new Date(w.date) >= new Date(startDate))
            .reduce((sum, w) => sum + (w.calories || 0), 0);
    },
    
    getWorkoutsSince(startDate) {
        const workoutHistory = FitGenius.utils.loadFromLocalStorage('workout_history') || [];
        return workoutHistory.filter(w => new Date(w.date) >= new Date(startDate)).length;
    },
    
    completeChallenge(challenge) {
        // Move to completed challenges
        this.challenges.completed.push({
            ...challenge,
            completedAt: new Date().toISOString()
        });
        
        // Remove from active
        this.challenges.active = this.challenges.active.filter(c => c.id !== challenge.id);
        
        // Award rewards
        this.addXP(challenge.reward.xp, 'challenge');
        
        // Show completion notification
        this.showChallengeCompletionNotification(challenge);
        
        this.saveUserProgress();
        
        FitGenius.analytics.trackEvent('challenge_completed', {
            challengeId: challenge.id,
            duration: challenge.duration,
            xpAwarded: challenge.reward.xp
        });
    },
    
    showChallengeCompletionNotification(challenge) {
        const notification = document.createElement('div');
        notification.className = 'challenge-completion-notification';
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            z-index: 10003;
            box-shadow: 0 16px 64px rgba(245, 158, 11, 0.4);
            transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        `;
        
        notification.innerHTML = `
            <div style="font-size: 3rem; margin-bottom: 1rem;">${challenge.icon}</div>
            <h2 style="margin-bottom: 0.5rem;">Wyzwanie Ukończone!</h2>
            <p style="margin-bottom: 1rem; font-size: 1.2rem;">${challenge.name}</p>
            <p style="opacity: 0.9; margin-bottom: 1rem;">${challenge.description}</p>
            <div style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 0.5rem; display: inline-block;">
                Nagroda: +${challenge.reward.xp} XP
            </div>
            <button onclick="this.parentElement.remove()" style="display: block; margin: 1rem auto 0; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border: none; border-radius: 0.5rem; color: white; cursor: pointer;">Zbierz nagrodę</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 100);
    },
    
    generateWeeklyChallenges() {
        // Generate dynamic weekly challenges based on user progress
        const userStats = this.calculateUserStats();
        const weeklyGoals = [];
        
        // Calorie challenge
        const calorieTarget = Math.max(1500, userStats.totalCaloriesBurned / 4);
        weeklyGoals.push({
            id: `weekly_calories_${Date.now()}`,
            name: 'Tygodniowy Spalacz',
            description: `Spał ${calorieTarget} kalorii w tym tygodniu`,
            icon: '🔥',
            duration: 7,
            target: calorieTarget,
            type: 'calories',
            reward: { xp: 200 }
        });
        
        // Workout frequency challenge
        const workoutTarget = Math.min(7, Math.max(3, Math.floor(userStats.workoutsCompleted / 10)));
        weeklyGoals.push({
            id: `weekly_workouts_${Date.now()}`,
            name: 'Tygodniowy Rytm',
            description: `Wykonaj ${workoutTarget} treningów w tym tygodniu`,
            icon: '💪',
            duration: 7,
            target: workoutTarget,
            type: 'workouts',
            reward: { xp: 150 }
        });
        
        return weeklyGoals;
    },
    
    // Social features
    initializeSocialFeatures() {
        this.updateLeaderboards();
        this.generateCommunityStats();
        this.setupSocialSharing();
    },
    
    updateLeaderboards() {
        // Mock leaderboard data
        this.social.leaderboards.weekly = [
            { rank: 1, name: 'Anna K.', points: 1250, avatar: '👩‍💼' },
            { rank: 2, name: 'Michał N.', points: 1180, avatar: '👨‍💻' },
            { rank: 3, name: 'Kasia W.', points: 1050, avatar: '👩‍👧‍👦' },
            { rank: 4, name: 'Tomasz Z.', points: 980, avatar: '🏋️‍♂️' },
            { rank: 5, name: 'TY', points: this.userLevel.totalXP, avatar: '🌟' }
        ];
        
        this.displayLeaderboard();
    },
    
    displayLeaderboard() {
        const leaderboardContainer = document.querySelector('.leaderboard-container');
        if (!leaderboardContainer) return;
        
        const leaderboardHTML = this.social.leaderboards.weekly.map(entry => `
            <div class="leaderboard-entry ${entry.name === 'TY' ? 'user-entry' : ''}" style="
                display: flex;
                align-items: center;
                padding: 1rem;
                margin-bottom: 0.5rem;
                background: var(--glass-background);
                border-radius: 0.75rem;
                border: 1px solid var(--glass-border);
                ${entry.name === 'TY' ? 'border-color: var(--primary-color);' : ''}
            ">
                <div class="rank" style="font-size: 1.2rem; font-weight: bold; margin-right: 1rem; color: var(--primary-color);">
                    ${entry.rank}
                </div>
                <div class="avatar" style="font-size: 2rem; margin-right: 1rem;">
                    ${entry.avatar}
                </div>
                <div class="user-info" style="flex: 1;">
                    <div class="name" style="font-weight: 600; color: var(--text-primary);">
                        ${entry.name}
                    </div>
                    <div class="points" style="font-size: 0.875rem; color: var(--text-secondary);">
                        ${entry.points} XP
                    </div>
                </div>
                ${entry.rank <= 3 ? `<div class="medal" style="font-size: 1.5rem;">${entry.rank === 1 ? '🥇' : entry.rank === 2 ? '🥈' : '🥉'}</div>` : ''}
            </div>
        `).join('');
        
        leaderboardContainer.innerHTML = leaderboardHTML;
    },
    
    generateCommunityStats() {
        // Update community statistics
        this.social.communityStats.totalUsers += Math.floor(Math.random() * 100);
        this.social.communityStats.totalWorkouts += Math.floor(Math.random() * 1000);
        this.social.communityStats.totalCaloriesBurned += Math.floor(Math.random() * 10000);
    },
    
    setupSocialSharing() {
        // Enhanced sharing functionality
        document.addEventListener('shareWorkout', (e) => {
            this.shareWorkout(e.detail);
        });
        
        document.addEventListener('shareAchievement', (e) => {
            this.shareAchievement(e.detail);
        });
    },
    
    shareWorkout(workoutData) {
        const shareText = `🏋️ Właśnie ukończyłem świetny trening z FitGenius.pl!\n\n` +
            `💪 ${workoutData.exercises.length} ćwiczeń\n` +
            `⏱️ ${workoutData.time} minut\n` +
            `🔥 ${workoutData.calories} kalorii\n\n` +
            `Dołącz do mnie i osiągnij swoje cele fitness! 🎯`;
        
        this.handleSocialShare(shareText, 'workout');
    },
    
    shareAchievement(achievement) {
        const shareText = `🏆 Właśnie odblokowałem osiągnięcie "${achievement.name}" w FitGenius.pl!\n\n` +
            `${achievement.icon} ${achievement.description}\n\n` +
            `Dołącz do społeczności i osiągnij swoje cele! 💪`;
        
        this.handleSocialShare(shareText, 'achievement');
    },
    
    handleSocialShare(text, type) {
        if (navigator.share) {
            navigator.share({
                title: 'FitGenius.pl - Moje osiągnięcie',
                text: text,
                url: window.location.href
            });
        } else {
            navigator.clipboard.writeText(text).then(() => {
                FitGenius.forms.showNotification('Skopiowano do schowka!', 'success');
            });
        }
        
        // Track sharing
        FitGenius.analytics.trackEvent('content_shared', {
            type: type,
            method: navigator.share ? 'native' : 'clipboard'
        });
        
        // Award XP for sharing
        this.addXP(25, 'social_sharing');
    },
    
    // Daily rewards and motivation
    setupDailyRewards() {
        this.checkDailyReward();
        this.displayDailyQuote();
    },
    
    checkDailyReward() {
        const lastRewardDate = FitGenius.utils.loadFromLocalStorage('last_daily_reward');
        const today = new Date().toDateString();
        
        if (lastRewardDate !== today) {
            this.awardDailyReward();
            FitGenius.utils.saveToLocalStorage('last_daily_reward', today);
        }
    },
    
    awardDailyReward() {
        const rewards = [
            { type: 'xp', amount: 50, message: 'Dzienny bonus XP!' },
            { type: 'motivation', message: 'Specjalna wiadomość motywacyjna!' },
            { type: 'tip', message: 'Dzienna porada treningowa!' }
        ];
        
        const reward = rewards[Math.floor(Math.random() * rewards.length)];
        
        if (reward.type === 'xp') {
            this.addXP(reward.amount, 'daily_reward');
        }
        
        this.showDailyRewardNotification(reward);
    },
    
    showDailyRewardNotification(reward) {
        const notification = document.createElement('div');
        notification.className = 'daily-reward-notification';
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 1rem;
            z-index: 10002;
            transform: translateX(100%);
            transition: transform 0.4s ease;
            box-shadow: 0 8px 32px rgba(6, 182, 212, 0.3);
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.75rem;">
                <span style="font-size: 1.5rem;">🎁</span>
                <div>
                    <div style="font-weight: bold; margin-bottom: 0.25rem;">Dzienna Nagroda!</div>
                    <div style="font-size: 0.875rem; opacity: 0.9;">${reward.message}</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 400);
        }, 4000);
    },
    
    displayDailyQuote() {
        const quotes = this.motivation.dailyQuotes;
        const dailyQuote = quotes[new Date().getDate() % quotes.length];
        
        const quoteElement = document.querySelector('.daily-quote');
        if (quoteElement) {
            quoteElement.textContent = dailyQuote;
        }
    },
    
    // Progress display updates
    updateProgressDisplay() {
        // Update level progress bar
        const levelProgressBar = document.querySelector('.level-progress-bar');
        if (levelProgressBar) {
            const progress = (this.userLevel.currentXP / this.userLevel.xpToNextLevel) * 100;
            levelProgressBar.style.width = `${progress}%`;
        }
        
        // Update level display
        const levelDisplay = document.querySelector('.current-level');
        if (levelDisplay) {
            levelDisplay.textContent = this.userLevel.currentLevel;
        }
        
        // Update XP display
        const xpDisplay = document.querySelector('.current-xp');
        if (xpDisplay) {
            xpDisplay.textContent = `${this.userLevel.currentXP}/${this.userLevel.xpToNextLevel} XP`;
        }
    },
    
    // Handle workout completion
    handleWorkoutCompletion(workoutData) {
        // Award XP based on workout
        const baseXP = 100;
        const timeBonus = Math.floor(workoutData.time / 10) * 5;
        const difficultyBonus = workoutData.difficulty === 'hard' ? 50 : workoutData.difficulty === 'medium' ? 25 : 0;
        
        const totalXP = baseXP + timeBonus + difficultyBonus;
        this.addXP(totalXP, 'workout_completion');
        
        // Check for new achievements
        this.checkAchievements();
        
        // Update challenge progress
        this.updateActiveChallenges();
        
        // Show encouragement message
        this.showEncouragementMessage();
    },
    
    showEncouragementMessage() {
        const messages = this.motivation.encouragementMessages;
        const message = messages[Math.floor(Math.random() * messages.length)];
        
        setTimeout(() => {
            FitGenius.forms.showNotification(message, 'success');
        }, 1000);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    GamificationEngine.init();
    
    // Simulate workout completion for demo
    setTimeout(() => {
        const demoWorkout = {
            time: 45,
            calories: 320,
            difficulty: 'medium',
            exercises: 6
        };
        
        GamificationEngine.handleWorkoutCompletion(demoWorkout);
    }, 5000);
});

// Export for global access
window.GamificationEngine = GamificationEngine;