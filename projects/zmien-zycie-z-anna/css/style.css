/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-purple: #7C3AED;
    --secondary-purple: #A855F7;
    --light-purple: #E9D5FF;
    --gold: #F59E0B;
    --light-gold: #FEF3C7;
    --dark-gray: #1F2937;
    --medium-gray: #6B7280;
    --light-gray: #F9FAFB;
    --white: #FFFFFF;
    --success: #10B981;
    --error: #EF4444;
    
    --font-primary: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --gradient-primary: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 100%);
    --gradient-gold: linear-gradient(135deg, var(--gold) 0%, #FBBF24 100%);
    --gradient-light: linear-gradient(135deg, var(--light-purple) 0%, var(--light-gold) 100%);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 1.875rem;
}

h4 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: var(--medium-gray);
}

.highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-nav {
    display: inline-block;
    padding: 14px 28px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    text-align: center;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-purple);
    border: 2px solid var(--primary-purple);
}

.btn-secondary:hover {
    background: var(--primary-purple);
    color: var(--white);
}

.btn-nav {
    background: var(--gradient-gold);
    color: var(--white);
    padding: 10px 20px;
    font-size: 0.875rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(124, 58, 237, 0.1);
    z-index: 1000;
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h2 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    font-size: 1.5rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-purple);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-purple);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    margin-top: 80px;
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-badge {
    display: inline-block;
    background: var(--gradient-gold);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.hero h1 {
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: var(--medium-gray);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-purple);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--medium-gray);
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
}

.hero-img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
}

.hero-testimonial {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

.hero-testimonial p {
    font-style: italic;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.hero-testimonial span {
    font-size: 0.875rem;
    color: var(--primary-purple);
    font-weight: 600;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Quiz Section */
.quiz-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.quiz-container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
}

.quiz-progress {
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light-purple);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 33.33%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--medium-gray);
}

.question {
    display: none;
}

.question.active {
    display: block;
}

.question h3 {
    margin-bottom: 2rem;
    color: var(--dark-gray);
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quiz-options label {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid var(--light-purple);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quiz-options label:hover {
    border-color: var(--primary-purple);
    background: var(--light-purple);
}

.quiz-options input[type="radio"] {
    margin-right: 1rem;
    accent-color: var(--primary-purple);
}

.quiz-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.quiz-results {
    text-align: center;
}

.result-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin: 2rem 0;
}

.result-score {
    text-align: center;
}

.score-number {
    display: block;
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-purple);
}

.score-label {
    color: var(--medium-gray);
}

.result-text {
    flex: 1;
    text-align: left;
}

.result-message {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-gray);
}

/* Program Section */
.program-section {
    padding: 80px 0;
}

.program-timeline {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
}

.program-timeline::before {
    content: '';
    position: absolute;
    left: 60px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
}

.timeline-item {
    display: flex;
    margin-bottom: 4rem;
    position: relative;
}

.timeline-marker {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 700;
    margin-right: 2rem;
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 2;
}

.timeline-marker span {
    font-size: 2rem;
}

.timeline-marker small {
    font-size: 0.875rem;
    opacity: 0.9;
}

.timeline-content {
    flex: 1;
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-purple);
}

.timeline-content h3 {
    color: var(--primary-purple);
    margin-bottom: 1rem;
}

.timeline-content ul {
    list-style: none;
    margin-bottom: 1.5rem;
}

.timeline-content li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.timeline-content li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success);
    font-weight: bold;
}

.milestone {
    background: var(--light-gold);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--gold);
}

.milestone strong {
    color: var(--gold);
}

.program-benefits {
    margin-top: 4rem;
    text-align: center;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.benefit-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.benefit-item h4 {
    color: var(--primary-purple);
    margin-bottom: 1rem;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.testimonial-card {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.testimonial-header img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 1rem;
}

.client-info h4 {
    margin-bottom: 0.25rem;
    color: var(--dark-gray);
}

.client-info span {
    color: var(--medium-gray);
    font-size: 0.875rem;
}

.rating {
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.transformation {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.before, .after {
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: 6px;
}

.before {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.after {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.testimonials-cta {
    text-align: center;
}

.testimonials-cta p {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

/* About Section */
.about-section {
    padding: 80px 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 600px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
}

.about-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--gradient-gold);
    color: var(--white);
    padding: 1rem;
    border-radius: 12px;
    font-weight: 600;
    box-shadow: var(--shadow-lg);
}

.about-text .section-header {
    text-align: left;
    margin-bottom: 2rem;
}

.about-story p {
    margin-bottom: 1.5rem;
}

.credentials {
    margin: 3rem 0;
}

.credentials-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.credential-item {
    display: flex;
    align-items: center;
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 12px;
}

.credential-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.credential-info h4 {
    margin-bottom: 0.25rem;
    color: var(--primary-purple);
}

.credential-info span {
    color: var(--medium-gray);
    font-size: 0.875rem;
}

.about-approach {
    margin-top: 3rem;
}

.approach-points {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.approach-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.approach-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.approach-content h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-purple);
}

/* Guide Section */
.guide-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.guide-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.guide-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
}

.guide-text h2 {
    margin-bottom: 2rem;
}

.guide-benefits {
    margin-bottom: 2rem;
}

.benefits-list {
    list-style: none;
    margin-top: 1rem;
}

.benefits-list li {
    padding: 0.75rem 0;
    color: var(--dark-gray);
}

.guide-form {
    margin-bottom: 2rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--light-purple);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-purple);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
    accent-color: var(--primary-purple);
}

.checkmark {
    flex-shrink: 0;
}

.form-success {
    text-align: center;
    padding: 2rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 12px;
    border: 2px solid var(--success);
}

.form-success h3 {
    color: var(--success);
    margin-bottom: 1rem;
}

.guide-guarantee {
    font-size: 0.875rem;
    color: var(--medium-gray);
    text-align: center;
}

/* Webinar Section */
.webinar-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 100%);
    color: var(--white);
}

.webinar-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.webinar-badge {
    display: inline-block;
    background: var(--gradient-gold);
    color: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.webinar-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.webinar-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.webinar-detail {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.detail-icon {
    font-size: 2rem;
}

.detail-content h4 {
    margin-bottom: 0.5rem;
    color: var(--white);
}

.detail-content p {
    opacity: 0.9;
    color: var(--white);
}

.webinar-agenda h3 {
    margin-bottom: 1rem;
    color: var(--white);
}

.webinar-agenda ul {
    list-style: none;
}

.webinar-agenda li {
    padding: 0.75rem 0;
    padding-left: 1.5rem;
    position: relative;
    opacity: 0.9;
}

.webinar-agenda li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--gold);
}

.webinar-signup {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    color: var(--dark-gray);
    box-shadow: var(--shadow-xl);
}

.signup-header {
    text-align: center;
    margin-bottom: 2rem;
}

.webinar-price {
    margin-top: 1rem;
}

.price-old {
    text-decoration: line-through;
    color: var(--medium-gray);
    font-size: 0.875rem;
    display: block;
}

.price-new {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gold);
}

.signup-guarantee {
    text-align: center;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--medium-gray);
}

/* Consultation Section */
.consultation-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.consultation-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.consultation-benefits .benefits-grid {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.consultation-benefits .benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    text-align: left;
    padding: 1.5rem;
}

.benefit-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.benefit-content h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-purple);
}

.booking-form {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.booking-note {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--medium-gray);
    text-align: center;
    padding: 1rem;
    background: var(--light-gold);
    border-radius: 8px;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    font-size: 2rem;
}

.contact-details h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-purple);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: var(--primary-purple);
    text-decoration: none;
    font-weight: 500;
}

.social-links a:hover {
    color: var(--secondary-purple);
}

.contact-form {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 20px;
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 2rem;
}

.footer-brand h3 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-social a:hover {
    color: var(--secondary-purple);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-column h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 0.5rem;
}

.footer-column a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: var(--secondary-purple);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content,
    .about-content,
    .guide-content,
    .webinar-content,
    .consultation-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    h1 {
        font-size: 3rem;
    }
    
    h2 {
        font-size: 2.25rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .nav-links {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 2rem;
        transition: left 0.3s ease;
        z-index: 999;
    }
    
    .nav-links.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .hero-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .hero-cta {
        justify-content: center;
    }
    
    .timeline-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .program-timeline::before {
        display: none;
    }
    
    .timeline-marker {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .quiz-container {
        padding: 1.5rem;
    }
    
    .result-content {
        flex-direction: column;
        text-align: center;
    }
    
    .result-text {
        text-align: center;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .webinar-details {
        text-align: center;
    }
    
    .approach-item {
        flex-direction: column;
        text-align: center;
    }
    
    .consultation-benefits .benefit-item {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .credentials-list {
        grid-template-columns: 1fr;
    }
    
    .credential-item {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .hero {
        padding: 40px 0;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    h3 {
        font-size: 1.5rem;
    }
    
    .hero-img,
    .about-image img,
    .guide-image img {
        height: 300px;
    }
    
    .hero-testimonial {
        position: static;
        margin-top: 1rem;
        padding: 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        display: block;
        text-align: center;
        margin-bottom: 1rem;
        width: 100%;
    }
    
    .hero-cta {
        flex-direction: column;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .quiz-navigation {
        flex-direction: column;
        gap: 1rem;
    }
    
    .quiz-options label {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .timeline-marker {
        width: 80px;
        height: 80px;
    }
    
    .timeline-marker span {
        font-size: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 1.75rem;
    }
    
    .testimonial-card,
    .benefit-item,
    .booking-form,
    .webinar-signup {
        padding: 1.5rem;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .quiz-container {
        padding: 1rem;
    }
    
    .navbar {
        padding: 0.75rem 0;
    }
    
    .nav-brand h2 {
        font-size: 1.25rem;
    }
}

@media (max-width: 360px) {
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .timeline-marker {
        width: 60px;
        height: 60px;
    }
    
    .timeline-marker span {
        font-size: 1.25rem;
    }
    
    .hero-badge,
    .webinar-badge {
        font-size: 0.75rem;
        padding: 6px 12px;
    }
    
    .testimonial-card,
    .benefit-item {
        padding: 1rem;
    }
}

/* Animation and Interactions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling for internal links */
html {
    scroll-padding-top: 100px;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-purple);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .navbar,
    .hero-cta,
    .quiz-section,
    .webinar-section,
    .consultation-section,
    .contact-section,
    .footer {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    .testimonial-card,
    .benefit-item,
    .timeline-item {
        page-break-inside: avoid;
    }
}