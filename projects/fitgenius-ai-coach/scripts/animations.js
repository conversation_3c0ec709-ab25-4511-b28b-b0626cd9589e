/**
 * FitGenius.pl - Advanced Animations & Micro-interactions
 * Enterprise-grade animation system for premium user experience
 */

const AnimationEngine = {
    // Animation configuration
    config: {
        easing: {
            easeOutBounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            easeOutCubic: 'cubic-bezier(0.33, 1, 0.68, 1)',
            easeInOutCubic: 'cubic-bezier(0.65, 0, 0.35, 1)',
            spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
        },
        durations: {
            fast: 200,
            normal: 300,
            slow: 500,
            verySlow: 800
        },
        delays: {
            stagger: 100,
            cascade: 150,
            wave: 50
        }
    },
    
    // Active animations registry
    activeAnimations: new Map(),
    observers: new Map(),
    
    // Initialize animation system
    init() {
        this.setupScrollAnimations();
        this.setupHoverEffects();
        this.setupButtonAnimations();
        this.setupFormAnimations();
        this.setupPageTransitions();
        this.setupParallaxEffects();
        this.setupMicroInteractions();
        this.setupPerformanceOptimizations();
        console.log('✨ Animation Engine initialized');
    },
    
    // Scroll-triggered animations
    setupScrollAnimations() {
        const observerOptions = {
            threshold: [0, 0.25, 0.5, 0.75, 1],
            rootMargin: '0px 0px -10% 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerScrollAnimation(entry.target, entry.intersectionRatio);
                }
            });
        }, observerOptions);
        
        // Observe elements with animation classes
        const animatedElements = document.querySelectorAll(`
            .feature-card,
            .testimonial-card,
            .pricing-card,
            .dashboard-card,
            .section-header,
            .hero-content,
            .stat-item,
            .exercise-item
        `);
        
        animatedElements.forEach((el, index) => {
            // Set initial state
            el.style.opacity = '0';
            el.style.transform = 'translateY(50px) scale(0.95)';
            el.style.filter = 'blur(5px)';
            el.style.transition = `all ${this.config.durations.slow}ms ${this.config.easing.easeOutCubic}`;
            
            // Add staggered delay
            el.style.transitionDelay = `${index * this.config.delays.stagger}ms`;
            
            observer.observe(el);
        });
        
        this.observers.set('scroll', observer);
    },
    
    triggerScrollAnimation(element, ratio) {
        if (ratio > 0.1) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0) scale(1)';
            element.style.filter = 'blur(0px)';
            
            // Add entrance effect class
            element.classList.add('animate-in');
            
            // Trigger specific animations based on element type
            if (element.classList.contains('stat-item')) {
                this.animateStatNumbers(element);
            }
            
            if (element.classList.contains('feature-card')) {
                this.animateFeatureCard(element);
            }
        }
    },
    
    // Advanced hover effects
    setupHoverEffects() {
        // 3D tilt effect for cards
        const cards = document.querySelectorAll(`
            .feature-card,
            .pricing-card,
            .testimonial-card,
            .dashboard-card
        `);
        
        cards.forEach(card => {
            card.addEventListener('mouseenter', (e) => this.startCardTilt(e.target));
            card.addEventListener('mousemove', (e) => this.updateCardTilt(e));
            card.addEventListener('mouseleave', (e) => this.endCardTilt(e.target));
        });
        
        // Magnetic effect for buttons
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', (e) => this.startMagneticEffect(e.target));
            button.addEventListener('mousemove', (e) => this.updateMagneticEffect(e));
            button.addEventListener('mouseleave', (e) => this.endMagneticEffect(e.target));
        });
        
        // Glow effect for interactive elements
        const glowElements = document.querySelectorAll('.nav-link, .feature-cta, .plan-cta');
        glowElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => this.addGlowEffect(e.target));
            element.addEventListener('mouseleave', (e) => this.removeGlowEffect(e.target));
        });
    },
    
    startCardTilt(card) {
        card.style.transition = 'transform 0.1s ease-out';
        card.style.transformStyle = 'preserve-3d';
    },
    
    updateCardTilt(e) {
        const card = e.currentTarget;
        const rect = card.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const mouseX = e.clientX - centerX;
        const mouseY = e.clientY - centerY;
        
        const rotateX = (mouseY / (rect.height / 2)) * -10;
        const rotateY = (mouseX / (rect.width / 2)) * 10;
        
        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
        
        // Add shimmer effect
        this.createShimmerEffect(card, mouseX, mouseY);
    },
    
    endCardTilt(card) {
        card.style.transition = `transform ${this.config.durations.normal}ms ${this.config.easing.spring}`;
        card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
        
        // Remove shimmer
        const shimmer = card.querySelector('.shimmer-effect');
        if (shimmer) {
            shimmer.remove();
        }
    },
    
    createShimmerEffect(card, mouseX, mouseY) {
        let shimmer = card.querySelector('.shimmer-effect');
        if (!shimmer) {
            shimmer = document.createElement('div');
            shimmer.className = 'shimmer-effect';
            shimmer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(circle 100px at var(--x) var(--y), rgba(255,255,255,0.1) 0%, transparent 70%);
                pointer-events: none;
                border-radius: inherit;
                z-index: 1;
            `;
            card.appendChild(shimmer);
        }
        
        shimmer.style.setProperty('--x', `${mouseX + card.offsetWidth / 2}px`);
        shimmer.style.setProperty('--y', `${mouseY + card.offsetHeight / 2}px`);
    },
    
    startMagneticEffect(button) {
        button.style.transition = 'transform 0.2s ease-out';
    },
    
    updateMagneticEffect(e) {
        const button = e.currentTarget;
        const rect = button.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const mouseX = e.clientX - centerX;
        const mouseY = e.clientY - centerY;
        
        const distance = Math.sqrt(mouseX * mouseX + mouseY * mouseY);
        const maxDistance = 50;
        
        if (distance < maxDistance) {
            const strength = (maxDistance - distance) / maxDistance;
            const translateX = mouseX * strength * 0.5;
            const translateY = mouseY * strength * 0.5;
            
            button.style.transform = `translate(${translateX}px, ${translateY}px) scale(1.05)`;
        }
    },
    
    endMagneticEffect(button) {
        button.style.transition = `transform ${this.config.durations.normal}ms ${this.config.easing.spring}`;
        button.style.transform = 'translate(0px, 0px) scale(1)';
    },
    
    addGlowEffect(element) {
        element.style.boxShadow = '0 0 20px rgba(99, 102, 241, 0.5)';
        element.style.transition = `box-shadow ${this.config.durations.normal}ms ease`;
    },
    
    removeGlowEffect(element) {
        element.style.boxShadow = '';
    },
    
    // Button animations
    setupButtonAnimations() {
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .feature-cta, .plan-cta');
        
        buttons.forEach(button => {
            // Click ripple effect
            button.addEventListener('click', (e) => this.createRippleEffect(e));
            
            // Loading state animation
            if (button.dataset.loading) {
                this.setupLoadingAnimation(button);
            }
        });
    },
    
    createRippleEffect(e) {
        const button = e.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.className = 'ripple';
        ripple.style.cssText = `
            position: absolute;
            top: ${y}px;
            left: ${x}px;
            width: ${size}px;
            height: ${size}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple-animation 600ms ease-out;
            pointer-events: none;
            z-index: 10;
        `;
        
        // Add ripple animation keyframes if not exists
        this.addRippleKeyframes();
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    },
    
    addRippleKeyframes() {
        if (!document.querySelector('#ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple-animation {
                    0% {
                        transform: scale(0);
                        opacity: 1;
                    }
                    100% {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    },
    
    // Form animations
    setupFormAnimations() {
        const inputs = document.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            // Focus animations
            input.addEventListener('focus', (e) => this.animateInputFocus(e.target));
            input.addEventListener('blur', (e) => this.animateInputBlur(e.target));
            
            // Typing animations
            input.addEventListener('input', (e) => this.animateInputTyping(e.target));
        });
        
        // Form submission animation
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => this.animateFormSubmission(form));
        });
    },
    
    animateInputFocus(input) {
        input.style.transition = `all ${this.config.durations.normal}ms ${this.config.easing.easeOutCubic}`;
        input.style.transform = 'scale(1.02)';
        input.style.boxShadow = '0 0 0 3px rgba(99, 102, 241, 0.1)';
        
        // Animate label if exists
        const label = input.previousElementSibling;
        if (label && label.tagName === 'LABEL') {
            label.style.transition = `all ${this.config.durations.normal}ms ease`;
            label.style.transform = 'translateY(-5px) scale(0.9)';
            label.style.color = 'var(--primary-color)';
        }
    },
    
    animateInputBlur(input) {
        input.style.transform = 'scale(1)';
        if (!input.value) {
            input.style.boxShadow = '';
            
            const label = input.previousElementSibling;
            if (label && label.tagName === 'LABEL') {
                label.style.transform = 'translateY(0) scale(1)';
                label.style.color = '';
            }
        }
    },
    
    animateInputTyping(input) {
        // Add typing indicator
        input.classList.add('typing');
        clearTimeout(input.typingTimeout);
        
        input.typingTimeout = setTimeout(() => {
            input.classList.remove('typing');
        }, 1000);
    },
    
    // Page transitions
    setupPageTransitions() {
        // Smooth scroll with easing
        this.setupSmoothScrolling();
        
        // Section transitions
        this.setupSectionTransitions();
    },
    
    setupSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    this.smoothScrollTo(target);
                }
            });
        });
    },
    
    smoothScrollTo(target) {
        const start = window.pageYOffset;
        const targetPosition = target.offsetTop - 80; // Account for navbar
        const distance = targetPosition - start;
        const duration = Math.min(Math.abs(distance) * 0.5, 1000);
        
        let startTime = null;
        
        const animation = (currentTime) => {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);
            
            // Easing function
            const easeProgress = this.easeInOutCubic(progress);
            
            window.scrollTo(0, start + distance * easeProgress);
            
            if (progress < 1) {
                requestAnimationFrame(animation);
            }
        };
        
        requestAnimationFrame(animation);
    },
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    },
    
    // Parallax effects
    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.phone-mockup, .hero::before');
        
        if (parallaxElements.length === 0) return;
        
        const updateParallax = () => {
            const scrolled = window.pageYOffset;
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + index * 0.2;
                const yPos = -(scrolled * speed);
                element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            });
        };
        
        // Throttled scroll listener
        let ticking = false;
        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateParallax();
                    ticking = false;
                });
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', onScroll);
    },
    
    // Micro-interactions
    setupMicroInteractions() {
        // Navigation hover effects
        this.setupNavigationAnimations();
        
        // Logo animation
        this.setupLogoAnimation();
        
        // Progress bar animations
        this.setupProgressAnimations();
        
        // Notification animations
        this.setupNotificationAnimations();
    },
    
    setupNavigationAnimations() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('mouseenter', () => {
                link.style.transition = `all ${this.config.durations.fast}ms ease`;
                link.style.transform = 'translateY(-2px)';
            });
            
            link.addEventListener('mouseleave', () => {
                link.style.transform = 'translateY(0)';
            });
        });
    },
    
    setupLogoAnimation() {
        const logo = document.querySelector('.logo-icon');
        if (logo) {
            setInterval(() => {
                logo.style.animation = 'none';
                setTimeout(() => {
                    logo.style.animation = 'pulse 2s infinite';
                }, 10);
            }, 10000);
        }
    },
    
    setupProgressAnimations() {
        const progressBars = document.querySelectorAll('.progress-fill');
        
        progressBars.forEach(bar => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateProgressBar(bar);
                        observer.unobserve(bar);
                    }
                });
            });
            
            observer.observe(bar);
        });
    },
    
    animateProgressBar(bar) {
        const targetWidth = bar.style.width || '0%';
        bar.style.width = '0%';
        bar.style.transition = `width ${this.config.durations.slow}ms ${this.config.easing.easeOutCubic}`;
        
        setTimeout(() => {
            bar.style.width = targetWidth;
        }, 100);
    },
    
    // Statistics number animation
    animateStatNumbers(container) {
        const numbers = container.querySelectorAll('[data-target]');
        
        numbers.forEach(number => {
            const target = parseInt(number.dataset.target);
            const duration = 2000;
            const steps = 60;
            const increment = target / steps;
            let current = 0;
            let step = 0;
            
            const timer = setInterval(() => {
                step++;
                current += increment;
                
                if (step >= steps) {
                    current = target;
                    clearInterval(timer);
                }
                
                number.textContent = this.formatNumber(Math.floor(current));
                
                // Add bounce effect on completion
                if (step >= steps) {
                    number.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        number.style.transition = 'transform 0.3s ease';
                        number.style.transform = 'scale(1)';
                    }, 100);
                }
            }, duration / steps);
        });
    },
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(0) + 'K';
        }
        return num.toString();
    },
    
    // Feature card animation
    animateFeatureCard(card) {
        const icon = card.querySelector('.feature-icon');
        const items = card.querySelectorAll('.feature-list li');
        
        if (icon) {
            icon.style.animation = 'bounce 0.8s ease-out';
        }
        
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            item.style.transition = `all ${this.config.durations.normal}ms ease`;
            
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, index * 100);
        });
    },
    
    // Performance optimizations
    setupPerformanceOptimizations() {
        // Reduce motion for users who prefer it
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.reduceMotion();
        }
        
        // Pause animations when tab is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimations();
            } else {
                this.resumeAnimations();
            }
        });
        
        // Optimize for low-end devices
        this.optimizeForPerformance();
    },
    
    reduceMotion() {
        const style = document.createElement('style');
        style.textContent = `
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    },
    
    pauseAnimations() {
        document.body.style.animationPlayState = 'paused';
    },
    
    resumeAnimations() {
        document.body.style.animationPlayState = 'running';
    },
    
    optimizeForPerformance() {
        // Detect low-end devices
        const isLowEndDevice = navigator.hardwareConcurrency <= 2 || 
                              navigator.deviceMemory <= 4;
        
        if (isLowEndDevice) {
            // Disable expensive animations
            this.disableExpensiveAnimations();
        }
    },
    
    disableExpensiveAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            .shimmer-effect { display: none !important; }
            * { backdrop-filter: none !important; }
        `;
        document.head.appendChild(style);
    },
    
    // Notification animations
    setupNotificationAnimations() {
        // Override the showNotification function to add animations
        if (window.FitGenius && FitGenius.forms) {
            const originalShowNotification = FitGenius.forms.showNotification;
            
            FitGenius.forms.showNotification = (message, type = 'info') => {
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 1rem 1.5rem;
                    border-radius: 0.75rem;
                    color: white;
                    font-weight: 600;
                    z-index: 10000;
                    transform: translateX(120%) scale(0.8);
                    transition: all ${this.config.durations.normal}ms ${this.config.easing.spring};
                    backdrop-filter: blur(10px);
                    opacity: 0;
                `;
                
                if (type === 'success') {
                    notification.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                } else if (type === 'error') {
                    notification.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
                } else {
                    notification.style.background = 'linear-gradient(135deg, #6366f1, #4f46e5)';
                }
                
                notification.textContent = message;
                document.body.appendChild(notification);
                
                // Animate in
                requestAnimationFrame(() => {
                    notification.style.transform = 'translateX(0) scale(1)';
                    notification.style.opacity = '1';
                });
                
                // Auto remove with animation
                setTimeout(() => {
                    notification.style.transform = 'translateX(120%) scale(0.8)';
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, this.config.durations.normal);
                }, 5000);
            };
        }
    },
    
    // Utility functions
    createFloatingParticles(container, count = 5) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'floating-particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(99, 102, 241, 0.6);
                border-radius: 50%;
                pointer-events: none;
                animation: float-particle ${3 + Math.random() * 4}s ease-in-out infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 2}s;
            `;
            
            container.appendChild(particle);
        }
        
        // Add keyframes if not exists
        this.addFloatingParticleKeyframes();
    },
    
    addFloatingParticleKeyframes() {
        if (!document.querySelector('#floating-particle-styles')) {
            const style = document.createElement('style');
            style.id = 'floating-particle-styles';
            style.textContent = `
                @keyframes float-particle {
                    0%, 100% {
                        transform: translateY(0) rotate(0deg);
                        opacity: 0;
                    }
                    10%, 90% {
                        opacity: 1;
                    }
                    50% {
                        transform: translateY(-100px) rotate(180deg);
                    }
                }
            `;
            document.head.appendChild(style);
        }
    },
    
    // Cleanup function
    destroy() {
        // Remove all observers
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        
        // Cancel active animations
        this.activeAnimations.forEach(animation => animation.cancel());
        this.activeAnimations.clear();
        
        console.log('🧹 Animation Engine cleaned up');
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    AnimationEngine.init();
    
    // Add floating particles to hero section
    const hero = document.querySelector('.hero');
    if (hero) {
        AnimationEngine.createFloatingParticles(hero, 8);
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    AnimationEngine.destroy();
});

// Export for global access
window.AnimationEngine = AnimationEngine;