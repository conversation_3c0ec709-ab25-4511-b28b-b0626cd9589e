/**
 * AI Service - HuggingFace Integration
 * Production-ready AI service for BrandMe.pl
 */

class AIService {
    constructor() {
        this.config = {
            apiUrl: 'https://api-inference.huggingface.co/models',
            token: '*************************************',
            llamaModel: 'meta-llama/Meta-Llama-3.1-8B-Instruct',
            deepseekModel: 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
            useLlama: true,
            timeout: 30000,
            maxTokens: 2048,
            temperature: 0.7
        };
        
        this.requestQueue = [];
        this.isProcessing = false;
        this.rateLimiter = new RateLimiter(10, 60000); // 10 requests per minute
        
        this.init();
    }

    init() {
        this.setupErrorHandling();
        this.preloadModels();
    }

    setupErrorHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            console.error('AI Service - Unhandled promise rejection:', event.reason);
            this.showUserFriendlyError('Wystąpił błąd podczas komunikacji z AI. Spróbuj ponownie.');
        });
    }

    async preloadModels() {
        try {
            // Warm up the models with a simple request
            await this.generateText("Hello", { maxLength: 10, skipQueue: true });
        } catch (error) {
            console.warn('Model preload failed, but service will work:', error.message);
        }
    }

    // ===== MAIN AI GENERATION METHODS ===== //
    
    async generateLinkedInPost(postData) {
        const prompt = this.buildLinkedInPrompt(postData);
        const response = await this.generateText(prompt, {
            maxLength: 1500,
            temperature: 0.8,
            stopSequences: ['\n\n---', 'END_POST']
        });
        
        return this.parseLinkedInResponse(response);
    }

    async generateBio(bioData, platform) {
        const prompt = this.buildBioPrompt(bioData, platform);
        const maxLength = this.getBioMaxLength(platform);
        
        const response = await this.generateText(prompt, {
            maxLength: maxLength,
            temperature: 0.6,
            stopSequences: ['END_BIO', '\n\n']
        });
        
        return this.parseBioResponse(response, platform);
    }

    async generateWebsiteContent(userData, contentType) {
        const prompt = this.buildWebsitePrompt(userData, contentType);
        const response = await this.generateText(prompt, {
            maxLength: contentType === 'about' ? 800 : 400,
            temperature: 0.7
        });
        
        return this.parseWebsiteResponse(response, contentType);
    }

    async analyzeProfile(profileData) {
        const prompt = this.buildAnalysisPrompt(profileData);
        const response = await this.generateText(prompt, {
            maxLength: 1000,
            temperature: 0.5
        });
        
        return this.parseAnalysisResponse(response);
    }

    async generateContentIdeas(userData, count = 5) {
        const prompt = this.buildContentIdeasPrompt(userData, count);
        const response = await this.generateText(prompt, {
            maxLength: 800,
            temperature: 0.9
        });
        
        return this.parseContentIdeasResponse(response);
    }

    async optimizeContent(content, optimizationType) {
        const prompt = this.buildOptimizationPrompt(content, optimizationType);
        const response = await this.generateText(prompt, {
            maxLength: 500,
            temperature: 0.6
        });
        
        return this.parseOptimizationResponse(response);
    }

    // ===== CORE AI COMMUNICATION ===== //
    
    async generateText(prompt, options = {}) {
        if (!this.rateLimiter.canMakeRequest()) {
            throw new Error('Rate limit exceeded. Please wait a moment.');
        }

        const requestOptions = {
            maxLength: options.maxLength || 500,
            temperature: options.temperature || this.config.temperature,
            stopSequences: options.stopSequences || [],
            skipQueue: options.skipQueue || false
        };

        if (requestOptions.skipQueue) {
            return await this.makeAPIRequest(prompt, requestOptions);
        } else {
            return await this.queueRequest(prompt, requestOptions);
        }
    }

    async queueRequest(prompt, options) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                prompt,
                options,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await this.makeAPIRequest(request.prompt, request.options);
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }

            // Small delay between requests to avoid overwhelming the API
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        this.isProcessing = false;
    }

    async makeAPIRequest(prompt, options) {
        const model = this.config.useLlama ? this.config.llamaModel : this.config.deepseekModel;
        const url = `${this.config.apiUrl}/${model}`;

        const payload = {
            inputs: prompt,
            parameters: {
                max_new_tokens: options.maxLength,
                temperature: options.temperature,
                do_sample: true,
                top_p: 0.9,
                stop: options.stopSequences
            }
        };

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.token}`,
                    'Content-Type': 'application/json',
                    'X-Use-Cache': 'false'
                },
                body: JSON.stringify(payload),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API Error: ${response.status} - ${errorData.error || response.statusText}`);
            }

            const data = await response.json();
            
            if (Array.isArray(data) && data[0]?.generated_text) {
                return data[0].generated_text.replace(prompt, '').trim();
            } else if (data.generated_text) {
                return data.generated_text.replace(prompt, '').trim();
            } else {
                throw new Error('Invalid response format from AI service');
            }

        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('Request timeout - please try again');
            }
            
            console.error('AI API Error:', error);
            throw new Error(`AI generation failed: ${error.message}`);
        }
    }

    // ===== PROMPT BUILDERS ===== //
    
    buildLinkedInPrompt(postData) {
        const { topic, postType, tone, callToAction, targetAudience, keyMessage } = postData;
        
        return `Create a professional LinkedIn post in Polish language.

Topic: ${topic || keyMessage}
Post type: ${postType}
Tone: ${tone}
Target audience: ${targetAudience || 'professionals'}
Call to action: ${callToAction}

Requirements:
- Write in Polish
- Use engaging storytelling
- Include relevant emojis (2-3 max)
- Add 3-5 industry hashtags
- Length: 150-300 words
- Professional but personable tone
- Include a clear call to action

Structure:
1. Hook (attention-grabbing opening)
2. Main content with value
3. Call to action
4. Hashtags

Generate the LinkedIn post:`;
    }

    buildBioPrompt(bioData, platform) {
        const { name, title, company, industry, experience, skills, achievements, goal, personality, interests } = bioData;
        
        const platformSpecs = {
            linkedin: 'Professional bio for LinkedIn (2600 chars max). Include expertise, achievements, and call to action.',
            twitter: 'Concise Twitter bio (160 chars max). Use emojis and keywords.',
            instagram: 'Creative Instagram bio (150 chars max). Use line breaks and emojis.',
            website: 'Comprehensive website bio. Tell your story, include achievements and personality.'
        };

        return `Create a ${platform} bio in Polish for:

Name: ${name}
Position: ${title}
Company: ${company || 'Independent'}
Industry: ${industry}
Experience: ${experience}
Skills: ${skills}
Achievements: ${achievements}
Goal: ${goal}
Personality: ${personality}
Interests: ${interests}

Platform requirements: ${platformSpecs[platform]}

Write in Polish, be authentic and professional. Include personality while maintaining credibility.

Generate the bio:`;
    }

    buildWebsitePrompt(userData, contentType) {
        const prompts = {
            headline: `Create a powerful headline in Polish for ${userData.profession} with ${userData.experience} experience in ${userData.industry}. Make it compelling and memorable (max 100 chars).`,
            
            about: `Write an "About Me" section in Polish for:
            Name: ${userData.fullName}
            Profession: ${userData.profession}
            Industry: ${userData.industry}
            Experience: ${userData.experience}
            Bio: ${userData.bio}
            
            Make it personal, professional, and engaging. Include achievements and aspirations (300-500 words).`,
            
            experience: `Create a professional experience section in Polish for ${userData.profession} in ${userData.industry}. Include 2-3 roles with achievements and responsibilities. Make it specific and results-oriented.`,
            
            skills: `Generate 8-12 relevant skills for ${userData.profession} in ${userData.industry}. Focus on both technical and soft skills. Return as comma-separated list.`
        };

        return prompts[contentType] + '\n\nGenerate the content:';
    }

    buildAnalysisPrompt(profileData) {
        return `Analyze this personal brand profile and provide insights in Polish:

Profile data: ${JSON.stringify(profileData)}

Provide:
1. Strengths (2-3 points)
2. Areas for improvement (2-3 points)  
3. Recommendations (3-4 actionable items)
4. Industry positioning assessment

Be specific and actionable. Focus on personal branding strategy.

Analysis:`;
    }

    buildContentIdeasPrompt(userData, count) {
        return `Generate ${count} content ideas in Polish for ${userData.profession} in ${userData.industry}.

Include:
- LinkedIn posts
- Blog articles
- Video content
- Social media posts

Make them relevant to personal branding and professional growth. Each idea should be specific and actionable.

Content ideas:`;
    }

    buildOptimizationPrompt(content, type) {
        const optimizations = {
            engagement: 'Suggest ways to increase engagement and interaction',
            seo: 'Provide SEO optimization recommendations',
            clarity: 'Improve clarity and readability',
            cta: 'Strengthen the call-to-action'
        };

        return `Optimize this content for ${optimizations[type]} in Polish:

Content: "${content}"

Provide 3-4 specific, actionable recommendations.

Optimization suggestions:`;
    }

    // ===== RESPONSE PARSERS ===== //
    
    parseLinkedInResponse(response) {
        const lines = response.split('\n').filter(line => line.trim());
        let text = '';
        let hashtags = [];

        lines.forEach(line => {
            if (line.includes('#')) {
                const hashtagMatches = line.match(/#\w+/g);
                if (hashtagMatches) {
                    hashtags.push(...hashtagMatches);
                }
            }
            text += line + '\n';
        });

        return {
            text: text.trim(),
            hashtags: [...new Set(hashtags)],
            wordCount: text.split(' ').length,
            estimatedReach: this.calculateEstimatedReach(text),
            engagementScore: this.calculateEngagementScore(text)
        };
    }

    parseBioResponse(response, platform) {
        const cleaned = response.trim().replace(/^(Bio:|Biography:)/i, '');
        
        return {
            text: cleaned,
            length: cleaned.length,
            wordCount: cleaned.split(' ').length,
            platform: platform,
            isWithinLimit: this.checkBioLength(cleaned, platform)
        };
    }

    parseWebsiteResponse(response, contentType) {
        const cleaned = response.trim();
        
        if (contentType === 'skills') {
            return cleaned.split(',').map(skill => skill.trim()).filter(skill => skill.length > 0);
        }
        
        return cleaned;
    }

    parseAnalysisResponse(response) {
        const sections = response.split('\n').filter(line => line.trim());
        
        return {
            strengths: this.extractListItems(sections, ['strengths', 'strong', 'advantages']),
            improvements: this.extractListItems(sections, ['improvement', 'weak', 'areas']),
            recommendations: this.extractListItems(sections, ['recommend', 'suggest', 'should']),
            summary: response.trim()
        };
    }

    parseContentIdeasResponse(response) {
        const lines = response.split('\n').filter(line => line.trim());
        const ideas = [];
        
        lines.forEach(line => {
            const cleaned = line.replace(/^\d+\.?\s*/, '').replace(/^[-•]\s*/, '').trim();
            if (cleaned.length > 10) {
                ideas.push({
                    title: cleaned,
                    platform: this.detectPlatform(cleaned),
                    type: this.detectContentType(cleaned)
                });
            }
        });
        
        return ideas;
    }

    parseOptimizationResponse(response) {
        const suggestions = response.split('\n')
            .filter(line => line.trim())
            .map(line => line.replace(/^\d+\.?\s*/, '').replace(/^[-•]\s*/, '').trim())
            .filter(suggestion => suggestion.length > 10);
            
        return suggestions;
    }

    // ===== UTILITY METHODS ===== //
    
    getBioMaxLength(platform) {
        const limits = {
            linkedin: 2600,
            twitter: 160,
            instagram: 150,
            website: 1000
        };
        return limits[platform] || 500;
    }

    checkBioLength(text, platform) {
        const maxLength = this.getBioMaxLength(platform);
        return text.length <= maxLength;
    }

    calculateEstimatedReach(text) {
        const baseReach = 1000;
        let multiplier = 1;
        
        if (text.includes('?')) multiplier += 0.3;
        if (text.includes('💡') || text.includes('🎯')) multiplier += 0.2;
        if (text.match(/#\w+/g)?.length >= 3) multiplier += 0.4;
        if (text.length > 200) multiplier += 0.2;
        
        return Math.round(baseReach * multiplier);
    }

    calculateEngagementScore(text) {
        let score = 5.0;
        
        if (text.includes('?')) score += 1.5;
        if (text.toLowerCase().includes('comment')) score += 1.0;
        if (text.toLowerCase().includes('share')) score += 0.8;
        if (text.match(/[😊🎯💡🚀🔥✨]/g)) score += 0.5;
        
        return Math.min(score, 10.0);
    }

    extractListItems(sections, keywords) {
        const items = [];
        sections.forEach(section => {
            const lowerSection = section.toLowerCase();
            if (keywords.some(keyword => lowerSection.includes(keyword))) {
                const item = section.replace(/^\d+\.?\s*/, '').replace(/^[-•]\s*/, '').trim();
                if (item.length > 10) {
                    items.push(item);
                }
            }
        });
        return items;
    }

    detectPlatform(text) {
        const lower = text.toLowerCase();
        if (lower.includes('linkedin')) return 'linkedin';
        if (lower.includes('instagram') || lower.includes('story')) return 'instagram';
        if (lower.includes('twitter')) return 'twitter';
        if (lower.includes('blog') || lower.includes('article')) return 'blog';
        if (lower.includes('video') || lower.includes('youtube')) return 'video';
        return 'general';
    }

    detectContentType(text) {
        const lower = text.toLowerCase();
        if (lower.includes('case study')) return 'case-study';
        if (lower.includes('tutorial') || lower.includes('guide')) return 'educational';
        if (lower.includes('behind') || lower.includes('scenes')) return 'behind-scenes';
        if (lower.includes('tip') || lower.includes('advice')) return 'tips';
        if (lower.includes('story') || lower.includes('experience')) return 'story';
        return 'general';
    }

    showUserFriendlyError(message) {
        // Integration with existing notification system
        if (window.brandMePlatform) {
            window.brandMePlatform.showNotification(message, 'error');
        } else {
            console.error(message);
        }
    }

    // ===== PUBLIC API METHODS ===== //
    
    async isServiceReady() {
        try {
            await this.generateText("Test", { maxLength: 10, skipQueue: true });
            return true;
        } catch (error) {
            return false;
        }
    }

    getServiceStatus() {
        return {
            isProcessing: this.isProcessing,
            queueLength: this.requestQueue.length,
            rateLimitRemaining: this.rateLimiter.getRemainingRequests(),
            model: this.config.useLlama ? this.config.llamaModel : this.config.deepseekModel
        };
    }

    clearQueue() {
        this.requestQueue.forEach(request => {
            request.reject(new Error('Request cancelled'));
        });
        this.requestQueue = [];
        this.isProcessing = false;
    }
}

// ===== RATE LIMITER CLASS ===== //
class RateLimiter {
    constructor(maxRequests, windowMs) {
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
        this.requests = [];
    }

    canMakeRequest() {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < this.windowMs);
        return this.requests.length < this.maxRequests;
    }

    makeRequest() {
        if (this.canMakeRequest()) {
            this.requests.push(Date.now());
            return true;
        }
        return false;
    }

    getRemainingRequests() {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < this.windowMs);
        return Math.max(0, this.maxRequests - this.requests.length);
    }
}

// Initialize AI Service
const aiService = new AIService();

// Export for global use
window.AIService = AIService;
window.aiService = aiService;