// Multi-language translations for SmartCard.pl
const translations = {
    pl: {
        // Navigation
        brand: "SmartCard.pl",
        nav_features: "Funkcje",
        nav_templates: "Szablony",
        nav_pricing: "Cennik",
        nav_demo: "Demo",
        nav_cta: "<PERSON><PERSON><PERSON><PERSON><PERSON>j za darmo",
        
        // Hero Section
        hero_badge: "AI-Powered • 2025 Edition",
        hero_title_1: "Twoja wizytówka w erze",
        hero_title_2: "rób wrażenie od pierwszego kontaktu",
        hero_subtitle: "Stwórz profesjonalną wizytówkę z avatarem AI, kodem QR, NFC i zaawansowanymi analytics w zaledwie 60 sekund. Żegnaj plastikowe wizytówki!",
        stat_users: "Użytkowników",
        stat_cards: "Wizytówek",
        stat_satisfaction: "Satysfakcji",
        cta_primary: "Stwórz swoją AI wizytówkę w 60s",
        cta_secondary: "Zobacz demo",
        social_proof: "Dołącz do tysięcy profesjonalistów, którz<PERSON> już zaufali SmartCard.pl",
        
        // Demo Card
        demo_name: "<PERSON>",
        demo_title: "CEO & Founder",
        demo_company: "Innovation Tech",
        nfc_tap: "Dotknij telefonem",
        scanning: "Skanowanie...",
        
        // Creator Section
        creator_title: "Kreator AI Wizytówek",
        creator_subtitle: "Zobacz jak łatwo możesz stworzyć profesjonalną wizytówkę w czasie rzeczywistym",
        
        // Form
        form_personal: "Dane osobowe",
        form_name: "Imię i nazwisko",
        form_title: "Stanowisko",
        form_company: "Firma",
        form_contact: "Kontakt",
        form_email: "Email",
        form_phone: "Telefon",
        form_website: "Website",
        form_avatar: "Avatar AI",
        upload_photo: "Prześlij zdjęcie",
        form_template: "Szablon",
        
        // Preview
        preview_title: "Podgląd na żywo",
        generate_qr: "Generuj kod QR",
        download: "Pobierz",
        share: "Udostępnij",
        
        // Features
        features_title: "Funkcje, które zmieniają wszystko",
        features_subtitle: "Nie tylko wizytówka - to kompletny system networking i personal branding",
        
        feature_ai_title: "Generator Avatarów AI",
        feature_ai_desc: "Prześlij zdjęcie, a AI stworzy profesjonalny avatar w 3 stylach: business, cartoon, abstract",
        
        feature_3d_title: "Animacja 3D Flip",
        feature_3d_desc: "Wizytówka z efektem 3D flip - przód z danymi, tył z kodem QR i kontaktami",
        
        feature_qr_title: "Dynamiczny kod QR",
        feature_qr_desc: "Automatycznie generowany kod QR z twoimi danymi, możliwość śledzenia skanowań",
        
        feature_nfc_title: "Symulacja NFC",
        feature_nfc_desc: "Efekt \"tapnij telefonem\" z animacją transferu danych przez NFC",
        
        feature_analytics_title: "Zaawansowane Analytics",
        feature_analytics_desc: "Zobacz kto, kiedy i skąd przeglądał twoją wizytówkę. Heatmapy, statystyki, lead scoring",
        
        feature_voice_title: "Nagranie głosowe",
        feature_voice_desc: "Nagraj 15-sekundowe wprowadzenie głosowe - osobisty akcent, który wyróżni Cię z tłumu",
        
        feature_team_title: "Zarządzanie zespołem",
        feature_team_desc: "Twórz wizytówki dla całego zespołu z jednolitym brandingiem firmowym",
        
        feature_calendar_title: "Integracja z kalendarzem",
        feature_calendar_desc: "Bezpośrednie umówienie spotkania z twojej wizytówki - integracja z Google Calendar, Outlook",
        
        feature_ar_title: "Podgląd AR",
        feature_ar_desc: "Zobacz swoją wizytówkę w rozszerzonej rzeczywistości przez kamerę telefonu",
        
        // Templates
        templates_title: "15+ Oszałamiających szablonów",
        templates_subtitle: "Każdy szablon to małe dzieło sztuki z unikalną animacją",
        template_modern: "Przezroczyste elementy z efektem szkła",
        template_neon: "Świecące neony w stylu cyberpunk",
        template_minimal: "Czysty design z subtelnymi animacjami",
        template_creative: "Artystyczne kształty i żywe kolory",
        
        // Pricing
        pricing_title: "Ceny, które nie bolą",
        pricing_subtitle: "Rozpocznij za darmo, płać tylko za to czego potrzebujesz",
        
        plan_free: "Darmowy",
        plan_pro: "Pro",
        plan_business: "Business",
        most_popular: "Najpopularniejszy",
        
        // Free Plan
        free_feature_1: "1 wizytówka",
        free_feature_2: "3 szablony",
        free_feature_3: "Kod QR",
        free_feature_4: "Podstawowe analytics",
        start_free: "Rozpocznij za darmo",
        
        // Pro Plan
        pro_feature_1: "Nieograniczone wizytówki",
        pro_feature_2: "Wszystkie szablony",
        pro_feature_3: "Avatar AI",
        pro_feature_4: "Nagranie głosowe",
        pro_feature_5: "Zaawansowane analytics",
        pro_feature_6: "Integracja z kalendarzem",
        start_pro: "Wybierz Pro",
        
        // Business Plan
        business_feature_1: "Wszystko z Pro",
        business_feature_2: "Zarządzanie zespołem",
        business_feature_3: "Branding firmowy",
        business_feature_4: "API dostęp",
        business_feature_5: "White-label",
        business_feature_6: "Dedykowane wsparcie",
        contact_sales: "Skontaktuj się",
        
        // Footer
        footer_desc: "Rewolucjonizujemy networking z AI. Twoja wizytówka przyszłości już dziś.",
        footer_product: "Produkt",
        footer_features: "Funkcje",
        footer_templates: "Szablony",
        footer_pricing: "Cennik",
        footer_support: "Wsparcie",
        footer_help: "Pomoc",
        footer_contact: "Kontakt",
        footer_api: "API",
        footer_legal: "Prawne",
        footer_privacy: "Prywatność",
        footer_terms: "Regulamin",
        footer_cookies: "Cookies",
        footer_rights: "Wszystkie prawa zastrzeżone"
    },
    
    en: {
        // Navigation
        brand: "SmartCard.pl",
        nav_features: "Features",
        nav_templates: "Templates",
        nav_pricing: "Pricing",
        nav_demo: "Demo",
        nav_cta: "Start Free",
        
        // Hero Section
        hero_badge: "AI-Powered • 2025 Edition",
        hero_title_1: "Your business card in the age of",
        hero_title_2: "make an impression from first contact",
        hero_subtitle: "Create a professional business card with AI avatar, QR code, NFC and advanced analytics in just 60 seconds. Say goodbye to plastic cards!",
        stat_users: "Users",
        stat_cards: "Cards",
        stat_satisfaction: "Satisfaction",
        cta_primary: "Create your AI card in 60s",
        cta_secondary: "Watch demo",
        social_proof: "Join thousands of professionals who already trust SmartCard.pl",
        
        // Demo Card
        demo_name: "John Smith",
        demo_title: "CEO & Founder",
        demo_company: "Innovation Tech",
        nfc_tap: "Tap with phone",
        scanning: "Scanning...",
        
        // Creator Section
        creator_title: "AI Business Card Creator",
        creator_subtitle: "See how easy it is to create a professional business card in real-time",
        
        // Form
        form_personal: "Personal Info",
        form_name: "Full Name",
        form_title: "Job Title",
        form_company: "Company",
        form_contact: "Contact",
        form_email: "Email",
        form_phone: "Phone",
        form_website: "Website",
        form_avatar: "AI Avatar",
        upload_photo: "Upload Photo",
        form_template: "Template",
        
        // Preview
        preview_title: "Live Preview",
        generate_qr: "Generate QR",
        download: "Download",
        share: "Share",
        
        // Features
        features_title: "Features that change everything",
        features_subtitle: "Not just a business card - it's a complete networking and personal branding system",
        
        feature_ai_title: "AI Avatar Generator",
        feature_ai_desc: "Upload a photo and AI will create a professional avatar in 3 styles: business, cartoon, abstract",
        
        feature_3d_title: "3D Flip Animation",
        feature_3d_desc: "Business card with 3D flip effect - front with data, back with QR code and contacts",
        
        feature_qr_title: "Dynamic QR Code",
        feature_qr_desc: "Automatically generated QR code with your data, ability to track scans",
        
        feature_nfc_title: "NFC Simulation",
        feature_nfc_desc: "\"Tap with phone\" effect with NFC data transfer animation",
        
        feature_analytics_title: "Advanced Analytics",
        feature_analytics_desc: "See who, when and where viewed your card. Heatmaps, statistics, lead scoring",
        
        feature_voice_title: "Voice Recording",
        feature_voice_desc: "Record a 15-second voice introduction - personal touch that sets you apart",
        
        feature_team_title: "Team Management",
        feature_team_desc: "Create cards for your entire team with consistent corporate branding",
        
        feature_calendar_title: "Calendar Integration",
        feature_calendar_desc: "Direct meeting booking from your card - integration with Google Calendar, Outlook",
        
        feature_ar_title: "AR Preview",
        feature_ar_desc: "See your business card in augmented reality through your phone camera",
        
        // Templates
        templates_title: "15+ Stunning Templates",
        templates_subtitle: "Each template is a small work of art with unique animation",
        template_modern: "Transparent elements with glass effect",
        template_neon: "Glowing neons in cyberpunk style",
        template_minimal: "Clean design with subtle animations",
        template_creative: "Artistic shapes and vibrant colors",
        
        // Pricing
        pricing_title: "Prices that don't hurt",
        pricing_subtitle: "Start free, pay only for what you need",
        
        plan_free: "Free",
        plan_pro: "Pro",
        plan_business: "Business",
        most_popular: "Most Popular",
        
        // Free Plan
        free_feature_1: "1 business card",
        free_feature_2: "3 templates",
        free_feature_3: "QR Code",
        free_feature_4: "Basic analytics",
        start_free: "Start Free",
        
        // Pro Plan
        pro_feature_1: "Unlimited cards",
        pro_feature_2: "All templates",
        pro_feature_3: "AI Avatar",
        pro_feature_4: "Voice recording",
        pro_feature_5: "Advanced analytics",
        pro_feature_6: "Calendar integration",
        start_pro: "Choose Pro",
        
        // Business Plan
        business_feature_1: "Everything in Pro",
        business_feature_2: "Team management",
        business_feature_3: "Corporate branding",
        business_feature_4: "API access",
        business_feature_5: "White-label",
        business_feature_6: "Dedicated support",
        contact_sales: "Contact Sales",
        
        // Footer
        footer_desc: "Revolutionizing networking with AI. Your business card of the future, today.",
        footer_product: "Product",
        footer_features: "Features",
        footer_templates: "Templates",
        footer_pricing: "Pricing",
        footer_support: "Support",
        footer_help: "Help",
        footer_contact: "Contact",
        footer_api: "API",
        footer_legal: "Legal",
        footer_privacy: "Privacy",
        footer_terms: "Terms",
        footer_cookies: "Cookies",
        footer_rights: "All rights reserved"
    },
    
    es: {
        // Navigation
        brand: "SmartCard.pl",
        nav_features: "Características",
        nav_templates: "Plantillas",
        nav_pricing: "Precios",
        nav_demo: "Demo",
        nav_cta: "Empezar Gratis",
        
        // Hero Section
        hero_badge: "Impulsado por IA • Edición 2025",
        hero_title_1: "Tu tarjeta de visita en la era de",
        hero_title_2: "causa impresión desde el primer contacto",
        hero_subtitle: "Crea una tarjeta de visita profesional con avatar IA, código QR, NFC y análisis avanzados en solo 60 segundos. ¡Adiós a las tarjetas de plástico!",
        stat_users: "Usuarios",
        stat_cards: "Tarjetas",
        stat_satisfaction: "Satisfacción",
        cta_primary: "Crea tu tarjeta IA en 60s",
        cta_secondary: "Ver demo",
        social_proof: "Únete a miles de profesionales que ya confían en SmartCard.pl",
        
        // Demo Card
        demo_name: "Juan García",
        demo_title: "CEO y Fundador",
        demo_company: "Innovation Tech",
        nfc_tap: "Toca con el teléfono",
        scanning: "Escaneando...",
        
        // Creator Section
        creator_title: "Creador de Tarjetas IA",
        creator_subtitle: "Ve lo fácil que es crear una tarjeta de visita profesional en tiempo real",
        
        // Form
        form_personal: "Información Personal",
        form_name: "Nombre Completo",
        form_title: "Cargo",
        form_company: "Empresa",
        form_contact: "Contacto",
        form_email: "Email",
        form_phone: "Teléfono",
        form_website: "Sitio Web",
        form_avatar: "Avatar IA",
        upload_photo: "Subir Foto",
        form_template: "Plantilla",
        
        // Preview
        preview_title: "Vista Previa en Vivo",
        generate_qr: "Generar QR",
        download: "Descargar",
        share: "Compartir",
        
        // Features
        features_title: "Características que lo cambian todo",
        features_subtitle: "No solo una tarjeta de visita: es un sistema completo de networking y marca personal",
        
        feature_ai_title: "Generador de Avatar IA",
        feature_ai_desc: "Sube una foto y la IA creará un avatar profesional en 3 estilos: business, cartoon, abstracto",
        
        feature_3d_title: "Animación 3D Flip",
        feature_3d_desc: "Tarjeta de visita con efecto 3D flip - frente con datos, dorso con código QR y contactos",
        
        feature_qr_title: "Código QR Dinámico",
        feature_qr_desc: "Código QR generado automáticamente con tus datos, capacidad de rastrear escaneos",
        
        feature_nfc_title: "Simulación NFC",
        feature_nfc_desc: "Efecto \"toca con el teléfono\" con animación de transferencia de datos NFC",
        
        feature_analytics_title: "Análisis Avanzados",
        feature_analytics_desc: "Ve quién, cuándo y dónde vio tu tarjeta. Mapas de calor, estadísticas, puntuación de leads",
        
        feature_voice_title: "Grabación de Voz",
        feature_voice_desc: "Graba una introducción de voz de 15 segundos - toque personal que te distingue",
        
        feature_team_title: "Gestión de Equipo",
        feature_team_desc: "Crea tarjetas para todo tu equipo con marca corporativa consistente",
        
        feature_calendar_title: "Integración de Calendario",
        feature_calendar_desc: "Reserva directa de reuniones desde tu tarjeta - integración con Google Calendar, Outlook",
        
        feature_ar_title: "Vista Previa AR",
        feature_ar_desc: "Ve tu tarjeta de visita en realidad aumentada a través de la cámara de tu teléfono",
        
        // Templates
        templates_title: "15+ Plantillas Impresionantes",
        templates_subtitle: "Cada plantilla es una pequeña obra de arte con animación única",
        template_modern: "Elementos transparentes con efecto de cristal",
        template_neon: "Neones brillantes en estilo cyberpunk",
        template_minimal: "Diseño limpio con animaciones sutiles",
        template_creative: "Formas artísticas y colores vibrantes",
        
        // Pricing
        pricing_title: "Precios que no duelen",
        pricing_subtitle: "Empieza gratis, paga solo por lo que necesites",
        
        plan_free: "Gratis",
        plan_pro: "Pro",
        plan_business: "Business",
        most_popular: "Más Popular",
        
        // Free Plan
        free_feature_1: "1 tarjeta de visita",
        free_feature_2: "3 plantillas",
        free_feature_3: "Código QR",
        free_feature_4: "Análisis básicos",
        start_free: "Empezar Gratis",
        
        // Pro Plan
        pro_feature_1: "Tarjetas ilimitadas",
        pro_feature_2: "Todas las plantillas",
        pro_feature_3: "Avatar IA",
        pro_feature_4: "Grabación de voz",
        pro_feature_5: "Análisis avanzados",
        pro_feature_6: "Integración de calendario",
        start_pro: "Elegir Pro",
        
        // Business Plan
        business_feature_1: "Todo en Pro",
        business_feature_2: "Gestión de equipo",
        business_feature_3: "Marca corporativa",
        business_feature_4: "Acceso API",
        business_feature_5: "Marca blanca",
        business_feature_6: "Soporte dedicado",
        contact_sales: "Contactar Ventas",
        
        // Footer
        footer_desc: "Revolucionando el networking con IA. Tu tarjeta de visita del futuro, hoy.",
        footer_product: "Producto",
        footer_features: "Características",
        footer_templates: "Plantillas",
        footer_pricing: "Precios",
        footer_support: "Soporte",
        footer_help: "Ayuda",
        footer_contact: "Contacto",
        footer_api: "API",
        footer_legal: "Legal",
        footer_privacy: "Privacidad",
        footer_terms: "Términos",
        footer_cookies: "Cookies",
        footer_rights: "Todos los derechos reservados"
    }
};

// Language switching functionality
let currentLanguage = 'pl';

function switchLanguage(lang) {
    currentLanguage = lang;
    
    // Update all elements with data-translate attribute
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[lang] && translations[lang][key]) {
            element.textContent = translations[lang][key];
        }
    });
    
    // Update active language button
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-lang') === lang) {
            btn.classList.add('active');
        }
    });
    
    // Update document language
    document.documentElement.lang = lang;
    
    // Update demo card based on language
    updateDemoCard(lang);
    
    // Save language preference
    localStorage.setItem('smartcard-language', lang);
}

function updateDemoCard(lang) {
    const demoData = {
        pl: {
            name: "Jan Kowalski",
            title: "CEO & Founder",
            company: "Innovation Tech",
            email: "<EMAIL>",
            phone: "+48 ***********",
            website: "innovation.tech",
            linkedin: "linkedin.com/in/jankowalski"
        },
        en: {
            name: "John Smith",
            title: "CEO & Founder", 
            company: "Innovation Tech",
            email: "<EMAIL>",
            phone: "****** 123 4567",
            website: "innovation.tech",
            linkedin: "linkedin.com/in/johnsmith"
        },
        es: {
            name: "Juan García",
            title: "CEO y Fundador",
            company: "Innovation Tech", 
            email: "<EMAIL>",
            phone: "+34 ***********",
            website: "innovation.tech",
            linkedin: "linkedin.com/in/juangarcia"
        }
    };
    
    const data = demoData[lang];
    const cardName = document.querySelector('.card-name');
    const cardTitle = document.querySelector('.card-title');
    const cardCompany = document.querySelector('.card-company');
    
    if (cardName) cardName.textContent = data.name;
    if (cardTitle) cardTitle.textContent = data.title;
    if (cardCompany) cardCompany.textContent = data.company;
    
    // Update form placeholders
    const fullNameInput = document.getElementById('fullName');
    const jobTitleInput = document.getElementById('jobTitle');
    const companyInput = document.getElementById('company');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');
    const websiteInput = document.getElementById('website');
    
    if (fullNameInput) fullNameInput.placeholder = data.name;
    if (jobTitleInput) jobTitleInput.placeholder = data.title;
    if (companyInput) companyInput.placeholder = data.company;
    if (emailInput) emailInput.placeholder = data.email;
    if (phoneInput) phoneInput.placeholder = data.phone;
    if (websiteInput) websiteInput.placeholder = data.website;
}

// Initialize language system
function initializeLanguage() {
    // Load saved language or default to Polish
    const savedLanguage = localStorage.getItem('smartcard-language') || 'pl';
    
    // Set up language button event listeners
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const lang = btn.getAttribute('data-lang');
            switchLanguage(lang);
        });
    });
    
    // Apply initial language
    switchLanguage(savedLanguage);
}

// Export for use in other scripts
window.translations = translations;
window.switchLanguage = switchLanguage;
window.initializeLanguage = initializeLanguage;
window.currentLanguage = () => currentLanguage;