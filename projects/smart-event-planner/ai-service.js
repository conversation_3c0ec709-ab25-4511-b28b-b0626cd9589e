// AI Service - Llama 3.1 8B Integration for EventAI.pl

class AIService {
    constructor() {
        // Load configuration
        this.config = window.AppConfig || new Config();
        
        // Validate configuration
        try {
            this.config.validateConfig();
        } catch (error) {
            console.error('AI Service configuration error:', error);
            this.fallbackMode = true;
        }
        
        // API Configuration
        this.baseURL = this.config.get('HF_INFERENCE_URL');
        this.chatURL = this.config.get('HF_CHAT_URL');
        this.token = this.config.get('HF_TOKEN');
        
        // Models Configuration
        this.models = {
            primary: this.config.getAIModel('primary'), // Llama 3.1 8B
            fallback: this.config.getAIModel('fallback'), // DeepSeek R1
            sentiment: this.config.getAIModel('sentiment'),
            classification: this.config.getAIModel('classification')
        };
        
        // Rate limiting and caching
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessing = false;
        this.maxCacheSize = 100;
        this.cacheTimeout = this.config.get('CACHE_DURATION_MS');
        this.requestTimeout = this.config.get('REQUEST_TIMEOUT_MS');
        
        // Request tracking for rate limiting
        this.requestHistory = [];
        this.maxRequestsPerMinute = this.config.get('MAX_REQUESTS_PER_MINUTE');
        
        this.initializeAI();
    }

    async initializeAI() {
        // Initialize AI service with fallbacks
        try {
            await this.testConnection();
            console.log('AI Service initialized successfully');
        } catch (error) {
            console.warn('AI Service fallback mode:', error.message);
            this.fallbackMode = true;
        }
    }

    async testConnection() {
        const testMessage = "Odpowiedz krótko: Czy jesteś gotowy do pomocy w planowaniu wydarzeń?";
        await this.makeLlamaRequest([{ role: "user", content: testMessage }]);
    }

    // Rate Limiting Check
    checkRateLimit() {
        const now = Date.now();
        const oneMinuteAgo = now - 60000;
        
        // Clean old requests
        this.requestHistory = this.requestHistory.filter(time => time > oneMinuteAgo);
        
        if (this.requestHistory.length >= this.maxRequestsPerMinute) {
            const oldestRequest = Math.min(...this.requestHistory);
            const waitTime = 60000 - (now - oldestRequest);
            return { allowed: false, waitTime };
        }
        
        this.requestHistory.push(now);
        return { allowed: true, waitTime: 0 };
    }

    // Llama 3.1 8B Chat Completions Request
    async makeLlamaRequest(messages, options = {}) {
        const rateCheck = this.checkRateLimit();
        if (!rateCheck.allowed) {
            this.config.log(`Rate limit exceeded, waiting ${rateCheck.waitTime}ms`);
            await this.sleep(rateCheck.waitTime);
        }

        const cacheKey = `llama_${JSON.stringify(messages)}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                this.config.log('Returning cached response for Llama request');
                return cached.data;
            }
            this.cache.delete(cacheKey);
        }

        const payload = {
            model: this.models.primary,
            messages: messages,
            max_tokens: options.max_tokens || 1000,
            temperature: options.temperature || 0.7,
            top_p: options.top_p || 0.9,
            stream: false,
            ...options
        };

        try {
            this.config.log('Making Llama API request', { messages: messages.length, model: this.models.primary });
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

            const response = await fetch(this.chatURL, {
                method: 'POST',
                headers: this.config.getHeaders(),
                body: JSON.stringify(payload),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                if (response.status === 503) {
                    this.config.log('Model loading, waiting...');
                    await this.sleep(3000);
                    return this.makeLlamaRequest(messages, options); // Retry once
                }
                
                if (response.status === 429) {
                    this.config.log('Rate limited by API, falling back...');
                    return this.fallbackResponse(messages);
                }

                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            // Cache successful response
            this.cacheResponse(cacheKey, data);
            
            this.config.log('Llama API response received', { 
                usage: data.usage,
                model: data.model 
            });
            
            return data;

        } catch (error) {
            this.config.error('Llama API request failed', error);
            
            if (error.name === 'AbortError') {
                this.config.error('Request timeout');
            }
            
            // Try fallback model
            if (options.retryWithFallback !== false) {
                this.config.log('Trying fallback model...');
                return this.makeFallbackRequest(messages, options);
            }
            
            return this.fallbackResponse(messages);
        }
    }

    // Fallback to DeepSeek R1 model
    async makeFallbackRequest(messages, options = {}) {
        const fallbackPayload = {
            model: this.models.fallback,
            messages: messages,
            max_tokens: options.max_tokens || 800,
            temperature: options.temperature || 0.6,
            ...options,
            retryWithFallback: false // Prevent infinite recursion
        };

        try {
            const fallbackURL = `${this.baseURL}/${this.models.fallback}/v1/chat/completions`;
            
            const response = await fetch(fallbackURL, {
                method: 'POST',
                headers: this.config.getHeaders(),
                body: JSON.stringify(fallbackPayload)
            });

            if (response.ok) {
                const data = await response.json();
                this.config.log('Fallback model response received');
                return data;
            }
        } catch (error) {
            this.config.error('Fallback model also failed', error);
        }

        return this.fallbackResponse(messages);
    }

    // Core AI Request Handler with Queue Management
    async makeRequest(model, payload, retries = 3) {
        const cacheKey = `${model}_${JSON.stringify(payload)}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
            this.cache.delete(cacheKey);
        }

        // Add to queue if needed
        if (this.isProcessing) {
            return new Promise((resolve, reject) => {
                this.requestQueue.push({ model, payload, resolve, reject, retries });
            });
        }

        return this.processRequest(model, payload, retries, cacheKey);
    }

    async processRequest(model, payload, retries, cacheKey) {
        this.isProcessing = true;
        
        try {
            const response = await fetch(`${this.baseURL}/${model}`, {
                method: 'POST',
                headers: this.config.getHeaders(),
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                if (response.status === 503 && retries > 0) {
                    // Model is loading, wait and retry
                    await this.sleep(2000);
                    return this.processRequest(model, payload, retries - 1, cacheKey);
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            // Cache successful response
            this.cacheResponse(cacheKey, data);
            
            return data;
        } catch (error) {
            if (retries > 0) {
                await this.sleep(1000);
                return this.processRequest(model, payload, retries - 1, cacheKey);
            }
            
            // Return fallback response
            return this.getFallbackResponse(model, payload);
        } finally {
            this.isProcessing = false;
            this.processQueue();
        }
    }

    async processQueue() {
        if (this.requestQueue.length > 0) {
            const { model, payload, resolve, reject, retries } = this.requestQueue.shift();
            try {
                const result = await this.makeRequest(model, payload, retries);
                resolve(result);
            } catch (error) {
                reject(error);
            }
        }
    }

    cacheResponse(key, data) {
        // Manage cache size
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Fallback response for failed requests
    fallbackResponse(messages) {
        const lastMessage = messages[messages.length - 1];
        this.config.log('Using fallback response for message:', lastMessage.content);
        
        return {
            choices: [{
                message: {
                    role: "assistant",
                    content: "Przepraszam, obecnie doświadczamy problemów z AI. Używam podstawowych rekomendacji."
                }
            }],
            usage: { total_tokens: 50 },
            model: "fallback"
        };
    }

    // Event Planning AI Functions

    async generateEventRecommendations(eventData) {
        try {
            const prompt = this.config.formatPrompt('event_planning', {
                context: 'Planowanie wydarzenia w Polsce',
                eventType: eventData.type,
                guests: eventData.guests,
                budget: eventData.budget,
                location: eventData.location,
                date: eventData.date
            });

            const messages = [
                {
                    role: "system",
                    content: "Jesteś ekspertem od planowania wydarzeń w Polsce. Odpowiadasz w języku polskim i dostarczasz konkretne, praktyczne rekomendacje w formacie JSON."
                },
                {
                    role: "user",
                    content: prompt
                }
            ];

            const response = await this.makeLlamaRequest(messages, {
                temperature: 0.7,
                max_tokens: 1200
            });

            return this.parseEventRecommendations(response, eventData);
        } catch (error) {
            this.config.error('AI recommendation error:', error);
            return this.getFallbackEventRecommendations(eventData);
        }
    }

    buildEventPrompt(eventData) {
        const { type, guests, budget, location, date, duration } = eventData;
        
        return `Plan a ${type} event for ${guests} guests with budget ${budget} PLN in ${location}. 
        Duration: ${duration}. Date: ${date}.
        
        Provide recommendations for:
        1. Venue suggestions with pricing
        2. Catering options
        3. Entertainment ideas
        4. Budget optimization tips
        5. Timeline suggestions
        
        Response format: venue|catering|entertainment|tips|timeline`;
    }

    parseEventRecommendations(aiResponse, eventData) {
        try {
            let content;
            
            // Extract content from Llama response
            if (aiResponse.choices && aiResponse.choices[0]) {
                content = aiResponse.choices[0].message.content;
            } else if (aiResponse.generated_text) {
                content = aiResponse.generated_text;
            } else {
                throw new Error('Invalid response format');
            }

            this.config.log('Parsing AI response:', content.substring(0, 200) + '...');

            // Try to parse JSON response
            let parsedData;
            try {
                // Look for JSON in the response
                const jsonMatch = content.match(/\{.*\}/s);
                if (jsonMatch) {
                    parsedData = JSON.parse(jsonMatch[0]);
                } else {
                    // If no JSON found, parse manually
                    parsedData = this.parseTextualResponse(content, eventData);
                }
            } catch (parseError) {
                this.config.log('JSON parsing failed, using textual parsing');
                parsedData = this.parseTextualResponse(content, eventData);
            }

            return {
                venue: parsedData.venue || this.parseVenueRecommendation(content, eventData),
                catering: parsedData.catering || this.parseCateringRecommendation(content, eventData),
                entertainment: parsedData.entertainment || this.parseEntertainmentRecommendation(content, eventData),
                tips: parsedData.budget_tips || this.parseBudgetTips(content, eventData),
                timeline: parsedData.timeline || this.parseTimelineRecommendation(content, eventData),
                confidence: 0.85,
                aiGenerated: true,
                model: aiResponse.model || this.models.primary
            };
        } catch (error) {
            this.config.error('Failed to parse AI recommendations:', error);
            return this.getFallbackEventRecommendations(eventData);
        }
    }

    parseTextualResponse(content, eventData) {
        // Extract information from textual response using patterns
        const patterns = {
            venue: /(?:miejsce|venue)[\s\S]*?(\d+[\s,]*\d*)\s*zł/i,
            catering: /(?:catering|jedzenie)[\s\S]*?(\d+)\s*zł\s*(?:\/|na)\s*osob/i,
            entertainment: /(?:rozrywka|entertainment|muzyka)[\s\S]*?(\d+[\s,]*\d*)\s*zł/i
        };

        const extracted = {};
        
        for (const [key, pattern] of Object.entries(patterns)) {
            const match = content.match(pattern);
            if (match) {
                const price = parseInt(match[1].replace(/[,\s]/g, ''));
                extracted[key] = { estimatedPrice: price };
            }
        }

        return extracted;
    }

    parseVenueRecommendation(text, eventData) {
        const venueTypes = {
            wedding: ['Hotel', 'Pałac', 'Sala bankietowa', 'Restauracja'],
            corporate: ['Centrum konferencyjne', 'Hotel biznesowy', 'Sala konferencyjna'],
            birthday: ['Restauracja', 'Klub', 'Sala prywatna'],
            conference: ['Centrum kongresowe', 'Arena', 'Centrum wystawiennicze']
        };

        const venues = venueTypes[eventData.type] || venueTypes.wedding;
        const selectedVenue = venues[Math.floor(Math.random() * venues.length)];
        
        return {
            name: `${selectedVenue} Premium ${eventData.location}`,
            description: `Idealne miejsce na ${eventData.type} dla ${eventData.guests} osób`,
            price: this.calculateVenuePrice(eventData),
            capacity: eventData.guests,
            features: this.getVenueFeatures(eventData.type),
            rating: (4.2 + Math.random() * 0.7).toFixed(1),
            availability: this.checkAvailability(eventData.date)
        };
    }

    parseCateringRecommendation(text, eventData) {
        const menuTypes = {
            wedding: ['Menu weselne premium', 'Bufet tradycyjny', 'Menu degustacyjne'],
            corporate: ['Business lunch', 'Coffee break+', 'Networking dinner'],
            birthday: ['Menu urodzinowe', 'Bufet party', 'Menu tematyczne'],
            conference: ['Catering konferencyjny', 'Lunch boxes', 'Networking buffet']
        };

        const menus = menuTypes[eventData.type] || menuTypes.wedding;
        const selectedMenu = menus[Math.floor(Math.random() * menus.length)];
        
        return {
            name: selectedMenu,
            description: this.getCateringDescription(eventData.type, selectedMenu),
            pricePerPerson: this.calculateCateringPrice(eventData),
            totalPrice: this.calculateCateringPrice(eventData) * eventData.guests,
            dietary: ['Standard', 'Wegetariańskie', 'Wegańskie', 'Bezglutenowe'],
            service: this.getCateringService(eventData.type),
            rating: (4.3 + Math.random() * 0.6).toFixed(1)
        };
    }

    parseEntertainmentRecommendation(text, eventData) {
        const entertainmentTypes = {
            wedding: ['Zespół + DJ', 'DJ + Wodzirej', 'Zespół muzyczny live'],
            corporate: ['Prelegent motywacyjny', 'Team building', 'Networking mixer'],
            birthday: ['DJ + Karaoke', 'Zespół cover', 'DJ + Animacje'],
            conference: ['Keynote speaker', 'Panel dyskusyjny', 'Networking event']
        };

        const entertainment = entertainmentTypes[eventData.type] || entertainmentTypes.wedding;
        const selected = entertainment[Math.floor(Math.random() * entertainment.length)];
        
        return {
            name: selected,
            description: this.getEntertainmentDescription(eventData.type, selected),
            price: this.calculateEntertainmentPrice(eventData),
            duration: this.getEntertainmentDuration(eventData.duration),
            equipment: this.getRequiredEquipment(selected),
            rating: (4.4 + Math.random() * 0.5).toFixed(1)
        };
    }

    parseBudgetTips(text, eventData) {
        const baseTips = [
            'Zarezerwuj usługi z 2-3 miesięcznym wyprzedzeniem dla lepszych cen',
            'Rozważ wydarzenia w dni robocze - często 20-30% taniej',
            'Negocjuj pakiety - łączenie usług daje oszczędności',
            'Sprawdź alternatywne lokalizacje w pobliżu głównych miast'
        ];

        const budgetSpecific = this.getBudgetSpecificTips(eventData);
        
        return [...baseTips, ...budgetSpecific];
    }

    parseTimelineRecommendation(text, eventData) {
        return this.generateEventTimeline(eventData);
    }

    // Budget Calculation Functions
    calculateVenuePrice(eventData) {
        const basePrices = {
            wedding: 300,
            corporate: 200,
            birthday: 150,
            conference: 400
        };
        
        const basePrice = basePrices[eventData.type] || 250;
        const guestMultiplier = Math.max(0.8, eventData.guests / 100);
        const locationMultiplier = this.getLocationMultiplier(eventData.location);
        
        return Math.round(basePrice * guestMultiplier * locationMultiplier);
    }

    calculateCateringPrice(eventData) {
        const basePrices = {
            wedding: 180,
            corporate: 85,
            birthday: 120,
            conference: 150
        };
        
        const basePrice = basePrices[eventData.type] || 150;
        const budgetFactor = eventData.budget / (eventData.guests * 500); // Budget per guest factor
        
        return Math.round(basePrice * Math.min(1.5, Math.max(0.7, budgetFactor)));
    }

    calculateEntertainmentPrice(eventData) {
        const basePrices = {
            wedding: 8000,
            corporate: 12000,
            birthday: 3500,
            conference: 25000
        };
        
        const basePrice = basePrices[eventData.type] || 8000;
        const durationMultiplier = this.getDurationMultiplier(eventData.duration);
        
        return Math.round(basePrice * durationMultiplier);
    }

    getLocationMultiplier(location) {
        const multipliers = {
            'warszawa': 1.3,
            'kraków': 1.2,
            'gdańsk': 1.1,
            'wrocław': 1.1,
            'poznań': 1.0,
            'łódź': 0.9
        };
        
        const city = location.toLowerCase();
        return multipliers[city] || 1.0;
    }

    getDurationMultiplier(duration) {
        const multipliers = {
            '2-4': 0.7,
            '4-6': 1.0,
            '6-8': 1.3,
            'full-day': 1.5,
            'multi-day': 2.0
        };
        
        return multipliers[duration] || 1.0;
    }

    // AI-powered Budget Optimization
    async optimizeBudget(eventData, constraints = {}) {
        try {
            const prompt = this.config.formatPrompt('budget_optimization', {
                budget: eventData.budget,
                guests: eventData.guests,
                eventType: eventData.type,
                constraints: JSON.stringify(constraints)
            });

            const messages = [
                {
                    role: "system",
                    content: "Jesteś ekspertem finansowym od wydarzeń w Polsce. Optymalizujesz budżety wydarzeń i odpowiadasz w formacie JSON."
                },
                {
                    role: "user",
                    content: prompt
                }
            ];

            const response = await this.makeLlamaRequest(messages, {
                temperature: 0.3,
                max_tokens: 800
            });

            return this.parseBudgetOptimization(response, eventData);
        } catch (error) {
            this.config.error('Budget optimization error:', error);
            return this.getFallbackBudgetOptimization(eventData);
        }
    }

    buildBudgetOptimizationPrompt(eventData, constraints) {
        return `Optimize budget for ${eventData.type} event:
        Total budget: ${eventData.budget} PLN
        Guests: ${eventData.guests}
        Constraints: ${JSON.stringify(constraints)}
        
        Suggest percentage allocation for: venue, catering, entertainment, decorations, other.
        Format: venue:X%,catering:Y%,entertainment:Z%,decorations:W%,other:V%`;
    }

    parseBudgetOptimization(response, eventData) {
        try {
            let content;
            
            if (response.choices && response.choices[0]) {
                content = response.choices[0].message.content;
            } else if (response.generated_text) {
                content = response.generated_text;
            } else {
                throw new Error('Invalid response format');
            }

            this.config.log('Parsing budget optimization:', content.substring(0, 100) + '...');

            let allocations;
            try {
                const jsonMatch = content.match(/\{.*\}/s);
                if (jsonMatch) {
                    allocations = JSON.parse(jsonMatch[0]);
                } else {
                    allocations = this.extractBudgetAllocations(content);
                }
            } catch (parseError) {
                allocations = this.extractBudgetAllocations(content);
            }
            
            return {
                allocations,
                recommendations: this.generateBudgetRecommendations(allocations, eventData),
                potentialSavings: this.calculatePotentialSavings(allocations, eventData),
                riskAssessment: this.assessBudgetRisks(allocations, eventData),
                aiGenerated: true
            };
        } catch (error) {
            this.config.error('Failed to parse budget optimization:', error);
            return this.getFallbackBudgetOptimization(eventData);
        }
    }

    extractBudgetAllocations(text) {
        const defaultAllocations = {
            venue: 25,
            catering: 40,
            entertainment: 20,
            decorations: 10,
            other: 5
        };

        try {
            const matches = text.match(/(\w+):(\d+)%/g);
            if (matches) {
                const allocations = {};
                matches.forEach(match => {
                    const [, category, percentage] = match.match(/(\w+):(\d+)%/);
                    allocations[category.toLowerCase()] = parseInt(percentage);
                });
                return { ...defaultAllocations, ...allocations };
            }
        } catch (error) {
            console.warn('Failed to parse budget allocations:', error);
        }

        return defaultAllocations;
    }

    // Guest Management AI
    async analyzeGuestCompatibility(guestList) {
        try {
            const compatibilityPrompt = this.buildCompatibilityPrompt(guestList);
            const response = await this.makeRequest(this.models.sentiment, {
                inputs: compatibilityPrompt
            });

            return this.parseCompatibilityAnalysis(response, guestList);
        } catch (error) {
            return this.getFallbackCompatibilityAnalysis(guestList);
        }
    }

    buildCompatibilityPrompt(guestList) {
        const guestInfo = guestList.map(guest => 
            `${guest.name}: ${guest.age}, ${guest.interests}, ${guest.relationship}`
        ).join('. ');
        
        return `Analyze guest compatibility for seating: ${guestInfo}`;
    }

    parseCompatibilityAnalysis(response, guestList) {
        // Simulate compatibility scoring
        const compatibility = guestList.map(guest => ({
            ...guest,
            compatibilityScore: Math.random() * 0.4 + 0.6, // 0.6-1.0
            suggestedTable: Math.floor(Math.random() * Math.ceil(guestList.length / 8)) + 1,
            socialConnections: this.findSocialConnections(guest, guestList)
        }));

        return {
            overallCompatibility: this.calculateOverallCompatibility(compatibility),
            tableArrangements: this.optimizeTableArrangements(compatibility),
            potentialIssues: this.identifyPotentialIssues(compatibility),
            recommendations: this.generateSeatingRecommendations(compatibility)
        };
    }

    // Weather and Risk Assessment
    async analyzeEventRisks(eventData) {
        try {
            const riskPrompt = this.buildRiskAssessmentPrompt(eventData);
            const response = await this.makeRequest(this.models.classification, {
                inputs: riskPrompt,
                parameters: {
                    candidate_labels: ["high_risk", "medium_risk", "low_risk"]
                }
            });

            return this.parseRiskAssessment(response, eventData);
        } catch (error) {
            return this.getFallbackRiskAssessment(eventData);
        }
    }

    buildRiskAssessmentPrompt(eventData) {
        return `Assess risk for ${eventData.type} event on ${eventData.date} 
        with ${eventData.guests} guests, budget ${eventData.budget} PLN in ${eventData.location}. 
        Consider weather, logistics, capacity, and seasonal factors.`;
    }

    parseRiskAssessment(response, eventData) {
        const riskFactors = this.identifyRiskFactors(eventData);
        const mitigationStrategies = this.generateMitigationStrategies(riskFactors, eventData);
        
        return {
            overallRisk: this.calculateOverallRisk(riskFactors),
            riskFactors,
            mitigationStrategies,
            contingencyPlans: this.generateContingencyPlans(eventData),
            recommendations: this.generateRiskRecommendations(riskFactors, eventData)
        };
    }

    // Timeline Generation
    generateEventTimeline(eventData) {
        const timelineTemplates = {
            wedding: this.getWeddingTimeline(),
            corporate: this.getCorporateTimeline(),
            birthday: this.getBirthdayTimeline(),
            conference: this.getConferenceTimeline()
        };

        const template = timelineTemplates[eventData.type] || timelineTemplates.wedding;
        return this.customizeTimeline(template, eventData);
    }

    getWeddingTimeline() {
        return [
            { phase: 'Planowanie', weeks: 24, tasks: ['Wybór miejsca', 'Catering', 'Muzyka'] },
            { phase: 'Przygotowania', weeks: 12, tasks: ['Zaproszenia', 'Dekoracje', 'Suknia'] },
            { phase: 'Finalizacja', weeks: 4, tasks: ['Próba', 'Ostatnie szczegóły', 'Lista gości'] },
            { phase: 'Dzień ślubu', weeks: 0, tasks: ['Ceremonia', 'Wesele', 'Dokumentacja'] }
        ];
    }

    getCorporateTimeline() {
        return [
            { phase: 'Planowanie', weeks: 12, tasks: ['Cel eventu', 'Budżet', 'Miejsce'] },
            { phase: 'Organizacja', weeks: 8, tasks: ['Prelegenci', 'Catering', 'Materiały'] },
            { phase: 'Promocja', weeks: 4, tasks: ['Zaproszenia', 'Marketing', 'Rejestracja'] },
            { phase: 'Realizacja', weeks: 0, tasks: ['Event', 'Networking', 'Follow-up'] }
        ];
    }

    getBirthdayTimeline() {
        return [
            { phase: 'Planowanie', weeks: 6, tasks: ['Koncepcja', 'Miejsce', 'Lista gości'] },
            { phase: 'Organizacja', weeks: 3, tasks: ['Catering', 'Rozrywka', 'Dekoracje'] },
            { phase: 'Przygotowania', weeks: 1, tasks: ['Zaproszenia', 'Zakupy', 'Przygotowanie'] },
            { phase: 'Impreza', weeks: 0, tasks: ['Przywitanie', 'Program', 'Zabawa'] }
        ];
    }

    getConferenceTimeline() {
        return [
            { phase: 'Koncepcja', weeks: 20, tasks: ['Temat', 'Prelegenci', 'Sponsorzy'] },
            { phase: 'Organizacja', weeks: 12, tasks: ['Miejsce', 'Agenda', 'Technologia'] },
            { phase: 'Promocja', weeks: 8, tasks: ['Marketing', 'Rejestracja', 'Media'] },
            { phase: 'Realizacja', weeks: 0, tasks: ['Konferencja', 'Networking', 'Podsumowanie'] }
        ];
    }

    customizeTimeline(template, eventData) {
        return template.map(phase => ({
            ...phase,
            customizedTasks: this.customizeTimelineTasks(phase.tasks, eventData),
            deadline: this.calculateDeadline(phase.weeks, eventData.date),
            priority: this.calculatePhasePriority(phase, eventData),
            estimatedCost: this.estimatePhaseCost(phase, eventData)
        }));
    }

    customizeTimelineTasks(tasks, eventData) {
        return tasks.map(task => ({
            name: task,
            description: this.getTaskDescription(task, eventData),
            estimatedHours: this.estimateTaskHours(task, eventData),
            dependencies: this.getTaskDependencies(task),
            priority: this.getTaskPriority(task, eventData)
        }));
    }

    // Vendor Matching AI
    async findOptimalVendors(requirements) {
        try {
            const matchingPrompt = this.buildVendorMatchingPrompt(requirements);
            const response = await this.makeRequest(this.models.embedding, {
                inputs: matchingPrompt
            });

            return this.parseVendorMatching(response, requirements);
        } catch (error) {
            return this.getFallbackVendorMatching(requirements);
        }
    }

    buildVendorMatchingPrompt(requirements) {
        return `Find vendors for ${requirements.type} event:
        Budget: ${requirements.budget}
        Location: ${requirements.location}
        Date: ${requirements.date}
        Guests: ${requirements.guests}
        Preferences: ${requirements.preferences?.join(', ') || 'none'}`;
    }

    parseVendorMatching(response, requirements) {
        // Simulate vendor database with scoring
        const vendors = this.getVendorDatabase();
        const matchedVendors = vendors
            .filter(vendor => this.isVendorCompatible(vendor, requirements))
            .map(vendor => ({
                ...vendor,
                matchScore: this.calculateVendorMatchScore(vendor, requirements),
                estimatedCost: this.estimateVendorCost(vendor, requirements),
                availability: this.checkVendorAvailability(vendor, requirements.date)
            }))
            .sort((a, b) => b.matchScore - a.matchScore)
            .slice(0, 10);

        return {
            vendors: matchedVendors,
            totalEstimatedCost: matchedVendors.reduce((sum, v) => sum + v.estimatedCost, 0),
            recommendations: this.generateVendorRecommendations(matchedVendors, requirements),
            alternatives: this.findVendorAlternatives(matchedVendors, requirements)
        };
    }

    // Fallback Functions for when AI is unavailable
    getFallbackResponse(model, payload) {
        if (model.includes('sentiment')) {
            return [{ label: "POSITIVE", score: 0.7 }];
        }
        
        if (model.includes('classification')) {
            return { labels: ["medium_risk"], scores: [0.8] };
        }
        
        return [{ generated_text: "AI service temporarily unavailable. Using fallback recommendations." }];
    }

    getFallbackEventRecommendations(eventData) {
        return {
            venue: {
                name: `${eventData.location} Event Center`,
                description: `Profesjonalne miejsce na ${eventData.type}`,
                price: this.calculateVenuePrice(eventData),
                rating: "4.5",
                features: this.getVenueFeatures(eventData.type)
            },
            catering: {
                name: "Menu Premium",
                description: "Wysokiej jakości catering",
                pricePerPerson: this.calculateCateringPrice(eventData),
                rating: "4.3"
            },
            entertainment: {
                name: "Profesjonalna rozrywka",
                description: "Dostosowana do typu wydarzenia",
                price: this.calculateEntertainmentPrice(eventData),
                rating: "4.4"
            },
            tips: this.getBudgetSpecificTips(eventData),
            timeline: this.generateEventTimeline(eventData),
            confidence: 0.6
        };
    }

    getFallbackBudgetOptimization(eventData) {
        const allocations = {
            venue: 25,
            catering: 40,
            entertainment: 20,
            decorations: 10,
            other: 5
        };

        return {
            allocations,
            recommendations: this.generateBudgetRecommendations(allocations, eventData),
            potentialSavings: Math.round(eventData.budget * 0.15),
            riskAssessment: "medium"
        };
    }

    getFallbackCompatibilityAnalysis(guestList) {
        return {
            overallCompatibility: 0.8,
            tableArrangements: this.generateRandomTableArrangements(guestList),
            potentialIssues: [],
            recommendations: ["Mieszaj grupy wiekowe", "Uwzględnij zainteresowania"]
        };
    }

    getFallbackRiskAssessment(eventData) {
        return {
            overallRisk: "medium",
            riskFactors: this.identifyRiskFactors(eventData),
            mitigationStrategies: this.generateMitigationStrategies([], eventData),
            contingencyPlans: this.generateContingencyPlans(eventData),
            recommendations: ["Przygotuj plan B", "Monitoruj pogodę"]
        };
    }

    getFallbackVendorMatching(requirements) {
        const vendors = this.getVendorDatabase()
            .filter(v => this.isVendorCompatible(v, requirements))
            .slice(0, 5);

        return {
            vendors,
            totalEstimatedCost: vendors.reduce((sum, v) => sum + (v.basePrice || 5000), 0),
            recommendations: ["Porównaj oferty", "Sprawdź referencje"],
            alternatives: []
        };
    }

    // Helper Functions
    getVenueFeatures(eventType) {
        const features = {
            wedding: ['Sala balowa', 'Ogród', 'Parking', 'Noclegi'],
            corporate: ['Sala konferencyjna', 'Technologia AV', 'Catering', 'Parking'],
            birthday: ['Prywatna sala', 'Bar', 'Muzyka', 'Parking'],
            conference: ['Audytorium', 'Technologia', 'Catering', 'Stoiska']
        };
        
        return features[eventType] || features.wedding;
    }

    getCateringDescription(eventType, menuName) {
        const descriptions = {
            wedding: 'Eleganckie menu weselne z tradycyjnymi daniami',
            corporate: 'Profesjonalny catering biznesowy',
            birthday: 'Catering dostosowany do uroczystości',
            conference: 'Catering konferencyjny z coffee breaks'
        };
        
        return descriptions[eventType] || descriptions.wedding;
    }

    getEntertainmentDescription(eventType, name) {
        return `Profesjonalna rozrywka dostosowana do ${eventType}`;
    }

    getBudgetSpecificTips(eventData) {
        const budgetPerPerson = eventData.budget / eventData.guests;
        
        if (budgetPerPerson < 200) {
            return [
                'Rozważ mniejszą liczbę gości dla lepszej jakości',
                'Szukaj promocji i pakietów',
                'Wybierz dzień roboczy zamiast weekendu'
            ];
        } else if (budgetPerPerson > 500) {
            return [
                'Możesz pozwolić sobie na premium opcje',
                'Rozważ dodatkowe atrakcje',
                'Inwestuj w dokumentację wydarzenia'
            ];
        }
        
        return [
            'Zrównoważony budżet pozwala na dobre opcje',
            'Priorytetyzuj najważniejsze elementy',
            'Negocjuj pakiety usług'
        ];
    }

    generateBudgetRecommendations(allocations, eventData) {
        const recommendations = [];
        
        if (allocations.catering > 50) {
            recommendations.push('Wysoki udział cateringu - rozważ prostsze menu');
        }
        
        if (allocations.venue < 20) {
            recommendations.push('Niski budżet na miejsce może ograniczyć opcje');
        }
        
        if (allocations.entertainment > 30) {
            recommendations.push('Duży budżet na rozrywkę - upewnij się, że to priorytet');
        }
        
        return recommendations;
    }

    calculatePotentialSavings(allocations, eventData) {
        const baseSavings = eventData.budget * 0.1; // 10% base savings
        const optimizationBonus = Object.values(allocations).some(a => a > 45) ? 0.05 : 0.15;
        
        return Math.round(baseSavings * (1 + optimizationBonus));
    }

    assessBudgetRisks(allocations, eventData) {
        const risks = [];
        
        if (allocations.other < 5) {
            risks.push('Bardzo niski bufor na nieprzewidziane wydatki');
        }
        
        if (allocations.venue + allocations.catering > 75) {
            risks.push('Wysoka koncentracja budżetu na miejsce i jedzenie');
        }
        
        return risks.length > 0 ? 'medium' : 'low';
    }

    identifyRiskFactors(eventData) {
        const risks = [];
        const eventDate = new Date(eventData.date);
        const now = new Date();
        const daysToEvent = Math.ceil((eventDate - now) / (1000 * 60 * 60 * 24));
        
        if (daysToEvent < 30) {
            risks.push({
                type: 'time',
                severity: 'high',
                description: 'Krótki czas do wydarzenia'
            });
        }
        
        if (eventData.guests > 200) {
            risks.push({
                type: 'logistics',
                severity: 'medium',
                description: 'Duża liczba gości wymaga lepszej organizacji'
            });
        }
        
        const budgetPerPerson = eventData.budget / eventData.guests;
        if (budgetPerPerson < 150) {
            risks.push({
                type: 'budget',
                severity: 'medium',
                description: 'Ograniczony budżet na osobę'
            });
        }
        
        return risks;
    }

    generateMitigationStrategies(riskFactors, eventData) {
        const strategies = [];
        
        riskFactors.forEach(risk => {
            switch (risk.type) {
                case 'time':
                    strategies.push('Priorytetyzuj najważniejsze zadania');
                    strategies.push('Rozważ profesjonalnego organizatora');
                    break;
                case 'logistics':
                    strategies.push('Przygotuj szczegółowy plan logistyczny');
                    strategies.push('Zaangażuj dodatkową obsługę');
                    break;
                case 'budget':
                    strategies.push('Optymalizuj koszty bez utraty jakości');
                    strategies.push('Rozważ sponsoring lub wsparcie');
                    break;
            }
        });
        
        return [...new Set(strategies)]; // Remove duplicates
    }

    generateContingencyPlans(eventData) {
        const plans = [
            {
                scenario: 'Niepogoda',
                action: 'Przygotuj plan indoor lub namioty'
            },
            {
                scenario: 'Choroba dostawcy',
                action: 'Lista zastępczych dostawców'
            },
            {
                scenario: 'Więcej gości niż spodziewanych',
                action: 'Elastyczny catering +10%'
            },
            {
                scenario: 'Problemy techniczne',
                action: 'Backup sprzętu i wsparcie tech'
            }
        ];
        
        return plans;
    }

    generateRiskRecommendations(riskFactors, eventData) {
        const recommendations = [
            'Przygotuj listę kontaktów awaryjnych',
            'Zarezerwuj 10% budżetu na nieprzewidziane wydatki',
            'Potwierdź wszystkie rezerwacje na tydzień przed eventem'
        ];
        
        if (riskFactors.length > 2) {
            recommendations.push('Rozważ zatrudnienie profesjonalnego koordinatora');
        }
        
        return recommendations;
    }

    getVendorDatabase() {
        return [
            {
                id: 1,
                name: 'Catering Premium Plus',
                type: 'catering',
                rating: 4.8,
                basePrice: 150,
                location: 'Warszawa',
                specialties: ['wedding', 'corporate'],
                capacity: { min: 50, max: 500 }
            },
            {
                id: 2,
                name: 'Hotel Royal Events',
                type: 'venue',
                rating: 4.6,
                basePrice: 8000,
                location: 'Warszawa',
                specialties: ['wedding', 'conference'],
                capacity: { min: 100, max: 300 }
            },
            {
                id: 3,
                name: 'DJ Music Pro',
                type: 'entertainment',
                rating: 4.9,
                basePrice: 2500,
                location: 'Warszawa',
                specialties: ['wedding', 'birthday'],
                capacity: { min: 20, max: 200 }
            }
            // Add more vendors as needed
        ];
    }

    isVendorCompatible(vendor, requirements) {
        return vendor.specialties.includes(requirements.type) &&
               vendor.capacity.min <= requirements.guests &&
               vendor.capacity.max >= requirements.guests;
    }

    calculateVendorMatchScore(vendor, requirements) {
        let score = vendor.rating / 5; // Base score from rating
        
        // Location match
        if (vendor.location.toLowerCase().includes(requirements.location.toLowerCase())) {
            score += 0.2;
        }
        
        // Specialty match
        if (vendor.specialties.includes(requirements.type)) {
            score += 0.3;
        }
        
        // Capacity match
        const capacityMatch = 1 - Math.abs(vendor.capacity.max - requirements.guests) / vendor.capacity.max;
        score += capacityMatch * 0.2;
        
        return Math.min(score, 1);
    }

    estimateVendorCost(vendor, requirements) {
        const baseCost = vendor.basePrice;
        
        if (vendor.type === 'catering') {
            return baseCost * requirements.guests;
        }
        
        return baseCost;
    }

    checkVendorAvailability(vendor, date) {
        // Simulate availability check
        return Math.random() > 0.3; // 70% chance of availability
    }

    generateVendorRecommendations(vendors, requirements) {
        const recommendations = [];
        
        if (vendors.length > 0) {
            recommendations.push(`Top choice: ${vendors[0].name} with ${vendors[0].matchScore.toFixed(1)} match score`);
        }
        
        if (vendors.length > 3) {
            recommendations.push('Multiple good options available - compare quotes');
        }
        
        return recommendations;
    }

    findVendorAlternatives(vendors, requirements) {
        // Return alternative vendors or suggestions
        return [
            'Rozważ vendors z sąsiednich miast dla lepszych cen',
            'Sprawdź nowych vendors z dobrymi recenzjami',
            'Rozważ łączenie usług od jednego dostawcy'
        ];
    }

    calculateDeadline(weeksFromEvent, eventDate) {
        const eventDateTime = new Date(eventDate);
        const deadline = new Date(eventDateTime);
        deadline.setDate(deadline.getDate() - (weeksFromEvent * 7));
        
        return deadline.toISOString().split('T')[0];
    }

    calculatePhasePriority(phase, eventData) {
        const priorities = {
            'Planowanie': 'high',
            'Organizacja': 'high', 
            'Przygotowania': 'medium',
            'Realizacja': 'critical'
        };
        
        return priorities[phase.phase] || 'medium';
    }

    estimatePhaseCost(phase, eventData) {
        const costPercentages = {
            'Planowanie': 0.05,
            'Organizacja': 0.70,
            'Przygotowania': 0.20,
            'Realizacja': 0.05
        };
        
        const percentage = costPercentages[phase.phase] || 0.25;
        return Math.round(eventData.budget * percentage);
    }

    getTaskDescription(task, eventData) {
        const descriptions = {
            'Wybór miejsca': `Znajdź idealne miejsce na ${eventData.type} dla ${eventData.guests} osób`,
            'Catering': `Wybierz catering dostosowany do ${eventData.type}`,
            'Muzyka': 'Zarezerwuj rozrywkę muzyczną',
            'Zaproszenia': 'Przygotuj i wyślij zaproszenia'
        };
        
        return descriptions[task] || `Wykonaj zadanie: ${task}`;
    }

    estimateTaskHours(task, eventData) {
        const baseHours = {
            'Wybór miejsca': 8,
            'Catering': 6,
            'Muzyka': 4,
            'Zaproszenia': 3
        };
        
        const hours = baseHours[task] || 2;
        const complexityMultiplier = eventData.guests > 100 ? 1.5 : 1;
        
        return Math.round(hours * complexityMultiplier);
    }

    getTaskDependencies(task) {
        const dependencies = {
            'Catering': ['Wybór miejsca'],
            'Muzyka': ['Wybór miejsca'],
            'Dekoracje': ['Wybór miejsca'],
            'Zaproszenia': ['Lista gości']
        };
        
        return dependencies[task] || [];
    }

    getTaskPriority(task, eventData) {
        const priorities = {
            'Wybór miejsca': 'critical',
            'Catering': 'high',
            'Muzyka': 'medium',
            'Zaproszenia': 'medium'
        };
        
        return priorities[task] || 'low';
    }

    // Additional utility methods
    findSocialConnections(guest, guestList) {
        // Simulate finding social connections
        return guestList
            .filter(g => g.id !== guest.id && Math.random() > 0.7)
            .map(g => g.name)
            .slice(0, 3);
    }

    calculateOverallCompatibility(compatibility) {
        const totalScore = compatibility.reduce((sum, guest) => sum + guest.compatibilityScore, 0);
        return totalScore / compatibility.length;
    }

    optimizeTableArrangements(compatibility) {
        const tablesNeeded = Math.ceil(compatibility.length / 8);
        const tables = Array.from({ length: tablesNeeded }, (_, i) => ({
            tableNumber: i + 1,
            guests: [],
            compatibilityScore: 0
        }));
        
        // Simple round-robin assignment
        compatibility.forEach((guest, index) => {
            const tableIndex = index % tablesNeeded;
            tables[tableIndex].guests.push(guest);
        });
        
        return tables;
    }

    identifyPotentialIssues(compatibility) {
        const issues = [];
        
        compatibility.forEach(guest => {
            if (guest.compatibilityScore < 0.7) {
                issues.push(`Potential seating challenge with ${guest.name}`);
            }
        });
        
        return issues;
    }

    generateSeatingRecommendations(compatibility) {
        return [
            'Mix age groups at each table',
            'Separate potential conflicts',
            'Place social connectors strategically',
            'Keep family groups together but not isolated'
        ];
    }

    generateRandomTableArrangements(guestList) {
        const tablesNeeded = Math.ceil(guestList.length / 8);
        return Array.from({ length: tablesNeeded }, (_, i) => ({
            tableNumber: i + 1,
            guests: guestList.slice(i * 8, (i + 1) * 8),
            compatibilityScore: 0.8
        }));
    }

    checkAvailability(date) {
        // Simulate availability checking
        const eventDate = new Date(date);
        const dayOfWeek = eventDate.getDay();
        
        // Weekends are busier
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            return Math.random() > 0.3 ? 'available' : 'limited';
        }
        
        return 'available';
    }

    getEntertainmentDuration(eventDuration) {
        const durations = {
            '2-4': '3 godziny',
            '4-6': '5 godzin',
            '6-8': '7 godzin',
            'full-day': '8 godzin',
            'multi-day': 'pakiet wielodniowy'
        };
        
        return durations[eventDuration] || '5 godzin';
    }

    getRequiredEquipment(entertainmentType) {
        const equipment = {
            'DJ + Karaoke': ['Konsola DJ', 'System karaoke', 'Mikrofony', 'Oświetlenie'],
            'Zespół + DJ': ['Instrumenty', 'Nagłośnienie', 'Oświetlenie sceniczne'],
            'Prelegent motywacyjny': ['Mikrofon bezprzewodowy', 'Projektor', 'Ekran']
        };
        
        return equipment[entertainmentType] || ['Podstawowe nagłośnienie'];
    }

    getCateringService(eventType) {
        const services = {
            wedding: 'Pełna obsługa kelnerska + koordinator',
            corporate: 'Obsługa biznesowa + coffee breaks',
            birthday: 'Obsługa kelnerska',
            conference: 'Catering + coffee breaks + lunch'
        };
        
        return services[eventType] || 'Standardowa obsługa';
    }
}

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIService;
} else {
    window.AIService = AIService;
}