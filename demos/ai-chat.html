<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SupportBot Pro - AI Customer Support</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        accent: '#06b6d4'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .typing-indicator {
            animation: typing 1.5s infinite;
        }
        @keyframes typing {
            0%, 60%, 100% { opacity: 1; }
            30% { opacity: 0.5; }
        }
        .message-animation {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex">
    <!-- Sidebar -->
    <div class="w-80 bg-white shadow-lg border-r">
        <!-- Header -->
        <div class="p-6 border-b bg-gradient-to-r from-primary to-secondary text-white">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                    <span class="text-xl">🤖</span>
                </div>
                <div>
                    <h1 class="font-bold text-lg">SupportBot Pro</h1>
                    <p class="text-sm text-white/80">AI Customer Support</p>
                </div>
            </div>
        </div>

        <!-- Conversation List -->
        <div class="p-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="font-semibold text-gray-900">Recent Conversations</h2>
                <button class="bg-primary text-white px-3 py-1 rounded-lg text-sm hover:bg-indigo-600 transition-colors">
                    + New Chat
                </button>
            </div>
            
            <div class="space-y-2">
                <div class="p-3 bg-primary/10 rounded-lg border-l-4 border-primary cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900">Product Support</p>
                            <p class="text-sm text-gray-600">How do I reset my password?</p>
                        </div>
                        <span class="text-xs text-gray-500">2m ago</span>
                    </div>
                </div>

                <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900">Billing Question</p>
                            <p class="text-sm text-gray-600">When is my next payment due?</p>
                        </div>
                        <span class="text-xs text-gray-500">1h ago</span>
                    </div>
                </div>

                <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900">Feature Request</p>
                            <p class="text-sm text-gray-600">Can you add dark mode?</p>
                        </div>
                        <span class="text-xs text-gray-500">3h ago</span>
                    </div>
                </div>

                <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900">Technical Issue</p>
                            <p class="text-sm text-gray-600">App crashes on startup</p>
                        </div>
                        <span class="text-xs text-gray-500">5h ago</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Summary -->
        <div class="p-4 border-t">
            <h3 class="font-semibold text-gray-900 mb-3">Today's Stats</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Conversations</span>
                    <span class="font-semibold text-gray-900">47</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Resolved</span>
                    <span class="font-semibold text-green-600">42</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Avg Response</span>
                    <span class="font-semibold text-gray-900">1.2s</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Satisfaction</span>
                    <span class="font-semibold text-yellow-600">4.8/5</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col">
        <!-- Chat Header -->
        <div class="p-6 bg-white border-b shadow-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                        <span class="text-white font-semibold">JS</span>
                    </div>
                    <div>
                        <h2 class="font-semibold text-gray-900">John Smith</h2>
                        <p class="text-sm text-gray-600">Customer • <EMAIL></p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                        Online
                    </span>
                    <button class="p-2 text-gray-400 hover:text-gray-600">
                        <span class="text-lg">⚙️</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="flex-1 overflow-y-auto p-6 space-y-4" id="chatMessages">
            <!-- Customer Message -->
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-sm font-semibold">JS</span>
                </div>
                <div class="flex-1">
                    <div class="bg-gray-100 rounded-lg p-3 max-w-md">
                        <p class="text-gray-900">Hi, I'm having trouble resetting my password. The reset email isn't coming through.</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">2:34 PM</p>
                </div>
            </div>

            <!-- AI Response -->
            <div class="flex items-start space-x-3 justify-end">
                <div class="flex-1 flex justify-end">
                    <div class="bg-gradient-to-r from-primary to-secondary text-white rounded-lg p-3 max-w-md">
                        <p>Hello John! I'd be happy to help you with your password reset. Let me check a few things:</p>
                        <ul class="mt-2 space-y-1">
                            <li>• Have you checked your spam/junk folder?</li>
                            <li>• Is the <NAME_EMAIL> correct?</li>
                            <li>• When did you last request the reset?</li>
                        </ul>
                    </div>
                </div>
                <div class="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                    <span class="text-white text-sm">🤖</span>
                </div>
            </div>

            <!-- Customer Response -->
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-sm font-semibold">JS</span>
                </div>
                <div class="flex-1">
                    <div class="bg-gray-100 rounded-lg p-3 max-w-md">
                        <p class="text-gray-900">I checked spam and the email is correct. I requested it about 10 minutes ago.</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">2:36 PM</p>
                </div>
            </div>

            <!-- AI Response with Actions -->
            <div class="flex items-start space-x-3 justify-end">
                <div class="flex-1 flex justify-end">
                    <div class="bg-gradient-to-r from-primary to-secondary text-white rounded-lg p-3 max-w-md">
                        <p class="mb-3">I can help you reset your password directly. I've sent a new reset link to your email. It should arrive within 2-3 minutes.</p>
                        <div class="space-y-2">
                            <button class="w-full bg-white/20 hover:bg-white/30 text-white py-2 px-3 rounded text-sm transition-colors">
                                📧 Resend Reset Email
                            </button>
                            <button class="w-full bg-white/20 hover:bg-white/30 text-white py-2 px-3 rounded text-sm transition-colors">
                                🔒 Reset Password Now
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                    <span class="text-white text-sm">🤖</span>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div class="flex items-start space-x-3" id="typingIndicator" style="display: none;">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-sm font-semibold">JS</span>
                </div>
                <div class="bg-gray-100 rounded-lg p-3">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full typing-indicator"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full typing-indicator" style="animation-delay: 0.2s;"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full typing-indicator" style="animation-delay: 0.4s;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Input -->
        <div class="p-6 bg-white border-t">
            <div class="flex items-center space-x-4">
                <div class="flex-1 relative">
                    <input
                        type="text"
                        id="messageInput"
                        placeholder="Type your message..."
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                        <span class="text-lg">📎</span>
                    </button>
                </div>
                <button
                    id="sendButton"
                    class="bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-lg hover:opacity-90 transition-opacity font-medium"
                >
                    Send
                </button>
            </div>
            
            <!-- Quick Responses -->
            <div class="mt-4">
                <p class="text-sm text-gray-600 mb-2">Quick responses:</p>
                <div class="flex flex-wrap gap-2">
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors quick-response">
                        Thank you for your help!
                    </button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors quick-response">
                        That worked perfectly
                    </button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors quick-response">
                        I need more help
                    </button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors quick-response">
                        Can I speak to a human?
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatMessages = document.getElementById('chatMessages');
        const typingIndicator = document.getElementById('typingIndicator');
        const quickResponses = document.querySelectorAll('.quick-response');

        // AI responses for demo
        const aiResponses = [
            "I understand your concern. Let me help you with that right away.",
            "That's a great question! Here's what I can do for you...",
            "I've processed your request. Is there anything else I can help you with?",
            "Thank you for providing that information. I'm working on a solution for you.",
            "I've escalated this to our technical team. You should hear back within 24 hours."
        ];

        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `flex items-start space-x-3 message-animation ${isUser ? '' : 'justify-end'}`;
            
            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            if (isUser) {
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-semibold">JS</span>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-lg p-3 max-w-md">
                            <p class="text-gray-900">${content}</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">${time}</p>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="flex-1 flex justify-end">
                        <div class="bg-gradient-to-r from-primary to-secondary text-white rounded-lg p-3 max-w-md">
                            <p>${content}</p>
                        </div>
                    </div>
                    <div class="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center">
                        <span class="text-white text-sm">🤖</span>
                    </div>
                `;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTyping() {
            typingIndicator.style.display = 'flex';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, true);
            messageInput.value = '';

            // Show typing indicator
            showTyping();

            // Simulate AI response delay
            setTimeout(() => {
                hideTyping();
                const response = aiResponses[Math.floor(Math.random() * aiResponses.length)];
                addMessage(response, false);
            }, 1000 + Math.random() * 2000);
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        quickResponses.forEach(button => {
            button.addEventListener('click', () => {
                messageInput.value = button.textContent;
                sendMessage();
            });
        });

        // Simulate periodic incoming messages
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance every 10 seconds
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    addMessage("Is there anything else I can help you with today?", false);
                }, 2000);
            }
        }, 10000);
    </script>
</body>
</html>
