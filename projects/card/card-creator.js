// Advanced Card Creator System for SmartCard.pl
class CardCreator {
    constructor() {
        this.currentCard = null;
        this.templates = this.initializeTemplates();
        this.init();
    }

    init() {
        this.setupCardCreator();
        this.initializeAIAvatar();
        this.setupFormValidation();
        this.setupAutoSave();
    }

    initializeTemplates() {
        return {
            modern: {
                name: 'Modern Glassmorphism',
                description: 'Przezroczyste elementy z efektem szkła',
                colors: {
                    primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    secondary: 'rgba(255, 255, 255, 0.1)',
                    text: '#ffffff',
                    accent: '#667eea'
                },
                layout: 'centered',
                animation: 'float'
            },
            neon: {
                name: '<PERSON><PERSON>',
                description: 'Świec<PERSON>ce neony w stylu cyberpunk',
                colors: {
                    primary: 'linear-gradient(135deg, #00f2fe 0%, #4facfe 100%)',
                    secondary: '#000000',
                    text: '#00f2fe',
                    accent: '#ff0080'
                },
                layout: 'split',
                animation: 'glow'
            },
            minimalist: {
                name: 'Minimalist Pro',
                description: 'Czysty design z subtelnymi animacjami',
                colors: {
                    primary: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                    secondary: '#ffffff',
                    text: '#2c3e50',
                    accent: '#3498db'
                },
                layout: 'minimal',
                animation: 'subtle'
            },
            creative: {
                name: 'Creative Burst',
                description: 'Artystyczne kształty i żywe kolory',
                colors: {
                    primary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    secondary: 'rgba(240, 147, 251, 0.1)',
                    text: '#ffffff',
                    accent: '#f5576c'
                },
                layout: 'artistic',
                animation: 'burst'
            },
            elegant: {
                name: 'Elegant Gold',
                description: 'Luksusowe złote akcenty',
                colors: {
                    primary: 'linear-gradient(135deg, #ffd89b 0%, #19547b 100%)',
                    secondary: '#1a1a1a',
                    text: '#ffd89b',
                    accent: '#d4af37'
                },
                layout: 'luxury',
                animation: 'elegant'
            },
            tech: {
                name: 'Tech Matrix',
                description: 'Futurystyczny styl high-tech',
                colors: {
                    primary: 'linear-gradient(135deg, #00ff88 0%, #00b4db 100%)',
                    secondary: '#0a0a0a',
                    text: '#00ff88',
                    accent: '#00b4db'
                },
                layout: 'matrix',
                animation: 'matrix'
            }
        };
    }

    setupCardCreator() {
        this.createCardCreatorModal();
        this.setupTemplateSelector();
        this.setupColorCustomizer();
        this.setupFontSelector();
        this.setupLayoutOptions();
    }

    createCardCreatorModal() {
        const creatorModal = document.createElement('div');
        creatorModal.id = 'cardCreatorModal';
        creatorModal.className = 'creator-modal-overlay hidden';
        creatorModal.innerHTML = `
            <div class="creator-modal">
                <div class="creator-header">
                    <div class="creator-title">
                        <h2><i class="fas fa-magic"></i> Kreator Wizytówek AI</h2>
                        <p>Stwórz profesjonalną wizytówkę w kilka minut</p>
                    </div>
                    <div class="creator-actions">
                        <button class="save-btn" id="saveCard" disabled>
                            <i class="fas fa-save"></i> Zapisz
                        </button>
                        <button class="close-creator" id="closeCreator">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="creator-content">
                    <div class="creator-sidebar">
                        <div class="creator-tabs">
                            <button class="creator-tab active" data-tab="template">
                                <i class="fas fa-palette"></i>
                                <span>Szablon</span>
                            </button>
                            <button class="creator-tab" data-tab="content">
                                <i class="fas fa-edit"></i>
                                <span>Treść</span>
                            </button>
                            <button class="creator-tab" data-tab="avatar">
                                <i class="fas fa-user-circle"></i>
                                <span>Avatar AI</span>
                            </button>
                            <button class="creator-tab" data-tab="customize">
                                <i class="fas fa-cog"></i>
                                <span>Dostosuj</span>
                            </button>
                            <button class="creator-tab" data-tab="export">
                                <i class="fas fa-download"></i>
                                <span>Eksport</span>
                            </button>
                        </div>
                        
                        <!-- Template Selection -->
                        <div class="creator-panel active" data-panel="template">
                            <div class="panel-header">
                                <h3>Wybierz szablon</h3>
                                <p>Każdy szablon ma unikalną animację</p>
                            </div>
                            <div class="templates-grid" id="templatesGrid">
                                ${this.renderTemplateOptions()}
                            </div>
                        </div>
                        
                        <!-- Content Panel -->
                        <div class="creator-panel" data-panel="content">
                            <div class="panel-header">
                                <h3>Dane osobowe</h3>
                                <p>Wypełnij informacje na wizytówce</p>
                            </div>
                            <form class="card-form" id="cardForm">
                                <div class="form-section">
                                    <label>Imię i nazwisko *</label>
                                    <input type="text" id="cardName" placeholder="Jan Kowalski" required>
                                </div>
                                
                                <div class="form-section">
                                    <label>Stanowisko</label>
                                    <input type="text" id="cardTitle" placeholder="CEO & Founder">
                                </div>
                                
                                <div class="form-section">
                                    <label>Firma</label>
                                    <input type="text" id="cardCompany" placeholder="Innovation Tech">
                                </div>
                                
                                <div class="form-section">
                                    <label>Email *</label>
                                    <input type="email" id="cardEmail" placeholder="<EMAIL>" required>
                                </div>
                                
                                <div class="form-section">
                                    <label>Telefon</label>
                                    <input type="tel" id="cardPhone" placeholder="+48 ***********">
                                </div>
                                
                                <div class="form-section">
                                    <label>Strona internetowa</label>
                                    <input type="url" id="cardWebsite" placeholder="https://innovation.tech">
                                </div>
                                
                                <div class="form-section">
                                    <label>LinkedIn</label>
                                    <input type="url" id="cardLinkedin" placeholder="https://linkedin.com/in/jankowalski">
                                </div>
                                
                                <div class="form-section">
                                    <label>Instagram</label>
                                    <input type="text" id="cardInstagram" placeholder="@jankowalski">
                                </div>
                                
                                <div class="form-section">
                                    <label>Twitter</label>
                                    <input type="text" id="cardTwitter" placeholder="@jankowalski">
                                </div>
                                
                                <div class="form-section">
                                    <label>Opis (max 100 znaków)</label>
                                    <textarea id="cardDescription" placeholder="Krótki opis działalności..." maxlength="100"></textarea>
                                    <div class="char-counter">
                                        <span id="charCount">0</span>/100
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Avatar AI Panel -->
                        <div class="creator-panel" data-panel="avatar">
                            <div class="panel-header">
                                <h3>Generator Avatara AI</h3>
                                <p>Stwórz unikalny avatar z AI</p>
                            </div>
                            
                            <div class="avatar-generator">
                                <div class="avatar-upload-section">
                                    <div class="avatar-preview" id="avatarPreview">
                                        <div class="avatar-placeholder">
                                            <i class="fas fa-camera"></i>
                                            <span>Prześlij zdjęcie</span>
                                        </div>
                                    </div>
                                    
                                    <input type="file" id="avatarUpload" accept="image/*" hidden>
                                    <button class="upload-avatar-btn" onclick="document.getElementById('avatarUpload').click()">
                                        <i class="fas fa-upload"></i> Prześlij zdjęcie
                                    </button>
                                </div>
                                
                                <div class="avatar-styles">
                                    <h4>Styl avatara</h4>
                                    <div class="style-options">
                                        <button class="style-option active" data-style="professional">
                                            <div class="style-preview professional"></div>
                                            <span>Professional</span>
                                        </button>
                                        <button class="style-option" data-style="cartoon">
                                            <div class="style-preview cartoon"></div>
                                            <span>Cartoon</span>
                                        </button>
                                        <button class="style-option" data-style="abstract">
                                            <div class="style-preview abstract"></div>
                                            <span>Abstract</span>
                                        </button>
                                        <button class="style-option" data-style="minimal">
                                            <div class="style-preview minimal"></div>
                                            <span>Minimal</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="ai-options">
                                    <h4>Opcje AI</h4>
                                    <div class="ai-controls">
                                        <label class="ai-control">
                                            <input type="checkbox" id="enhanceColors" checked>
                                            <span>Popraw kolory</span>
                                        </label>
                                        <label class="ai-control">
                                            <input type="checkbox" id="removeBackground" checked>
                                            <span>Usuń tło</span>
                                        </label>
                                        <label class="ai-control">
                                            <input type="checkbox" id="addEffects">
                                            <span>Dodaj efekty</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <button class="generate-avatar-btn" id="generateAvatar" disabled>
                                    <i class="fas fa-magic"></i> Generuj Avatar AI
                                </button>
                            </div>
                        </div>
                        
                        <!-- Customize Panel -->
                        <div class="creator-panel" data-panel="customize">
                            <div class="panel-header">
                                <h3>Dostosuj design</h3>
                                <p>Personalizuj wygląd wizytówki</p>
                            </div>
                            
                            <div class="customization-options">
                                <div class="option-group">
                                    <h4>Kolory</h4>
                                    <div class="color-palette">
                                        <div class="color-preset active" data-colors="default">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #667eea, #764ba2)"></div>
                                            <span>Domyślny</span>
                                        </div>
                                        <div class="color-preset" data-colors="ocean">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #00c6ff, #0072ff)"></div>
                                            <span>Ocean</span>
                                        </div>
                                        <div class="color-preset" data-colors="sunset">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #ff9a9e, #fecfef)"></div>
                                            <span>Sunset</span>
                                        </div>
                                        <div class="color-preset" data-colors="forest">
                                            <div class="color-preview" style="background: linear-gradient(45deg, #43e97b, #38f9d7)"></div>
                                            <span>Forest</span>
                                        </div>
                                    </div>
                                    
                                    <div class="custom-colors">
                                        <label>Kolor główny</label>
                                        <input type="color" id="primaryColor" value="#667eea">
                                        
                                        <label>Kolor akcentu</label>
                                        <input type="color" id="accentColor" value="#764ba2">
                                    </div>
                                </div>
                                
                                <div class="option-group">
                                    <h4>Czcionka</h4>
                                    <select id="fontFamily" class="font-selector">
                                        <option value="Inter">Inter (Domyślna)</option>
                                        <option value="Roboto">Roboto</option>
                                        <option value="Poppins">Poppins</option>
                                        <option value="Montserrat">Montserrat</option>
                                        <option value="Playfair Display">Playfair Display</option>
                                        <option value="Lato">Lato</option>
                                    </select>
                                </div>
                                
                                <div class="option-group">
                                    <h4>Layout</h4>
                                    <div class="layout-options">
                                        <button class="layout-option active" data-layout="centered">
                                            <div class="layout-preview centered-preview"></div>
                                            <span>Centered</span>
                                        </button>
                                        <button class="layout-option" data-layout="split">
                                            <div class="layout-preview split-preview"></div>
                                            <span>Split</span>
                                        </button>
                                        <button class="layout-option" data-layout="minimal">
                                            <div class="layout-preview minimal-preview"></div>
                                            <span>Minimal</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="option-group">
                                    <h4>Animacje</h4>
                                    <div class="animation-controls">
                                        <label class="toggle-control">
                                            <input type="checkbox" id="enableAnimations" checked>
                                            <span class="toggle-slider"></span>
                                            <span>Włącz animacje</span>
                                        </label>
                                        
                                        <select id="animationType" class="animation-selector">
                                            <option value="float">Float</option>
                                            <option value="glow">Glow</option>
                                            <option value="pulse">Pulse</option>
                                            <option value="rotate">Rotate</option>
                                            <option value="none">Brak</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Export Panel -->
                        <div class="creator-panel" data-panel="export">
                            <div class="panel-header">
                                <h3>Eksport i udostępnienie</h3>
                                <p>Pobierz lub udostępnij wizytówkę</p>
                            </div>
                            
                            <div class="export-options">
                                <div class="export-formats">
                                    <h4>Format eksportu</h4>
                                    <div class="format-buttons">
                                        <button class="format-btn" data-format="png">
                                            <i class="fas fa-image"></i>
                                            <span>PNG</span>
                                            <small>Najlepsza jakość</small>
                                        </button>
                                        <button class="format-btn" data-format="jpg">
                                            <i class="fas fa-file-image"></i>
                                            <span>JPG</span>
                                            <small>Mniejszy rozmiar</small>
                                        </button>
                                        <button class="format-btn" data-format="pdf">
                                            <i class="fas fa-file-pdf"></i>
                                            <span>PDF</span>
                                            <small>Do druku</small>
                                        </button>
                                        <button class="format-btn" data-format="svg">
                                            <i class="fas fa-vector-square"></i>
                                            <span>SVG</span>
                                            <small>Skalowalna</small>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="export-sizes">
                                    <h4>Rozmiar</h4>
                                    <select id="exportSize" class="size-selector">
                                        <option value="business">Wizytówka (85x55mm)</option>
                                        <option value="social">Social Media (1080x1080px)</option>
                                        <option value="email">Email Signature (600x200px)</option>
                                        <option value="custom">Niestandardowy</option>
                                    </select>
                                    
                                    <div class="custom-size hidden" id="customSizeInputs">
                                        <div class="size-inputs">
                                            <input type="number" id="customWidth" placeholder="Szerokość">
                                            <span>×</span>
                                            <input type="number" id="customHeight" placeholder="Wysokość">
                                            <select id="sizeUnit">
                                                <option value="px">px</option>
                                                <option value="mm">mm</option>
                                                <option value="in">in</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="qr-options">
                                    <h4>Kod QR</h4>
                                    <label class="toggle-control">
                                        <input type="checkbox" id="includeQR" checked>
                                        <span class="toggle-slider"></span>
                                        <span>Dołącz kod QR</span>
                                    </label>
                                    
                                    <div class="qr-content" id="qrContent">
                                        <label>Zawartość QR</label>
                                        <select id="qrType" class="qr-type-selector">
                                            <option value="vcard">vCard (Kontakt)</option>
                                            <option value="url">URL strony</option>
                                            <option value="linkedin">Profil LinkedIn</option>
                                            <option value="email">Email</option>
                                            <option value="phone">Telefon</option>
                                            <option value="custom">Niestandardowy</option>
                                        </select>
                                        
                                        <input type="text" id="customQRContent" placeholder="Niestandardowa zawartość QR" class="hidden">
                                    </div>
                                </div>
                                
                                <div class="sharing-options">
                                    <h4>Udostępnienie</h4>
                                    <div class="sharing-buttons">
                                        <button class="share-btn" data-platform="linkedin">
                                            <i class="fab fa-linkedin"></i>
                                            LinkedIn
                                        </button>
                                        <button class="share-btn" data-platform="twitter">
                                            <i class="fab fa-twitter"></i>
                                            Twitter
                                        </button>
                                        <button class="share-btn" data-platform="facebook">
                                            <i class="fab fa-facebook"></i>
                                            Facebook
                                        </button>
                                        <button class="share-btn" data-platform="email">
                                            <i class="fas fa-envelope"></i>
                                            Email
                                        </button>
                                    </div>
                                    
                                    <div class="public-link">
                                        <label>Link publiczny</label>
                                        <div class="link-input-group">
                                            <input type="text" id="publicLink" readonly placeholder="Wygenerowany zostanie po zapisaniu">
                                            <button class="copy-link-btn" id="copyLink" disabled>
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="export-actions">
                                    <button class="export-btn primary" id="downloadCard">
                                        <i class="fas fa-download"></i>
                                        Pobierz wizytówkę
                                    </button>
                                    <button class="export-btn secondary" id="previewCard">
                                        <i class="fas fa-eye"></i>
                                        Podgląd
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Live Preview -->
                    <div class="creator-preview">
                        <div class="preview-header">
                            <h3>Podgląd na żywo</h3>
                            <div class="preview-controls">
                                <button class="preview-control" id="flipCardPreview" title="Obróć wizytówkę">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="preview-control" id="fullscreenPreview" title="Pełny ekran">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="preview-control" id="mobilePreview" title="Widok mobilny">
                                    <i class="fas fa-mobile-alt"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="preview-container" id="previewContainer">
                            <div class="business-card-live" id="businessCardLive">
                                <div class="card-front-live">
                                    <!-- Dynamic content will be inserted here -->
                                </div>
                                <div class="card-back-live">
                                    <!-- QR code and additional info -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="preview-info">
                            <div class="info-item">
                                <span class="info-label">Szablon:</span>
                                <span class="info-value" id="currentTemplate">Modern Glassmorphism</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Rozmiar:</span>
                                <span class="info-value">85×55mm (standardowa wizytówka)</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Ostatnia zmiana:</span>
                                <span class="info-value" id="lastModified">Teraz</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(creatorModal);
        this.setupCreatorEventListeners();
    }

    renderTemplateOptions() {
        return Object.entries(this.templates).map(([key, template]) => `
            <div class="template-option ${key === 'modern' ? 'active' : ''}" data-template="${key}">
                <div class="template-preview-card">
                    <div class="template-preview-content" style="background: ${template.colors.primary}">
                        <div class="preview-elements">
                            <div class="preview-avatar"></div>
                            <div class="preview-text">
                                <div class="preview-name"></div>
                                <div class="preview-title"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="template-info">
                    <h4>${template.name}</h4>
                    <p>${template.description}</p>
                </div>
            </div>
        `).join('');
    }

    setupCreatorEventListeners() {
        // Modal controls
        document.getElementById('closeCreator').addEventListener('click', () => this.hideCreator());
        document.getElementById('cardCreatorModal').addEventListener('click', (e) => {
            if (e.target.id === 'cardCreatorModal') this.hideCreator();
        });

        // Tab switching
        document.querySelectorAll('.creator-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchCreatorTab(tab.dataset.tab));
        });

        // Template selection
        document.querySelectorAll('.template-option').forEach(option => {
            option.addEventListener('click', () => this.selectTemplate(option.dataset.template));
        });

        // Form inputs with real-time preview
        const formInputs = ['cardName', 'cardTitle', 'cardCompany', 'cardEmail', 'cardPhone', 'cardWebsite', 'cardLinkedin', 'cardInstagram', 'cardTwitter', 'cardDescription'];
        formInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', () => this.updateLivePreview());
                input.addEventListener('blur', () => this.saveToAutoSave());
            }
        });

        // Character counter for description
        document.getElementById('cardDescription').addEventListener('input', (e) => {
            document.getElementById('charCount').textContent = e.target.value.length;
        });

        // Avatar upload
        document.getElementById('avatarUpload').addEventListener('change', (e) => this.handleAvatarUpload(e));

        // Avatar style selection
        document.querySelectorAll('.style-option').forEach(option => {
            option.addEventListener('click', () => this.selectAvatarStyle(option.dataset.style));
        });

        // AI Avatar generation
        document.getElementById('generateAvatar').addEventListener('click', () => this.generateAIAvatar());

        // Color customization
        document.querySelectorAll('.color-preset').forEach(preset => {
            preset.addEventListener('click', () => this.selectColorPreset(preset.dataset.colors));
        });

        document.getElementById('primaryColor').addEventListener('change', (e) => this.updateCustomColors());
        document.getElementById('accentColor').addEventListener('change', (e) => this.updateCustomColors());

        // Font selection
        document.getElementById('fontFamily').addEventListener('change', (e) => this.updateFont(e.target.value));

        // Layout options
        document.querySelectorAll('.layout-option').forEach(option => {
            option.addEventListener('click', () => this.selectLayout(option.dataset.layout));
        });

        // Animation controls
        document.getElementById('enableAnimations').addEventListener('change', (e) => this.toggleAnimations(e.target.checked));
        document.getElementById('animationType').addEventListener('change', (e) => this.updateAnimationType(e.target.value));

        // Export controls
        document.querySelectorAll('.format-btn').forEach(btn => {
            btn.addEventListener('click', () => this.selectExportFormat(btn.dataset.format));
        });

        document.getElementById('exportSize').addEventListener('change', (e) => this.updateExportSize(e.target.value));
        document.getElementById('includeQR').addEventListener('change', (e) => this.toggleQRCode(e.target.checked));
        document.getElementById('qrType').addEventListener('change', (e) => this.updateQRType(e.target.value));

        // Preview controls
        document.getElementById('flipCardPreview').addEventListener('click', () => this.flipCardPreview());
        document.getElementById('fullscreenPreview').addEventListener('click', () => this.toggleFullscreenPreview());
        document.getElementById('mobilePreview').addEventListener('click', () => this.toggleMobilePreview());

        // Export actions
        document.getElementById('downloadCard').addEventListener('click', () => this.downloadCard());
        document.getElementById('previewCard').addEventListener('click', () => this.showPreviewModal());

        // Sharing
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', () => this.shareCard(btn.dataset.platform));
        });

        document.getElementById('copyLink').addEventListener('click', () => this.copyPublicLink());

        // Save functionality
        document.getElementById('saveCard').addEventListener('click', () => this.saveCard());
    }

    showCreator(cardData = null) {
        const modal = document.getElementById('cardCreatorModal');
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        // Load card data if editing
        if (cardData) {
            this.loadCardData(cardData);
        } else {
            this.resetForm();
        }
        
        this.updateLivePreview();
        
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    hideCreator() {
        const modal = document.getElementById('cardCreatorModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
    }

    switchCreatorTab(tab) {
        // Update tabs
        document.querySelectorAll('.creator-tab').forEach(t => t.classList.remove('active'));
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // Update panels
        document.querySelectorAll('.creator-panel').forEach(panel => panel.classList.remove('active'));
        document.querySelector(`[data-panel="${tab}"]`).classList.add('active');
    }

    selectTemplate(templateKey) {
        // Update template selection
        document.querySelectorAll('.template-option').forEach(option => option.classList.remove('active'));
        document.querySelector(`[data-template="${templateKey}"]`).classList.add('active');

        // Update current template
        this.currentTemplate = templateKey;
        document.getElementById('currentTemplate').textContent = this.templates[templateKey].name;

        // Update live preview
        this.updateLivePreview();
        this.enableSaveButton();
    }

    updateLivePreview() {
        const cardData = this.getFormData();
        const template = this.templates[this.currentTemplate || 'modern'];
        
        const frontCard = document.querySelector('.card-front-live');
        const backCard = document.querySelector('.card-back-live');
        
        // Generate front of card
        frontCard.innerHTML = this.generateCardHTML(cardData, template, 'front');
        
        // Generate back of card
        backCard.innerHTML = this.generateCardHTML(cardData, template, 'back');
        
        // Apply template styles
        this.applyTemplateStyles(template);
        
        // Update last modified
        document.getElementById('lastModified').textContent = new Date().toLocaleTimeString('pl-PL');
    }

    getFormData() {
        return {
            name: document.getElementById('cardName')?.value || '',
            title: document.getElementById('cardTitle')?.value || '',
            company: document.getElementById('cardCompany')?.value || '',
            email: document.getElementById('cardEmail')?.value || '',
            phone: document.getElementById('cardPhone')?.value || '',
            website: document.getElementById('cardWebsite')?.value || '',
            linkedin: document.getElementById('cardLinkedin')?.value || '',
            instagram: document.getElementById('cardInstagram')?.value || '',
            twitter: document.getElementById('cardTwitter')?.value || '',
            description: document.getElementById('cardDescription')?.value || '',
            avatar: this.currentAvatar || null
        };
    }

    generateCardHTML(data, template, side) {
        if (side === 'front') {
            return `
                <div class="card-layout ${template.layout}" style="background: ${template.colors.primary}">
                    <div class="card-avatar-section">
                        ${data.avatar ? 
                            `<img src="${data.avatar}" alt="${data.name}" class="card-avatar-img">` :
                            `<div class="card-avatar-placeholder">
                                <i class="fas fa-user"></i>
                            </div>`
                        }
                    </div>
                    
                    <div class="card-info-section">
                        <h2 class="card-name" style="color: ${template.colors.text}">${data.name || 'Twoje Imię'}</h2>
                        ${data.title ? `<p class="card-title" style="color: ${template.colors.text}; opacity: 0.8">${data.title}</p>` : ''}
                        ${data.company ? `<p class="card-company" style="color: ${template.colors.accent}">${data.company}</p>` : ''}
                        ${data.description ? `<p class="card-description" style="color: ${template.colors.text}; opacity: 0.7">${data.description}</p>` : ''}
                    </div>
                    
                    <div class="card-decoration">
                        <div class="decoration-element" style="background: ${template.colors.accent}"></div>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="card-layout back" style="background: ${template.colors.secondary}; border: 1px solid ${template.colors.accent}">
                    <div class="card-contacts">
                        ${data.email ? `
                            <div class="contact-item">
                                <i class="fas fa-envelope" style="color: ${template.colors.accent}"></i>
                                <span style="color: ${template.colors.text}">${data.email}</span>
                            </div>
                        ` : ''}
                        
                        ${data.phone ? `
                            <div class="contact-item">
                                <i class="fas fa-phone" style="color: ${template.colors.accent}"></i>
                                <span style="color: ${template.colors.text}">${data.phone}</span>
                            </div>
                        ` : ''}
                        
                        ${data.website ? `
                            <div class="contact-item">
                                <i class="fas fa-globe" style="color: ${template.colors.accent}"></i>
                                <span style="color: ${template.colors.text}">${this.formatWebsite(data.website)}</span>
                            </div>
                        ` : ''}
                        
                        ${data.linkedin ? `
                            <div class="contact-item">
                                <i class="fab fa-linkedin" style="color: ${template.colors.accent}"></i>
                                <span style="color: ${template.colors.text}">LinkedIn</span>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="card-qr-section">
                        <div class="qr-code-placeholder" style="border: 2px solid ${template.colors.accent}">
                            <i class="fas fa-qrcode" style="color: ${template.colors.accent}"></i>
                            <span style="color: ${template.colors.text}; font-size: 0.8rem;">QR Code</span>
                        </div>
                    </div>
                    
                    <div class="card-social">
                        ${data.instagram || data.twitter ? '<div class="social-icons">' : ''}
                        ${data.instagram ? `<i class="fab fa-instagram" style="color: ${template.colors.accent}"></i>` : ''}
                        ${data.twitter ? `<i class="fab fa-twitter" style="color: ${template.colors.accent}"></i>` : ''}
                        ${data.instagram || data.twitter ? '</div>' : ''}
                    </div>
                </div>
            `;
        }
    }

    formatWebsite(url) {
        return url.replace(/^https?:\/\//, '').replace(/\/$/, '');
    }

    applyTemplateStyles(template) {
        const liveCard = document.getElementById('businessCardLive');
        if (!liveCard) return;

        // Remove existing template classes
        liveCard.className = liveCard.className.replace(/template-\w+/g, '');
        
        // Add new template class
        liveCard.classList.add(`template-${this.currentTemplate}`);
        
        // Apply animation
        if (template.animation && document.getElementById('enableAnimations')?.checked) {
            liveCard.classList.add(`animation-${template.animation}`);
        }
    }

    handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file
        if (!file.type.startsWith('image/')) {
            this.showNotification('Wybierz plik obrazu', 'error');
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('Plik jest za duży (max 5MB)', 'error');
            return;
        }

        // Show upload progress
        this.showUploadProgress();

        // Process image
        const reader = new FileReader();
        reader.onload = (e) => {
            this.processUploadedImage(e.target.result);
            document.getElementById('generateAvatar').disabled = false;
        };
        reader.readAsDataURL(file);
    }

    processUploadedImage(imageSrc) {
        const preview = document.getElementById('avatarPreview');
        preview.innerHTML = `
            <img src="${imageSrc}" alt="Uploaded avatar" class="uploaded-avatar">
            <div class="avatar-overlay">
                <button class="change-avatar-btn" onclick="document.getElementById('avatarUpload').click()">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        `;
        
        this.uploadedImage = imageSrc;
        this.showNotification('Zdjęcie przesłane pomyślnie!', 'success');
    }

    selectAvatarStyle(style) {
        document.querySelectorAll('.style-option').forEach(option => option.classList.remove('active'));
        document.querySelector(`[data-style="${style}"]`).classList.add('active');
        
        this.selectedAvatarStyle = style;
    }

    async generateAIAvatar() {
        // Check if user has access to AI features
        if (!window.paymentSystem?.checkFeatureLimit('aiFeatures')) {
            window.paymentSystem?.showLimitWarning('aiFeatures');
            return;
        }

        if (!this.uploadedImage) {
            this.showNotification('Najpierw prześlij zdjęcie', 'error');
            return;
        }

        const generateBtn = document.getElementById('generateAvatar');
        const originalHTML = generateBtn.innerHTML;

        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generowanie AI...';

        try {
            // Simulate AI processing
            await this.simulateAIProcessing();

            // Generate avatar based on style
            const generatedAvatar = await this.createStylizedAvatar(this.uploadedImage, this.selectedAvatarStyle);

            this.currentAvatar = generatedAvatar;
            this.updateLivePreview();
            this.showNotification('Avatar AI wygenerowany pomyślnie!', 'success');

        } catch (error) {
            this.showNotification('Błąd podczas generowania avatara', 'error');
        } finally {
            generateBtn.disabled = false;
            generateBtn.innerHTML = originalHTML;
        }
    }

    simulateAIProcessing() {
        return new Promise((resolve) => {
            // Simulate different processing steps
            const steps = [
                'Analizowanie twarzy...',
                'Usuwanie tła...',
                'Poprawianie kolorów...',
                'Aplikowanie stylu...',
                'Finalizowanie...'
            ];
            
            let stepIndex = 0;
            const interval = setInterval(() => {
                if (stepIndex < steps.length) {
                    document.getElementById('generateAvatar').innerHTML = 
                        `<i class="fas fa-spinner fa-spin"></i> ${steps[stepIndex]}`;
                    stepIndex++;
                } else {
                    clearInterval(interval);
                    resolve();
                }
            }, 800);
        });
    }

    createStylizedAvatar(originalImage, style) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                canvas.width = 200;
                canvas.height = 200;
                
                // Draw original image
                ctx.drawImage(img, 0, 0, 200, 200);
                
                // Apply style filters
                this.applyAvatarStyle(ctx, style);
                
                resolve(canvas.toDataURL('image/png'));
            };
            
            img.src = originalImage;
        });
    }

    applyAvatarStyle(ctx, style) {
        const imageData = ctx.getImageData(0, 0, 200, 200);
        const data = imageData.data;
        
        switch (style) {
            case 'cartoon':
                // Reduce colors for cartoon effect
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.round(data[i] / 32) * 32; // Red
                    data[i + 1] = Math.round(data[i + 1] / 32) * 32; // Green
                    data[i + 2] = Math.round(data[i + 2] / 32) * 32; // Blue
                }
                break;
                
            case 'abstract':
                // Create abstract effect
                for (let i = 0; i < data.length; i += 4) {
                    const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
                    data[i] = avg > 128 ? 255 : 0; // Red
                    data[i + 1] = avg > 128 ? 255 : 0; // Green
                    data[i + 2] = avg > 128 ? 255 : 0; // Blue
                }
                break;
                
            case 'minimal':
                // Create minimal effect with reduced saturation
                for (let i = 0; i < data.length; i += 4) {
                    const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
                    data[i] = (data[i] + avg) / 2; // Red
                    data[i + 1] = (data[i + 1] + avg) / 2; // Green
                    data[i + 2] = (data[i + 2] + avg) / 2; // Blue
                }
                break;
                
            default: // professional
                // Enhance contrast
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] * 1.2); // Red
                    data[i + 1] = Math.min(255, data[i + 1] * 1.2); // Green
                    data[i + 2] = Math.min(255, data[i + 2] * 1.2); // Blue
                }
                break;
        }
        
        ctx.putImageData(imageData, 0, 0);
    }

    selectColorPreset(preset) {
        document.querySelectorAll('.color-preset').forEach(p => p.classList.remove('active'));
        document.querySelector(`[data-colors="${preset}"]`).classList.add('active');
        
        // Update template colors
        const colorMap = {
            default: { primary: '#667eea', accent: '#764ba2' },
            ocean: { primary: '#00c6ff', accent: '#0072ff' },
            sunset: { primary: '#ff9a9e', accent: '#fecfef' },
            forest: { primary: '#43e97b', accent: '#38f9d7' }
        };
        
        if (colorMap[preset]) {
            document.getElementById('primaryColor').value = colorMap[preset].primary;
            document.getElementById('accentColor').value = colorMap[preset].accent;
            this.updateCustomColors();
        }
    }

    updateCustomColors() {
        const primaryColor = document.getElementById('primaryColor').value;
        const accentColor = document.getElementById('accentColor').value;
        
        // Update current template colors
        if (this.templates[this.currentTemplate]) {
            this.templates[this.currentTemplate].colors.primary = primaryColor;
            this.templates[this.currentTemplate].colors.accent = accentColor;
        }
        
        this.updateLivePreview();
        this.enableSaveButton();
    }

    updateFont(fontFamily) {
        const liveCard = document.getElementById('businessCardLive');
        if (liveCard) {
            liveCard.style.fontFamily = fontFamily;
        }
        this.enableSaveButton();
    }

    selectLayout(layout) {
        document.querySelectorAll('.layout-option').forEach(option => option.classList.remove('active'));
        document.querySelector(`[data-layout="${layout}"]`).classList.add('active');
        
        if (this.templates[this.currentTemplate]) {
            this.templates[this.currentTemplate].layout = layout;
        }
        
        this.updateLivePreview();
        this.enableSaveButton();
    }

    toggleAnimations(enabled) {
        const liveCard = document.getElementById('businessCardLive');
        if (liveCard) {
            if (enabled) {
                liveCard.classList.add('animations-enabled');
            } else {
                liveCard.classList.remove('animations-enabled');
            }
        }
    }

    updateAnimationType(type) {
        const liveCard = document.getElementById('businessCardLive');
        if (liveCard) {
            // Remove existing animation classes
            liveCard.className = liveCard.className.replace(/animation-\w+/g, '');
            
            if (type !== 'none') {
                liveCard.classList.add(`animation-${type}`);
            }
        }
    }

    flipCardPreview() {
        const liveCard = document.getElementById('businessCardLive');
        if (liveCard) {
            liveCard.classList.toggle('flipped');
        }
    }

    toggleFullscreenPreview() {
        const previewContainer = document.getElementById('previewContainer');
        if (previewContainer) {
            previewContainer.classList.toggle('fullscreen');
        }
    }

    toggleMobilePreview() {
        const previewContainer = document.getElementById('previewContainer');
        if (previewContainer) {
            previewContainer.classList.toggle('mobile-view');
        }
    }

    selectExportFormat(format) {
        document.querySelectorAll('.format-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-format="${format}"]`).classList.add('active');
        
        this.selectedExportFormat = format;
    }

    updateExportSize(size) {
        const customInputs = document.getElementById('customSizeInputs');
        if (size === 'custom') {
            customInputs.classList.remove('hidden');
        } else {
            customInputs.classList.add('hidden');
        }
    }

    toggleQRCode(enabled) {
        const qrContent = document.getElementById('qrContent');
        if (enabled) {
            qrContent.classList.remove('hidden');
        } else {
            qrContent.classList.add('hidden');
        }
        
        this.updateLivePreview();
    }

    updateQRType(type) {
        const customInput = document.getElementById('customQRContent');
        if (type === 'custom') {
            customInput.classList.remove('hidden');
        } else {
            customInput.classList.add('hidden');
        }
    }

    async downloadCard() {
        // Check export limits
        if (!window.paymentSystem?.checkFeatureLimit('exports')) {
            window.paymentSystem?.showLimitWarning('exports', window.paymentSystem.plans[window.paymentSystem.currentUser?.subscription?.plan || 'free'].limits.exports);
            return;
        }

        const format = this.selectedExportFormat || 'png';
        const cardData = this.getFormData();

        this.showNotification('Przygotowywanie pliku do pobrania...', 'info');

        try {
            const blob = await this.generateCardFile(format, cardData);
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `wizytowka-${cardData.name.replace(/\s+/g, '-').toLowerCase()}.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);

            // Track export for limits
            this.trackExport(format);

            this.showNotification('Wizytówka pobrana pomyślnie!', 'success');

        } catch (error) {
            this.showNotification('Błąd podczas generowania pliku', 'error');
        }
    }

    trackExport(format) {
        if (window.paymentSystem?.currentUser) {
            const user = window.paymentSystem.currentUser;
            if (!user.analytics.exports) {
                user.analytics.exports = [];
            }
            user.analytics.exports.push({
                date: new Date().toISOString(),
                format: format
            });
            localStorage.setItem('smartcard-user', JSON.stringify(user));
        }
    }

    async generateCardFile(format, cardData) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set dimensions based on export size
        const dimensions = this.getExportDimensions();
        canvas.width = dimensions.width;
        canvas.height = dimensions.height;
        
        // Render card to canvas
        await this.renderCardToCanvas(ctx, cardData, dimensions);
        
        return new Promise((resolve) => {
            if (format === 'png') {
                canvas.toBlob(resolve, 'image/png');
            } else if (format === 'jpg') {
                canvas.toBlob(resolve, 'image/jpeg', 0.9);
            } else if (format === 'pdf') {
                // For PDF, we'd use a library like jsPDF
                this.generatePDF(canvas).then(resolve);
            } else if (format === 'svg') {
                // For SVG, we'd generate SVG markup
                this.generateSVG(cardData).then(resolve);
            }
        });
    }

    getExportDimensions() {
        const size = document.getElementById('exportSize').value;
        
        const dimensions = {
            business: { width: 1063, height: 638 }, // 85×55mm at 300 DPI
            social: { width: 1080, height: 1080 },
            email: { width: 600, height: 200 },
            custom: {
                width: parseInt(document.getElementById('customWidth')?.value) || 1063,
                height: parseInt(document.getElementById('customHeight')?.value) || 638
            }
        };
        
        return dimensions[size] || dimensions.business;
    }

    async renderCardToCanvas(ctx, cardData, dimensions) {
        // Set background
        const template = this.templates[this.currentTemplate];
        ctx.fillStyle = template.colors.primary;
        ctx.fillRect(0, 0, dimensions.width, dimensions.height);
        
        // Draw card elements
        ctx.fillStyle = template.colors.text;
        ctx.font = `bold ${dimensions.height * 0.08}px Inter`;
        ctx.fillText(cardData.name || 'Twoje Imię', 50, dimensions.height * 0.3);
        
        if (cardData.title) {
            ctx.font = `${dimensions.height * 0.05}px Inter`;
            ctx.fillText(cardData.title, 50, dimensions.height * 0.45);
        }
        
        if (cardData.company) {
            ctx.fillStyle = template.colors.accent;
            ctx.fillText(cardData.company, 50, dimensions.height * 0.6);
        }
        
        // Draw avatar if exists
        if (this.currentAvatar) {
            await this.drawAvatarOnCanvas(ctx, this.currentAvatar, dimensions);
        }
        
        // Draw QR code if enabled
        if (document.getElementById('includeQR')?.checked) {
            await this.drawQRCodeOnCanvas(ctx, cardData, dimensions);
        }
    }

    async drawAvatarOnCanvas(ctx, avatarSrc, dimensions) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const size = dimensions.height * 0.3;
                const x = dimensions.width - size - 50;
                const y = 50;
                
                // Draw circular avatar
                ctx.save();
                ctx.beginPath();
                ctx.arc(x + size/2, y + size/2, size/2, 0, Math.PI * 2);
                ctx.clip();
                ctx.drawImage(img, x, y, size, size);
                ctx.restore();
                
                resolve();
            };
            img.src = avatarSrc;
        });
    }

    async drawQRCodeOnCanvas(ctx, cardData, dimensions) {
        // Generate QR code data
        const qrData = this.generateQRData(cardData);
        
        // For demo, draw a simple QR placeholder
        const size = dimensions.height * 0.2;
        const x = dimensions.width - size - 50;
        const y = dimensions.height - size - 50;
        
        ctx.fillStyle = '#000000';
        ctx.fillRect(x, y, size, size);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = `${size * 0.1}px monospace`;
        ctx.textAlign = 'center';
        ctx.fillText('QR', x + size/2, y + size/2);
        ctx.textAlign = 'left';
    }

    generateQRData(cardData) {
        const qrType = document.getElementById('qrType')?.value || 'vcard';
        
        switch (qrType) {
            case 'vcard':
                return `BEGIN:VCARD
VERSION:3.0
FN:${cardData.name}
ORG:${cardData.company}
TITLE:${cardData.title}
EMAIL:${cardData.email}
TEL:${cardData.phone}
URL:${cardData.website}
END:VCARD`;
            
            case 'url':
                return cardData.website;
            
            case 'linkedin':
                return cardData.linkedin;
            
            case 'email':
                return `mailto:${cardData.email}`;
            
            case 'phone':
                return `tel:${cardData.phone}`;
            
            case 'custom':
                return document.getElementById('customQRContent')?.value || '';
            
            default:
                return cardData.email;
        }
    }

    showPreviewModal() {
        // Create preview modal
        const previewModal = document.createElement('div');
        previewModal.className = 'preview-modal-overlay';
        previewModal.innerHTML = `
            <div class="preview-modal">
                <div class="preview-modal-header">
                    <h3>Podgląd wizytówki</h3>
                    <button class="close-preview">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="preview-modal-content">
                    <div class="preview-card-container">
                        ${document.getElementById('businessCardLive').outerHTML}
                    </div>
                </div>
                <div class="preview-modal-actions">
                    <button class="btn-secondary" onclick="this.closest('.preview-modal-overlay').remove()">
                        Zamknij
                    </button>
                    <button class="btn-primary" onclick="cardCreator.downloadCard()">
                        <i class="fas fa-download"></i> Pobierz
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(previewModal);
        
        previewModal.querySelector('.close-preview').addEventListener('click', () => {
            document.body.removeChild(previewModal);
        });
    }

    shareCard(platform) {
        const cardData = this.getFormData();
        const shareUrl = this.generatePublicLink();
        const shareText = `Sprawdź moją nową wizytówkę AI: ${cardData.name}`;
        
        const shareUrls = {
            linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`,
            twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,
            facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
            email: `mailto:?subject=${encodeURIComponent(shareText)}&body=${encodeURIComponent(`${shareText}\n\n${shareUrl}`)}`
        };
        
        if (shareUrls[platform]) {
            window.open(shareUrls[platform], '_blank', 'width=600,height=400');
        }
    }

    generatePublicLink() {
        // Generate a unique link for the card
        const cardId = Date.now().toString(36) + Math.random().toString(36).substr(2);
        return `https://smartcard.pl/card/${cardId}`;
    }

    copyPublicLink() {
        const link = this.generatePublicLink();
        document.getElementById('publicLink').value = link;
        
        navigator.clipboard.writeText(link).then(() => {
            this.showNotification('Link skopiowany do schowka!', 'success');
        });
    }

    saveCard() {
        // Check card limits
        if (!window.paymentSystem?.checkFeatureLimit('cards')) {
            window.paymentSystem?.showLimitWarning('cards', window.paymentSystem.plans[window.paymentSystem.currentUser?.subscription?.plan || 'free'].limits.cards);
            return;
        }

        const cardData = this.getFormData();

        if (!cardData.name || !cardData.email) {
            this.showNotification('Wypełnij wymagane pola: Imię i Email', 'error');
            return;
        }

        // Create card object
        const card = {
            id: Date.now(),
            ...cardData,
            template: this.currentTemplate,
            avatar: this.currentAvatar,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // Save to user's cards
        if (window.authSystem?.currentUser) {
            window.authSystem.currentUser.cards.push(card);
            window.authSystem.saveUserToStorage(window.authSystem.currentUser);
        }

        this.showNotification('Wizytówka zapisana pomyślnie!', 'success');
        this.hideCreator();

        // Refresh dashboard if visible
        if (document.getElementById('userDashboard')) {
            location.reload();
        }
    }

    setupFormValidation() {
        // Real-time validation for required fields
        const requiredFields = ['cardName', 'cardEmail'];
        
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => this.validateForm());
                field.addEventListener('blur', () => this.validateField(fieldId));
            }
        });
    }

    validateForm() {
        const name = document.getElementById('cardName')?.value;
        const email = document.getElementById('cardEmail')?.value;
        
        const isValid = name && email && this.validateEmail(email);
        document.getElementById('saveCard').disabled = !isValid;
        
        return isValid;
    }

    validateField(fieldId) {
        const field = document.getElementById(fieldId);
        if (!field) return;
        
        let isValid = true;
        
        if (fieldId === 'cardEmail') {
            isValid = this.validateEmail(field.value);
        } else if (fieldId === 'cardName') {
            isValid = field.value.trim().length > 0;
        }
        
        if (isValid) {
            field.classList.remove('error');
        } else {
            field.classList.add('error');
        }
    }

    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    setupAutoSave() {
        this.autoSaveData = {};
        this.loadAutoSave();
        
        // Auto-save every 30 seconds
        setInterval(() => {
            this.saveToAutoSave();
        }, 30000);
    }

    saveToAutoSave() {
        const formData = this.getFormData();
        this.autoSaveData = {
            ...formData,
            template: this.currentTemplate,
            avatar: this.currentAvatar,
            timestamp: Date.now()
        };
        
        localStorage.setItem('smartcard-autosave', JSON.stringify(this.autoSaveData));
    }

    loadAutoSave() {
        const autoSave = localStorage.getItem('smartcard-autosave');
        if (autoSave) {
            try {
                this.autoSaveData = JSON.parse(autoSave);
                
                // Check if auto-save is recent (within 24 hours)
                const isRecent = (Date.now() - this.autoSaveData.timestamp) < 24 * 60 * 60 * 1000;
                
                if (isRecent && this.autoSaveData.name) {
                    this.showAutoSaveNotification();
                }
            } catch (e) {
                localStorage.removeItem('smartcard-autosave');
            }
        }
    }

    showAutoSaveNotification() {
        const notification = document.createElement('div');
        notification.className = 'autosave-notification';
        notification.innerHTML = `
            <div class="autosave-content">
                <i class="fas fa-save"></i>
                <span>Znaleziono niezapisane zmiany. Chcesz je przywrócić?</span>
                <div class="autosave-actions">
                    <button class="btn-restore" onclick="cardCreator.restoreAutoSave()">Przywróć</button>
                    <button class="btn-discard" onclick="cardCreator.discardAutoSave()">Odrzuć</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }

    restoreAutoSave() {
        if (this.autoSaveData) {
            this.loadCardData(this.autoSaveData);
            this.showNotification('Przywrócono niezapisane zmiany', 'success');
        }
        this.removeAutoSaveNotification();
    }

    discardAutoSave() {
        localStorage.removeItem('smartcard-autosave');
        this.removeAutoSaveNotification();
    }

    removeAutoSaveNotification() {
        const notification = document.querySelector('.autosave-notification');
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }
    }

    loadCardData(cardData) {
        // Fill form with card data
        Object.keys(cardData).forEach(key => {
            const input = document.getElementById(`card${key.charAt(0).toUpperCase() + key.slice(1)}`);
            if (input && cardData[key]) {
                input.value = cardData[key];
            }
        });
        
        // Set template
        if (cardData.template) {
            this.selectTemplate(cardData.template);
        }
        
        // Set avatar
        if (cardData.avatar) {
            this.currentAvatar = cardData.avatar;
        }
        
        this.updateLivePreview();
    }

    resetForm() {
        // Clear all form inputs
        document.getElementById('cardForm')?.reset();
        
        // Reset template to default
        this.selectTemplate('modern');
        
        // Reset avatar
        this.currentAvatar = null;
        document.getElementById('avatarPreview').innerHTML = `
            <div class="avatar-placeholder">
                <i class="fas fa-camera"></i>
                <span>Prześlij zdjęcie</span>
            </div>
        `;
        
        // Reset various selections
        document.querySelectorAll('.style-option').forEach(option => option.classList.remove('active'));
        document.querySelector('.style-option[data-style="professional"]')?.classList.add('active');
        
        this.updateLivePreview();
    }

    enableSaveButton() {
        const saveBtn = document.getElementById('saveCard');
        if (saveBtn) {
            saveBtn.disabled = false;
        }
    }

    showUploadProgress() {
        const preview = document.getElementById('avatarPreview');
        preview.innerHTML = `
            <div class="upload-progress">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Przesyłanie...</span>
            </div>
        `;
    }

    showNotification(message, type) {
        // Use existing notification system
        if (window.SmartCardApp) {
            const app = new window.SmartCardApp();
            app.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    initializeAIAvatar() {
        // Initialize AI avatar processing capabilities
        this.selectedAvatarStyle = 'professional';
        this.uploadedImage = null;
        this.currentAvatar = null;
    }
}

// Global functions for card creator
function createNewCard() {
    if (!window.cardCreator) {
        window.cardCreator = new CardCreator();
    }
    window.cardCreator.showCreator();
}

function editCard(cardId) {
    // Load card data and open editor
    if (window.authSystem?.currentUser) {
        const card = window.authSystem.currentUser.cards.find(c => c.id == cardId);
        if (card) {
            if (!window.cardCreator) {
                window.cardCreator = new CardCreator();
            }
            window.cardCreator.showCreator(card);
        }
    }
}

// Initialize card creator when page loads
document.addEventListener('DOMContentLoaded', () => {
    if (!window.cardCreator) {
        window.cardCreator = new CardCreator();
    }
});

// Export for global access
window.CardCreator = CardCreator;