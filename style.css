/* DIGITAL VELOCITY - Kinematograficzne Portfolio */

:root {
    /* Neon Colors - Tokyo Drift Palette */
    --neon-blue: #00f5ff;
    --neon-purple: #bf00ff;
    --neon-pink: #ff0080;
    --neon-green: #00ff41;
    --neon-orange: #ff8c00;
    
    /* Dark Theme */
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-accent: var(--neon-blue);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
    --gradient-secondary: linear-gradient(135deg, var(--neon-pink), var(--neon-orange));
    --gradient-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    
    /* Fonts */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Raj<PERSON><PERSON>', sans-serif;
    
    /* Spacing */
    --container-max: 1400px;
    --section-padding: 120px 0;
    --element-gap: 2rem;
}

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: var(--font-secondary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-container {
    text-align: center;
    position: relative;
}

.neon-logo {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 2rem;
    position: relative;
}

.logo-text {
    color: var(--text-primary);
    text-shadow: 0 0 20px var(--neon-blue);
}

.logo-accent {
    color: var(--neon-blue);
    text-shadow: 
        0 0 10px var(--neon-blue),
        0 0 20px var(--neon-blue),
        0 0 40px var(--neon-blue);
    animation: neonPulse 2s ease-in-out infinite alternate;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto 1rem;
}

.loading-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    animation: loadingProgress 3s ease-in-out;
    box-shadow: 0 0 20px var(--neon-blue);
}

.loading-text {
    color: var(--text-secondary);
    font-size: 1.1rem;
    animation: textPulse 1.5s ease-in-out infinite;
}

.loading-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    pointer-events: none;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 245, 255, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.95);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.1);
}

.nav-container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo {
    font-family: var(--font-primary);
    font-weight: 900;
    font-size: 1.5rem;
}

.logo-main {
    color: var(--text-primary);
}

.logo-sub {
    color: var(--neon-blue);
    text-shadow: 0 0 10px var(--neon-blue);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--neon-blue);
    background: rgba(0, 245, 255, 0.1);
    text-shadow: 0 0 10px var(--neon-blue);
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-cta {
    background: var(--gradient-primary);
    color: var(--text-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
}

.nav-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.5);
}

.mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.mobile-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.neon-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(45deg, transparent 49%, var(--neon-blue) 50%, transparent 51%),
        linear-gradient(-45deg, transparent 49%, var(--neon-purple) 50%, transparent 51%);
    background-size: 200px 200px;
    opacity: 0.1;
    animation: neonLinesMove 15s linear infinite;
}

.hero-container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: 100vh;
}

.hero-content {
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(0, 245, 255, 0.1);
    border: 1px solid rgba(0, 245, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    color: var(--neon-blue);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    animation: badgeFloat 3s ease-in-out infinite;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.title-line {
    display: block;
    color: var(--text-primary);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.title-line.accent {
    color: var(--neon-blue);
    text-shadow: 
        0 0 10px var(--neon-blue),
        0 0 20px var(--neon-blue),
        0 0 40px var(--neon-blue);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

.title-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin-top: 0.5rem;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-stats {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--neon-blue);
    text-shadow: 0 0 20px var(--neon-blue);
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.btn-primary,
.btn-secondary {
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-primary);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 245, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid rgba(0, 245, 255, 0.5);
}

.btn-secondary:hover {
    background: rgba(0, 245, 255, 0.1);
    border-color: var(--neon-blue);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.2);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-cards {
    position: relative;
    width: 100%;
    height: 100%;
}

.project-card {
    position: absolute;
    width: 120px;
    height: 160px;
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.project-card:hover {
    transform: translateY(-10px) scale(1.05);
    border-color: var(--neon-blue);
    box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
}

.card-icon {
    font-size: 2rem;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-1 {
    top: 10%;
    left: 10%;
    animation: cardFloat1 6s ease-in-out infinite;
}

.card-2 {
    top: 20%;
    right: 15%;
    animation: cardFloat2 7s ease-in-out infinite;
}

.card-3 {
    bottom: 30%;
    left: 20%;
    animation: cardFloat3 8s ease-in-out infinite;
}

.card-4 {
    bottom: 15%;
    right: 10%;
    animation: cardFloat4 9s ease-in-out infinite;
}

.hologram-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 200px;
    background: rgba(0, 245, 255, 0.05);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hologram-content {
    text-align: center;
}

.code-lines {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--neon-green);
}

.code-line {
    margin: 0.5rem 0;
    opacity: 0;
    animation: codeTypewriter 3s ease-in-out infinite;
}

.code-line:nth-child(2) {
    animation-delay: 1s;
}

.code-line:nth-child(3) {
    animation-delay: 2s;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--text-secondary);
    animation: scrollBounce 2s ease-in-out infinite;
}

.scroll-text {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.scroll-arrow {
    font-size: 1.2rem;
    color: var(--neon-blue);
}

/* Animations */
@keyframes neonPulse {
    0% { text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 40px var(--neon-blue); }
    100% { text-shadow: 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue), 0 0 60px var(--neon-blue); }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes textPulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes neonLinesMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(200px, 200px); }
}

@keyframes badgeFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes titleGlow {
    0% { text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 40px var(--neon-blue); }
    100% { text-shadow: 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue), 0 0 60px var(--neon-blue); }
}

@keyframes cardFloat1 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes cardFloat2 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes cardFloat3 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(7deg); }
}

@keyframes cardFloat4 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-18px) rotate(-5deg); }
}

@keyframes codeTypewriter {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes scrollBounce {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-10px); }
}

/* Container */
.container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1rem;
}

.title-accent {
    color: var(--neon-blue);
    text-shadow: 0 0 20px var(--neon-blue);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 245, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--neon-blue);
    box-shadow: 0 20px 40px rgba(0, 245, 255, 0.2);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 10px 20px rgba(0, 245, 255, 0.3);
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
}

.service-features li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--neon-blue);
    font-size: 0.8rem;
}

/* Projects Section */
.projects {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.projects-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid rgba(0, 245, 255, 0.3);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient-primary);
    border-color: var(--neon-blue);
    color: var(--text-primary);
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
}

.projects-carousel {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: 20px;
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 245, 255, 0.2);
}

/* Fallback Projects */
.fallback-projects {
    padding: 2rem;
    text-align: center;
}

.fallback-projects h3 {
    color: var(--neon-blue);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.projects-grid-fallback {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.fallback-project-card {
    background: var(--bg-primary);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    text-align: left;
}

.fallback-project-card:hover {
    transform: translateY(-5px);
    border-color: var(--neon-blue);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.2);
}

.project-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: white;
}

.project-info h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.project-category {
    color: var(--neon-blue);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.project-desc {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tech-tag {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.project-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 245, 255, 0.3);
    border-top: 4px solid var(--neon-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Additional Effects & Animations */
.glitch-effect {
    animation: glitch 0.3s ease-in-out;
}

@keyframes glitch {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

.neon-pulse {
    animation: neonPulseEffect 1s ease-in-out;
}

@keyframes neonPulseEffect {
    0% {
        box-shadow: 0 0 5px var(--neon-blue);
        text-shadow: 0 0 5px var(--neon-blue);
    }
    50% {
        box-shadow: 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
        text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue);
    }
    100% {
        box-shadow: 0 0 5px var(--neon-blue);
        text-shadow: 0 0 5px var(--neon-blue);
    }
}

/* Animate In Classes */
.animate-in {
    animation: slideInUp 0.8s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Demo Modal */
.demo-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 20px;
    max-width: 800px;
    width: 90%;
    max-height: 80%;
    overflow: hidden;
    position: relative;
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(0, 245, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--neon-blue);
}

.modal-body {
    padding: 2rem;
}

.demo-video {
    width: 100%;
    height: 400px;
    background: var(--bg-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0, 245, 255, 0.2);
}

.video-placeholder {
    text-align: center;
    color: var(--text-secondary);
}

.video-placeholder i {
    font-size: 3rem;
    color: var(--neon-blue);
    margin-bottom: 1rem;
}

/* About Section */
.about {
    padding: var(--section-padding);
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%2300f5ff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 0;
}

.about-container {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-content h2 {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.about-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.about-stat {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 245, 255, 0.2);
    border-radius: 15px;
}

.about-stat-number {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 900;
    color: var(--neon-blue);
    text-shadow: 0 0 10px var(--neon-blue);
    display: block;
    margin-bottom: 0.5rem;
}

.about-stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.about-visual {
    position: relative;
    height: 500px;
}

.tech-orbit {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    border: 2px solid rgba(0, 245, 255, 0.2);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.tech-item {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
    animation: counterRotate 20s linear infinite;
}

.tech-item:nth-child(1) { top: -30px; left: 50%; transform: translateX(-50%); }
.tech-item:nth-child(2) { top: 50%; right: -30px; transform: translateY(-50%); }
.tech-item:nth-child(3) { bottom: -30px; left: 50%; transform: translateX(-50%); }
.tech-item:nth-child(4) { top: 50%; left: -30px; transform: translateY(-50%); }

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes counterRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(-360deg); }
}

/* Contact Section */
.contact {
    padding: var(--section-padding);
    background: var(--bg-primary);
    position: relative;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.contact-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 245, 255, 0.2);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-method:hover {
    border-color: var(--neon-blue);
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.2);
}

.contact-method-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.contact-method-info h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.contact-method-info p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.contact-form {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid rgba(0, 245, 255, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid rgba(0, 245, 255, 0.2);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.form-group select {
    cursor: pointer;
}

.form-group select option {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    width: 100%;
    background: var(--gradient-primary);
    color: var(--text-primary);
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.5);
}

/* Instant Contact */
.instant-contact {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 10000;
}

.contact-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.4);
    transition: all 0.3s ease;
    position: relative;
    animation: contactFloat 3s ease-in-out infinite;
}

.contact-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(0, 245, 255, 0.6);
}

.contact-toggle i {
    font-size: 1.5rem;
    color: white;
}

.contact-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--neon-blue);
    opacity: 0.3;
    animation: contactPulse 2s ease-in-out infinite;
}

.contact-options {
    position: absolute;
    bottom: 80px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.contact-options.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.contact-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 25px;
    color: var(--text-primary);
    text-decoration: none;
    min-width: 200px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.contact-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.contact-option:hover::before {
    left: 100%;
}

.contact-option:hover {
    transform: translateX(-10px);
    border-color: var(--neon-blue);
    box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
}

.contact-option.whatsapp:hover {
    border-color: #25d366;
    box-shadow: 0 10px 30px rgba(37, 211, 102, 0.3);
}

.contact-option.telegram:hover {
    border-color: #0088cc;
    box-shadow: 0 10px 30px rgba(0, 136, 204, 0.3);
}

.contact-option.email:hover {
    border-color: var(--neon-purple);
    box-shadow: 0 10px 30px rgba(191, 0, 255, 0.3);
}

.contact-option i {
    font-size: 1.5rem;
    width: 24px;
    text-align: center;
}

.contact-option.whatsapp i {
    color: #25d366;
}

.contact-option.telegram i {
    color: #0088cc;
}

.contact-option.email i {
    color: var(--neon-purple);
}

.contact-option span {
    font-weight: 600;
    flex: 1;
}

.contact-info {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: right;
}

/* Contact Animations */
@keyframes contactFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes contactPulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(0, 245, 255, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 700;
}

.footer-section p,
.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    line-height: 1.6;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--neon-blue);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 245, 255, 0.1);
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 2rem;
    }

    .hero-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .projects-filter {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .about-container,
    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .tech-orbit {
        width: 250px;
        height: 250px;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header,
    .modal-body {
        padding: 1rem;
    }

    .demo-video {
        height: 250px;
    }

    /* Instant Contact Mobile */
    .instant-contact {
        bottom: 1rem;
        right: 1rem;
    }

    .contact-toggle {
        width: 50px;
        height: 50px;
    }

    .contact-toggle i {
        font-size: 1.2rem;
    }

    .contact-options {
        bottom: 70px;
        right: 0;
    }

    .contact-option {
        padding: 0.75rem 1rem;
        min-width: 180px;
        font-size: 0.9rem;
    }

    .contact-option i {
        font-size: 1.2rem;
    }

    .contact-info {
        font-size: 0.7rem;
    }
}
