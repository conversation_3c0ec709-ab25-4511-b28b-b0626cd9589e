# EventAI.pl Configuration
NODE_ENV=production

# Hugging Face Configuration
HF_TOKEN=*************************************
LLAMA_MODEL=meta-llama/Meta-Llama-3.1-8B-Instruct
USE_LLAMA=true

# DeepSeek R1 Configuration (Fallback)
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-R1-Distill-Llama-8B

# API Endpoints
HF_INFERENCE_URL=https://api-inference.huggingface.co/models
HF_CHAT_COMPLETIONS_URL=https://api-inference.huggingface.co/models/meta-llama/Meta-Llama-3.1-8B-Instruct/v1/chat/completions

# Application Settings
APP_NAME=EventAI.pl
APP_VERSION=1.0.0
DEBUG=false

# Rate Limiting
MAX_REQUESTS_PER_MINUTE=30
CACHE_DURATION_MS=1800000

# Database (LocalStorage/IndexedDB for client-side)
STORAGE_PREFIX=eventai_
MAX_STORAGE_SIZE=50MB

# Features
ENABLE_OFFLINE_MODE=true
ENABLE_PWA=true
ENABLE_ANALYTICS=true
ENABLE_ERROR_TRACKING=true