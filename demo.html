<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DIGITAL VELOCITY - Demo Features</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .demo-title {
            font-size: 3rem;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: #cccccc;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: #1a1a1a;
            border: 1px solid #00f5ff;
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
        }
        
        .feature-title {
            color: #00f5ff;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .feature-description {
            color: #cccccc;
            margin-bottom: 1.5rem;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 245, 255, 0.5);
        }
        
        .projects-showcase {
            margin-top: 3rem;
        }
        
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .project-card {
            background: #1a1a1a;
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .project-card:hover {
            border-color: #00f5ff;
            box-shadow: 0 5px 15px rgba(0, 245, 255, 0.2);
        }
        
        .project-title {
            color: #ffffff;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .project-category {
            color: #00f5ff;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .project-description {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .project-link {
            color: #00f5ff;
            text-decoration: none;
            font-weight: 600;
        }
        
        .project-link:hover {
            text-decoration: underline;
        }
        
        .instructions {
            background: #1a1a1a;
            border: 1px solid #bf00ff;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 3rem;
        }
        
        .instructions h3 {
            color: #bf00ff;
            margin-bottom: 1rem;
        }
        
        .instructions ul {
            color: #cccccc;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
        }
        
        .easter-eggs {
            background: #0a0a0a;
            border: 2px dashed #ff0080;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            text-align: center;
        }
        
        .easter-eggs h3 {
            color: #ff0080;
            margin-bottom: 1rem;
        }
        
        .back-to-main {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <a href="index.html" class="demo-button back-to-main">← Powrót do Portfolio</a>
    
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">DIGITAL VELOCITY</h1>
            <p class="demo-subtitle">Przewodnik po funkcjach kinematograficznego portfolio</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h3 class="feature-title">🎬 Kinematograficzne Efekty</h3>
                <p class="feature-description">
                    Animacje inspirowane filmem Tokyo Drift z płynnym scrollingiem, 
                    efektami parallax i 3D transformacjami.
                </p>
                <a href="index.html#home" class="demo-button">Zobacz Hero Section</a>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">🎮 3D Carousel Projektów</h3>
                <p class="feature-description">
                    Interaktywny carousel z 20 projektami, efektami depth, 
                    flip cards i smooth transitions.
                </p>
                <a href="index.html#projects" class="demo-button">Eksploruj Projekty</a>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">✨ Particle System</h3>
                <p class="feature-description">
                    Zaawansowany system cząsteczek reagujący na mysz, 
                    z neonowymi efektami i połączeniami.
                </p>
                <a href="index.html" class="demo-button">Testuj Particles</a>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">🤖 AI-Powered Demos</h3>
                <p class="feature-description">
                    Live demos projektów AI: chatboty, automatyzacja, 
                    personal branding i systemy zarządzania.
                </p>
                <a href="demos/ai-chat.html" class="demo-button">AI Chat Demo</a>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">📱 Responsive Design</h3>
                <p class="feature-description">
                    Mobile-first approach z touch gestures, 
                    adaptive layouts i optimized performance.
                </p>
                <a href="index.html" class="demo-button">Test Mobile</a>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">🎵 Sound Design</h3>
                <p class="feature-description">
                    Futurystyczne dźwięki UI, hover effects 
                    i ambient background audio.
                </p>
                <a href="index.html" class="demo-button">Włącz Audio</a>
            </div>
        </div>
        
        <div class="projects-showcase">
            <h2 style="color: #00f5ff; text-align: center; margin-bottom: 2rem;">
                Portfolio Projektów (20 total)
            </h2>
            
            <div class="projects-grid">
                <div class="project-card">
                    <h4 class="project-title">AI Chat Support</h4>
                    <p class="project-category">AI & Automation</p>
                    <p class="project-description">Inteligentny system obsługi klienta z real-time responses</p>
                    <a href="demos/ai-chat.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">BrandMe.pl</h4>
                    <p class="project-category">Personal Branding</p>
                    <p class="project-description">Platforma budowania osobistej marki z AI tools</p>
                    <a href="projects/brandme-pl/index.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">FitGenius AI Coach</h4>
                    <p class="project-category">Health & Fitness</p>
                    <p class="project-description">Personalny trener AI dostępny 24/7</p>
                    <a href="projects/fitgenius-ai-coach/index.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">GastroAI Optimizer</h4>
                    <p class="project-category">Restaurant Management</p>
                    <p class="project-description">Inteligentny system zarządzania restauracją</p>
                    <a href="projects/gastro-ai-restaurant-optimizer/index.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">SmartCard.pl</h4>
                    <p class="project-category">AI Business Cards</p>
                    <p class="project-description">Generator wizytówek z AI avatarem</p>
                    <a href="projects/card/index.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">Dental Dashboard</h4>
                    <p class="project-category">Healthcare Management</p>
                    <p class="project-description">System zarządzania praktyką dentystyczną</p>
                    <a href="demos/dashboard-dental.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">E-commerce Candles</h4>
                    <p class="project-category">Online Store</p>
                    <p class="project-description">Sklep internetowy ze świecami</p>
                    <a href="demos/ecommerce-candles.html" class="project-link">Zobacz Demo →</a>
                </div>
                
                <div class="project-card">
                    <h4 class="project-title">+ 13 więcej projektów</h4>
                    <p class="project-category">Various Categories</p>
                    <p class="project-description">Odkryj pozostałe projekty w głównym portfolio</p>
                    <a href="index.html#projects" class="project-link">Zobacz Wszystkie →</a>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎯 Jak korzystać z portfolio:</h3>
            <ul>
                <li><strong>Nawigacja:</strong> Użyj menu lub scroll aby przemieszczać się między sekcjami</li>
                <li><strong>Projekty:</strong> Kliknij na karty projektów aby zobaczyć live demo</li>
                <li><strong>Filtry:</strong> Użyj przycisków filtrowania aby zobaczyć konkretne kategorie</li>
                <li><strong>Carousel:</strong> Użyj strzałek, scroll lub touch gestures do nawigacji</li>
                <li><strong>Particles:</strong> Poruszaj myszą aby zobaczyć interaktywne efekty</li>
                <li><strong>Formularz:</strong> Wypełnij formularz kontaktowy aby przetestować funkcjonalność</li>
            </ul>
        </div>
        
        <div class="easter-eggs">
            <h3>🎪 Easter Eggs & Ukryte Funkcje:</h3>
            <p><strong>Konami Code:</strong> ↑↑↓↓←→←→BA - Aktywuje Matrix Mode</p>
            <p><strong>Logo Clicks:</strong> Kliknij 5x na logo aby aktywować Neon Mode</p>
            <p><strong>Keyboard Navigation:</strong> Użyj strzałek do nawigacji w carousel</p>
            <p><strong>Performance Monitor:</strong> Sprawdź konsolę dla statystyk wydajności</p>
        </div>
        
        <div style="text-align: center; margin-top: 3rem; padding: 2rem;">
            <h3 style="color: #00f5ff;">Gotowy na cyfrową transformację?</h3>
            <p style="color: #cccccc; margin-bottom: 2rem;">
                Odkryj pełne portfolio i skontaktuj się z nami!
            </p>
            <a href="index.html" class="demo-button" style="font-size: 1.2rem; padding: 1rem 2rem;">
                🚀 Wejdź do Portfolio
            </a>
        </div>
    </div>
</body>
</html>
