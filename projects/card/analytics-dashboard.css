/* Analytics Dashboard Styles for SmartCard.pl */

.analytics-dashboard-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.analytics-dashboard-overlay.show {
    opacity: 1;
}

.analytics-dashboard {
    width: 95vw;
    height: 95vh;
    max-width: 1400px;
    max-height: 900px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.analytics-dashboard-overlay.show .analytics-dashboard {
    transform: scale(1);
}

/* Analytics Header */
.analytics-header {
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid var(--glass-border);
    padding: 1.5rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h2 {
    color: var(--dark-text);
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title h2 i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-title p {
    color: var(--dark-text-secondary);
    margin: 0;
    font-size: var(--font-size-base);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.time-range-select {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

.export-analytics-btn,
.close-analytics-btn {
    padding: 8px 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.export-analytics-btn:hover {
    background: var(--success-gradient);
    color: white;
    border-color: transparent;
}

.close-analytics-btn:hover {
    background: var(--warning-gradient);
    color: white;
    border-color: transparent;
}

/* Analytics Content */
.analytics-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: all var(--animation-fast);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.metric-card.views::before {
    background: var(--primary-gradient);
}

.metric-card.scans::before {
    background: var(--secondary-gradient);
}

.metric-card.contacts::before {
    background: var(--success-gradient);
}

.metric-card.conversion::before {
    background: var(--warning-gradient);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--glass-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-gradient);
    flex-shrink: 0;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--dark-text);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.metric-label {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-base);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.metric-change {
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.metric-change.positive {
    color: #27ae60;
}

.metric-change.negative {
    color: #e74c3c;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-container {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: all var(--animation-fast);
}

.chart-container:hover {
    box-shadow: var(--shadow-md);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    color: var(--dark-text);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 4px;
}

.chart-type-btn {
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-type-btn:hover,
.chart-type-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.chart-legend {
    display: flex;
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-sm);
    color: var(--dark-text-secondary);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.chart-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-content canvas {
    max-width: 100%;
    height: auto;
}

/* Location List */
.location-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.location-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius-md);
    transition: all var(--animation-fast);
}

.location-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.location-info {
    flex: 1;
}

.location-name {
    color: var(--dark-text);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.location-count {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
}

.location-bar {
    width: 100px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.location-progress {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 3px;
    transition: width var(--animation-normal);
}

/* Conversion Funnel */
.conversion-funnel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
}

.funnel-step {
    position: relative;
}

.step-bar {
    height: 50px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    color: white;
    font-weight: 600;
    transition: all var(--animation-normal);
    position: relative;
    overflow: hidden;
}

.step-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
}

.step-label {
    font-size: var(--font-size-base);
    z-index: 1;
}

.step-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    z-index: 1;
}

/* Real-time Section */
.chart-container.realtime {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.realtime-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-sm);
    color: var(--dark-text-secondary);
}

.realtime-indicator.active {
    color: #27ae60;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background: #27ae60;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.realtime-feed {
    max-height: 300px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.realtime-event {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius-md);
    border-left: 3px solid var(--primary-gradient);
    transition: all var(--animation-fast);
}

.realtime-event.new {
    animation: eventSlideIn 0.5s ease-out;
    background: rgba(102, 126, 234, 0.1);
}

@keyframes eventSlideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.realtime-event.view {
    border-left-color: #667eea;
}

.realtime-event.scan {
    border-left-color: #764ba2;
}

.realtime-event.contact {
    border-left-color: #27ae60;
}

.realtime-event.share {
    border-left-color: #f093fb;
}

.event-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--glass-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--primary-gradient);
}

.event-content {
    flex: 1;
}

.event-message {
    color: var(--dark-text);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.event-time {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-xs);
}

/* Insights Section */
.chart-container.insights {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
}

.insights-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius-md);
    border-left: 3px solid;
}

.insight-item.positive {
    border-left-color: #27ae60;
}

.insight-item.warning {
    border-left-color: #f39c12;
}

.insight-item.info {
    border-left-color: #3498db;
}

.insight-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 0.875rem;
}

.insight-item.positive .insight-icon {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.insight-item.warning .insight-icon {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
}

.insight-item.info .insight-icon {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
}

.insight-content {
    flex: 1;
}

.insight-title {
    color: var(--dark-text);
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: var(--font-size-sm);
}

.insight-message {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* Details Section */
.details-section {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.details-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid var(--glass-border);
}

.details-tab {
    flex: 1;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    font-weight: 500;
    border-bottom: 3px solid transparent;
}

.details-tab:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--dark-text);
}

.details-tab.active {
    color: var(--dark-text);
    border-bottom-color: var(--primary-gradient);
    background: rgba(102, 126, 234, 0.1);
}

.details-content {
    position: relative;
    min-height: 400px;
}

.details-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1.5rem;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-fast);
    overflow-y: auto;
}

.details-panel.active {
    opacity: 1;
    visibility: visible;
}

/* Data Tables */
.data-table {
    width: 100%;
    overflow-x: auto;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--glass-border);
}

.data-table th {
    background: rgba(255, 255, 255, 0.02);
    color: var(--dark-text-secondary);
    font-weight: 600;
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    color: var(--dark-text);
    font-size: var(--font-size-sm);
}

.data-table tr:hover td {
    background: rgba(255, 255, 255, 0.02);
}

.location-cell {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.location-cell strong {
    color: var(--dark-text);
}

.location-cell .country {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-xs);
}

.progress-cell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 3px;
    transition: width var(--animation-normal);
}

.trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 600;
    font-size: var(--font-size-xs);
}

.trend.up {
    color: #27ae60;
}

.trend.down {
    color: #e74c3c;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-sm);
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.event-type {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.event-type.view {
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
}

.event-type.scan {
    background: rgba(118, 75, 162, 0.2);
    color: #764ba2;
}

.event-type.contact {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.event-type.share {
    background: rgba(240, 147, 251, 0.2);
    color: #f093fb;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .analytics-dashboard {
        width: 98vw;
        height: 98vh;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .analytics-dashboard {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    
    .analytics-content {
        padding: 1rem;
    }
    
    .analytics-header {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .header-actions {
        justify-content: space-between;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .details-tabs {
        flex-wrap: wrap;
    }
    
    .details-tab {
        flex: 1;
        min-width: 25%;
        padding: 0.75rem;
        font-size: var(--font-size-sm);
    }
    
    .data-table {
        font-size: var(--font-size-xs);
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .metric-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .metric-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }
    
    .metric-value {
        font-size: var(--font-size-2xl);
    }
    
    .chart-content {
        min-height: 150px;
    }
    
    .details-tab {
        min-width: 50%;
    }
}

/* Loading States */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--dark-text-secondary);
}

.chart-loading i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar for Analytics */
.analytics-content::-webkit-scrollbar,
.realtime-feed::-webkit-scrollbar,
.details-panel::-webkit-scrollbar {
    width: 6px;
}

.analytics-content::-webkit-scrollbar-track,
.realtime-feed::-webkit-scrollbar-track,
.details-panel::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.analytics-content::-webkit-scrollbar-thumb,
.realtime-feed::-webkit-scrollbar-thumb,
.details-panel::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 3px;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .analytics-dashboard,
    .metric-card,
    .chart-container,
    .realtime-event,
    .details-panel {
        transition: none;
    }
    
    .pulse-dot {
        animation: none;
    }
    
    .realtime-event.new {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .analytics-dashboard {
        background: #000000;
        border: 2px solid #ffffff;
    }
    
    .metric-card,
    .chart-container {
        background: #1a1a1a;
        border: 1px solid #ffffff;
    }
    
    .metric-value,
    .chart-header h3 {
        color: #ffffff;
    }
}