// Authentication System for SmartCard.pl
class AuthenticationSystem {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        this.loadUserFromStorage();
        this.setupAuthUI();
        this.checkAuthState();
    }

    loadUserFromStorage() {
        const userData = localStorage.getItem('smartcard-user');
        if (userData) {
            try {
                this.currentUser = JSON.parse(userData);
            } catch (e) {
                localStorage.removeItem('smartcard-user');
            }
        }
    }

    saveUserToStorage(user) {
        localStorage.setItem('smartcard-user', JSON.stringify(user));
        this.currentUser = user;
    }

    setupAuthUI() {
        this.createAuthModal();
        this.updateNavigation();
    }

    createAuthModal() {
        const authModal = document.createElement('div');
        authModal.id = 'authModal';
        authModal.className = 'auth-modal-overlay hidden';
        authModal.innerHTML = `
            <div class="auth-modal">
                <div class="auth-modal-header">
                    <h2 id="authTitle" data-translate="login_title">Zaloguj się</h2>
                    <button class="close-auth" id="closeAuth">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="login" data-translate="login_tab">Logowanie</button>
                    <button class="auth-tab" data-tab="register" data-translate="register_tab">Rejestracja</button>
                </div>
                
                <!-- Login Form -->
                <div class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label data-translate="email_label">Email</label>
                        <input type="email" id="loginEmail" placeholder="<EMAIL>" required>
                        <div class="input-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="password_label">Hasło</label>
                        <input type="password" id="loginPassword" placeholder="••••••••" required>
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe">
                            <span class="checkmark"></span>
                            <span data-translate="remember_me">Zapamiętaj mnie</span>
                        </label>
                        <a href="#" class="forgot-password" data-translate="forgot_password">Zapomniałeś hasła?</a>
                    </div>
                    
                    <button type="submit" class="auth-btn primary" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i>
                        <span data-translate="login_btn">Zaloguj się</span>
                    </button>
                    
                    <div class="auth-divider">
                        <span data-translate="or">lub</span>
                    </div>
                    
                    <div class="social-login">
                        <button class="social-btn google" id="googleLogin">
                            <i class="fab fa-google"></i>
                            <span data-translate="login_google">Zaloguj przez Google</span>
                        </button>
                        <button class="social-btn linkedin" id="linkedinLogin">
                            <i class="fab fa-linkedin"></i>
                            <span data-translate="login_linkedin">Zaloguj przez LinkedIn</span>
                        </button>
                    </div>
                </div>
                
                <!-- Register Form -->
                <div class="auth-form hidden" id="registerForm">
                    <div class="form-group">
                        <label data-translate="full_name_label">Imię i nazwisko</label>
                        <input type="text" id="registerName" placeholder="Jan Kowalski" required>
                        <div class="input-icon">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="email_label">Email</label>
                        <input type="email" id="registerEmail" placeholder="<EMAIL>" required>
                        <div class="input-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="password_label">Hasło</label>
                        <input type="password" id="registerPassword" placeholder="••••••••" required>
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <button type="button" class="password-toggle" onclick="togglePassword('registerPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text" data-translate="password_weak">Słabe hasło</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="confirm_password_label">Potwierdź hasło</label>
                        <input type="password" id="confirmPassword" placeholder="••••••••" required>
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="acceptTerms" required>
                            <span class="checkmark"></span>
                            <span data-translate="accept_terms">Akceptuję <a href="#" class="terms-link">Regulamin</a> i <a href="#" class="privacy-link">Politykę Prywatności</a></span>
                        </label>
                        
                        <label class="checkbox-container">
                            <input type="checkbox" id="newsletter">
                            <span class="checkmark"></span>
                            <span data-translate="newsletter_signup">Chcę otrzymywać newsletter z nowościami</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="auth-btn primary" id="registerBtn">
                        <i class="fas fa-user-plus"></i>
                        <span data-translate="register_btn">Załóż konto</span>
                    </button>
                    
                    <div class="auth-divider">
                        <span data-translate="or">lub</span>
                    </div>
                    
                    <div class="social-login">
                        <button class="social-btn google" id="googleRegister">
                            <i class="fab fa-google"></i>
                            <span data-translate="register_google">Zarejestruj przez Google</span>
                        </button>
                        <button class="social-btn linkedin" id="linkedinRegister">
                            <i class="fab fa-linkedin"></i>
                            <span data-translate="register_linkedin">Zarejestruj przez LinkedIn</span>
                        </button>
                    </div>
                </div>
                
                <!-- Success Message -->
                <div class="auth-success hidden" id="authSuccess">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 data-translate="welcome_title">Witamy w SmartCard.pl!</h3>
                    <p data-translate="welcome_message">Twoje konto zostało utworzone. Możesz teraz tworzyć nieograniczone wizytówki AI.</p>
                    <button class="auth-btn primary" id="continueBtn" data-translate="continue_btn">Kontynuuj</button>
                </div>
            </div>
        `;

        document.body.appendChild(authModal);
        this.setupAuthEventListeners();
    }

    setupAuthEventListeners() {
        // Modal controls
        document.getElementById('closeAuth').addEventListener('click', () => this.hideAuthModal());
        document.getElementById('authModal').addEventListener('click', (e) => {
            if (e.target.id === 'authModal') this.hideAuthModal();
        });

        // Tab switching
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchAuthTab(tab.dataset.tab));
        });

        // Form submissions
        document.getElementById('loginBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('registerBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // Social logins
        document.getElementById('googleLogin').addEventListener('click', () => this.handleSocialLogin('google'));
        document.getElementById('linkedinLogin').addEventListener('click', () => this.handleSocialLogin('linkedin'));
        document.getElementById('googleRegister').addEventListener('click', () => this.handleSocialLogin('google'));
        document.getElementById('linkedinRegister').addEventListener('click', () => this.handleSocialLogin('linkedin'));

        // Password strength checker
        document.getElementById('registerPassword').addEventListener('input', (e) => {
            this.checkPasswordStrength(e.target.value);
        });

        // Confirm password validation
        document.getElementById('confirmPassword').addEventListener('input', () => {
            this.validatePasswordMatch();
        });

        // Continue button
        document.getElementById('continueBtn').addEventListener('click', () => {
            this.hideAuthModal();
            this.redirectToDashboard();
        });

        // Forgot password
        document.querySelector('.forgot-password').addEventListener('click', (e) => {
            e.preventDefault();
            this.showForgotPassword();
        });
    }

    showAuthModal(mode = 'login') {
        const modal = document.getElementById('authModal');
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        // Add entrance animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        this.switchAuthTab(mode);
    }

    hideAuthModal() {
        const modal = document.getElementById('authModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
    }

    switchAuthTab(tab) {
        // Update tabs
        document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // Update forms
        document.querySelectorAll('.auth-form').forEach(form => form.classList.add('hidden'));
        document.getElementById(`${tab}Form`).classList.remove('hidden');

        // Update title
        const title = tab === 'login' ? 'Zaloguj się' : 'Załóż konto';
        document.getElementById('authTitle').textContent = title;
    }

    async handleLogin() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!this.validateEmail(email)) {
            this.showAuthError('Podaj prawidłowy adres email');
            return;
        }

        if (password.length < 6) {
            this.showAuthError('Hasło musi mieć co najmniej 6 znaków');
            return;
        }

        this.showAuthLoading('loginBtn');

        try {
            // Simulate API call
            await this.simulateLogin(email, password);
            
            const user = {
                id: Date.now(),
                name: email.split('@')[0],
                email: email,
                plan: 'free',
                avatar: null,
                createdAt: new Date().toISOString(),
                cards: [],
                analytics: {
                    totalViews: 0,
                    totalScans: 0,
                    totalContacts: 0
                }
            };

            this.saveUserToStorage(user);
            this.showAuthSuccess('Zalogowano pomyślnie!');
            
            setTimeout(() => {
                this.hideAuthModal();
                this.updateNavigation();
                this.redirectToDashboard();
            }, 1500);

        } catch (error) {
            this.showAuthError(error.message);
        } finally {
            this.hideAuthLoading('loginBtn');
        }
    }

    async handleRegister() {
        const name = document.getElementById('registerName').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const acceptTerms = document.getElementById('acceptTerms').checked;

        if (!name.trim()) {
            this.showAuthError('Podaj imię i nazwisko');
            return;
        }

        if (!this.validateEmail(email)) {
            this.showAuthError('Podaj prawidłowy adres email');
            return;
        }

        if (password !== confirmPassword) {
            this.showAuthError('Hasła nie są identyczne');
            return;
        }

        if (!acceptTerms) {
            this.showAuthError('Musisz zaakceptować regulamin');
            return;
        }

        const strength = this.getPasswordStrength(password);
        if (strength < 2) {
            this.showAuthError('Hasło jest za słabe. Użyj co najmniej 8 znaków z cyframi i znakami specjalnymi');
            return;
        }

        this.showAuthLoading('registerBtn');

        try {
            // Simulate API call
            await this.simulateRegister(name, email, password);
            
            const user = {
                id: Date.now(),
                name: name,
                email: email,
                plan: 'free',
                avatar: null,
                createdAt: new Date().toISOString(),
                cards: [],
                analytics: {
                    totalViews: 0,
                    totalScans: 0,
                    totalContacts: 0
                },
                onboarding: {
                    completed: false,
                    step: 1
                }
            };

            this.saveUserToStorage(user);
            this.showRegistrationSuccess();

        } catch (error) {
            this.showAuthError(error.message);
        } finally {
            this.hideAuthLoading('registerBtn');
        }
    }

    async handleSocialLogin(provider) {
        this.showNotification(`Logowanie przez ${provider.charAt(0).toUpperCase() + provider.slice(1)}...`, 'info');
        
        // Simulate OAuth flow
        setTimeout(() => {
            const user = {
                id: Date.now(),
                name: `User ${provider}`,
                email: `user@${provider}.com`,
                plan: 'free',
                avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${provider}`,
                provider: provider,
                createdAt: new Date().toISOString(),
                cards: [],
                analytics: {
                    totalViews: 0,
                    totalScans: 0,
                    totalContacts: 0
                }
            };

            this.saveUserToStorage(user);
            this.showAuthSuccess(`Zalogowano przez ${provider.charAt(0).toUpperCase() + provider.slice(1)}!`);
            
            setTimeout(() => {
                this.hideAuthModal();
                this.updateNavigation();
                this.redirectToDashboard();
            }, 1500);
        }, 2000);
    }

    simulateLogin(email, password) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Simulate different responses
                if (email === '<EMAIL>' && password === 'demo123') {
                    resolve();
                } else if (Math.random() > 0.3) { // 70% success rate for demo
                    resolve();
                } else {
                    reject(new Error('Nieprawidłowy email lub hasło'));
                }
            }, 1500);
        });
    }

    simulateRegister(name, email, password) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Simulate email already exists
                if (email === '<EMAIL>') {
                    reject(new Error('Konto z tym adresem email już istnieje'));
                } else {
                    resolve();
                }
            }, 2000);
        });
    }

    checkPasswordStrength(password) {
        const strength = this.getPasswordStrength(password);
        const strengthBar = document.querySelector('.strength-fill');
        const strengthText = document.querySelector('.strength-text');
        
        const levels = [
            { text: 'Bardzo słabe', color: '#ff4757', width: '20%' },
            { text: 'Słabe', color: '#ff6b47', width: '40%' },
            { text: 'Średnie', color: '#ffa726', width: '60%' },
            { text: 'Silne', color: '#66bb6a', width: '80%' },
            { text: 'Bardzo silne', color: '#43a047', width: '100%' }
        ];

        const level = levels[strength] || levels[0];
        
        strengthBar.style.width = level.width;
        strengthBar.style.background = level.color;
        strengthText.textContent = level.text;
        strengthText.style.color = level.color;
    }

    getPasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score++;
        if (password.length >= 12) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;
        
        return Math.min(Math.floor(score / 1.2), 4);
    }

    validatePasswordMatch() {
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const confirmInput = document.getElementById('confirmPassword');
        
        if (confirmPassword && password !== confirmPassword) {
            confirmInput.setCustomValidity('Hasła nie są identyczne');
            confirmInput.classList.add('error');
        } else {
            confirmInput.setCustomValidity('');
            confirmInput.classList.remove('error');
        }
    }

    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    showAuthLoading(buttonId) {
        const button = document.getElementById(buttonId);
        const originalHTML = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Przetwarzanie...</span>';
        button.dataset.originalHTML = originalHTML;
    }

    hideAuthLoading(buttonId) {
        const button = document.getElementById(buttonId);
        button.disabled = false;
        button.innerHTML = button.dataset.originalHTML;
    }

    showAuthError(message) {
        this.showNotification(message, 'error');
    }

    showAuthSuccess(message) {
        this.showNotification(message, 'success');
    }

    showRegistrationSuccess() {
        document.querySelectorAll('.auth-form').forEach(form => form.classList.add('hidden'));
        document.getElementById('authSuccess').classList.remove('hidden');
    }

    showForgotPassword() {
        const email = prompt('Podaj adres email, na który wyślemy link do resetowania hasła:');
        if (email && this.validateEmail(email)) {
            this.showNotification('Link do resetowania hasła został wysłany na podany adres email', 'success');
        } else if (email) {
            this.showNotification('Podaj prawidłowy adres email', 'error');
        }
    }

    updateNavigation() {
        const navCTA = document.querySelector('.nav-cta');
        if (!navCTA) return;

        if (this.currentUser) {
            navCTA.innerHTML = `
                <div class="user-menu">
                    <div class="user-avatar" onclick="toggleUserMenu()">
                        ${this.currentUser.avatar ? 
                            `<img src="${this.currentUser.avatar}" alt="${this.currentUser.name}">` :
                            `<i class="fas fa-user"></i>`
                        }
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="user-info">
                            <div class="user-name">${this.currentUser.name}</div>
                            <div class="user-plan">${this.currentUser.plan.toUpperCase()}</div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="#" onclick="goToDashboard()">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a href="#" onclick="goToProfile()">
                            <i class="fas fa-user-cog"></i> Profil
                        </a>
                        <a href="#" onclick="goToSettings()">
                            <i class="fas fa-cog"></i> Ustawienia
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Wyloguj
                        </a>
                    </div>
                </div>
            `;
        } else {
            navCTA.textContent = 'Rozpocznij za darmo';
            navCTA.onclick = () => this.showAuthModal('register');
        }
    }

    checkAuthState() {
        // Update UI based on auth state
        const authRequiredButtons = document.querySelectorAll('[data-auth-required]');
        authRequiredButtons.forEach(button => {
            if (!this.currentUser) {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showAuthModal();
                });
            }
        });

        // Update CTA buttons
        const startCreatingBtn = document.getElementById('startCreating');
        const pricingBtns = document.querySelectorAll('.pricing-btn');

        if (startCreatingBtn) {
            if (this.currentUser) {
                startCreatingBtn.onclick = () => this.redirectToDashboard();
            } else {
                startCreatingBtn.onclick = () => this.showAuthModal('register');
            }
        }

        pricingBtns.forEach(btn => {
            if (!this.currentUser) {
                btn.onclick = (e) => {
                    e.preventDefault();
                    this.showAuthModal('register');
                };
            }
        });
    }

    redirectToDashboard() {
        // Create dashboard if user is logged in
        if (this.currentUser) {
            this.createDashboard();
        }
    }

    createDashboard() {
        // Hide main content
        document.querySelector('.hero').style.display = 'none';
        document.querySelector('.creator-section').style.display = 'none';
        document.querySelector('.features-section').style.display = 'none';
        document.querySelector('.templates-section').style.display = 'none';
        document.querySelector('.pricing-section').style.display = 'none';

        // Create dashboard
        const dashboard = document.createElement('div');
        dashboard.id = 'userDashboard';
        dashboard.className = 'dashboard-container';
        dashboard.innerHTML = `
            <div class="dashboard-header">
                <div class="container">
                    <div class="dashboard-welcome">
                        <h1>Witaj, ${this.currentUser.name}! 👋</h1>
                        <p>Zarządzaj swoimi wizytówkami AI i analizuj statystyki</p>
                    </div>
                    <div class="dashboard-actions">
                        <button class="btn-primary" onclick="createNewCard()">
                            <i class="fas fa-plus"></i> Nowa wizytówka
                        </button>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="container">
                    <div class="dashboard-grid">
                        <!-- Stats Cards -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.currentUser.cards.length}</div>
                                    <div class="stat-label">Wizytówki</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.currentUser.analytics.totalViews}</div>
                                    <div class="stat-label">Wyświetlenia</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.currentUser.analytics.totalScans}</div>
                                    <div class="stat-label">Skanowania</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">${this.currentUser.analytics.totalContacts}</div>
                                    <div class="stat-label">Kontakty</div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Cards -->
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h3>Twoje wizytówki</h3>
                                <button class="btn-secondary" onclick="viewAllCards()">Zobacz wszystkie</button>
                            </div>
                            <div class="cards-grid" id="userCardsGrid">
                                ${this.renderUserCards()}
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h3>Szybkie akcje</h3>
                            </div>
                            <div class="quick-actions">
                                <button class="quick-action-btn" onclick="createNewCard()">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>Nowa wizytówka</span>
                                </button>
                                <button class="quick-action-btn" onclick="viewAnalytics()">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Analytics</span>
                                </button>
                                <button class="quick-action-btn" onclick="managePlan()">
                                    <i class="fas fa-crown"></i>
                                    <span>Upgrade plan</span>
                                </button>
                                <button class="quick-action-btn" onclick="inviteTeam()">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Zaproś zespół</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Insert dashboard after navigation
        const navbar = document.querySelector('.navbar');
        navbar.insertAdjacentElement('afterend', dashboard);
    }

    renderUserCards() {
        if (this.currentUser.cards.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h4>Nie masz jeszcze żadnych wizytówek</h4>
                    <p>Stwórz swoją pierwszą wizytówkę AI w zaledwie kilka minut</p>
                    <button class="btn-primary" onclick="createNewCard()">
                        <i class="fas fa-plus"></i> Stwórz pierwszą wizytówkę
                    </button>
                </div>
            `;
        }

        return this.currentUser.cards.map(card => `
            <div class="user-card-item" data-card-id="${card.id}">
                <div class="card-preview">
                    <!-- Card preview content -->
                </div>
                <div class="card-actions">
                    <button onclick="editCard('${card.id}')"><i class="fas fa-edit"></i></button>
                    <button onclick="shareCard('${card.id}')"><i class="fas fa-share"></i></button>
                    <button onclick="deleteCard('${card.id}')"><i class="fas fa-trash"></i></button>
                </div>
            </div>
        `).join('');
    }

    logout() {
        localStorage.removeItem('smartcard-user');
        this.currentUser = null;
        location.reload();
    }

    showNotification(message, type = 'info') {
        // Use existing notification system from main app
        if (window.SmartCardApp && window.SmartCardApp.prototype.showNotification) {
            const app = new window.SmartCardApp();
            app.showNotification(message, type);
        } else {
            alert(message); // Fallback
        }
    }
}

// Global functions for UI interactions
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling.nextElementSibling;
    const icon = toggle.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

function goToDashboard() {
    window.authSystem.redirectToDashboard();
    toggleUserMenu();
}

function goToProfile() {
    // Implement profile page
    toggleUserMenu();
}

function goToSettings() {
    // Implement settings page
    toggleUserMenu();
}

function logout() {
    window.authSystem.logout();
}

function createNewCard() {
    // Implement card creator
    console.log('Creating new card...');
}

function viewAllCards() {
    // Implement cards gallery
    console.log('Viewing all cards...');
}

function viewAnalytics() {
    // Implement analytics view
    console.log('Viewing analytics...');
}

function managePlan() {
    // Implement plan management
    console.log('Managing plan...');
}

function inviteTeam() {
    // Implement team invitation
    console.log('Inviting team...');
}

function editCard(cardId) {
    console.log('Editing card:', cardId);
}

function shareCard(cardId) {
    console.log('Sharing card:', cardId);
}

function deleteCard(cardId) {
    if (confirm('Czy na pewno chcesz usunąć tę wizytówkę?')) {
        console.log('Deleting card:', cardId);
    }
}

// Initialize authentication system
document.addEventListener('DOMContentLoaded', () => {
    window.authSystem = new AuthenticationSystem();
});

// CSS for authentication system
const authStyles = `
    .auth-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(10px);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .auth-modal-overlay.show {
        opacity: 1;
    }
    
    .auth-modal-overlay.hidden {
        display: none;
    }
    
    .auth-modal {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-xl);
        padding: 2rem;
        max-width: 450px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
        transform: translateY(-20px);
        transition: transform 0.3s ease;
    }
    
    .auth-modal-overlay.show .auth-modal {
        transform: translateY(0);
    }
    
    .auth-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }
    
    .auth-modal-header h2 {
        color: var(--dark-text);
        font-size: var(--font-size-2xl);
        font-weight: 700;
        margin: 0;
    }
    
    .close-auth {
        background: none;
        border: none;
        color: var(--dark-text-secondary);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all var(--animation-fast);
    }
    
    .close-auth:hover {
        background: var(--glass-bg);
        color: var(--dark-text);
    }
    
    .auth-tabs {
        display: flex;
        gap: 0;
        margin-bottom: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius-md);
        padding: 4px;
    }
    
    .auth-tab {
        flex: 1;
        padding: 12px 20px;
        background: transparent;
        border: none;
        color: var(--dark-text-secondary);
        font-weight: 500;
        cursor: pointer;
        border-radius: var(--border-radius-md);
        transition: all var(--animation-fast);
    }
    
    .auth-tab.active {
        background: var(--primary-gradient);
        color: white;
    }
    
    .auth-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .form-group {
        position: relative;
    }
    
    .form-group label {
        display: block;
        font-size: var(--font-size-sm);
        font-weight: 500;
        color: var(--dark-text-secondary);
        margin-bottom: 8px;
    }
    
    .form-group input {
        width: 100%;
        padding: 14px 48px 14px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-md);
        color: var(--dark-text);
        font-size: var(--font-size-base);
        transition: all var(--animation-fast);
    }
    
    .form-group input:focus {
        outline: none;
        border-color: rgba(102, 126, 234, 0.5);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: rgba(255, 255, 255, 0.08);
    }
    
    .form-group input.error {
        border-color: #ff4757;
        box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
    }
    
    .input-icon {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--dark-text-secondary);
        margin-top: 14px;
    }
    
    .password-toggle {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--dark-text-secondary);
        cursor: pointer;
        padding: 4px;
        margin-top: 14px;
        z-index: 2;
    }
    
    .password-strength {
        margin-top: 8px;
    }
    
    .strength-bar {
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 4px;
    }
    
    .strength-fill {
        height: 100%;
        width: 0;
        transition: all var(--animation-normal);
        border-radius: 2px;
    }
    
    .strength-text {
        font-size: var(--font-size-xs);
        font-weight: 500;
    }
    
    .form-options {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .checkbox-container {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        cursor: pointer;
        font-size: var(--font-size-sm);
        color: var(--dark-text-secondary);
        line-height: 1.4;
    }
    
    .checkbox-container input[type="checkbox"] {
        display: none;
    }
    
    .checkmark {
        width: 18px;
        height: 18px;
        background: transparent;
        border: 2px solid var(--glass-border);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all var(--animation-fast);
        margin-top: 1px;
    }
    
    .checkmark::after {
        content: '✓';
        color: white;
        font-size: 12px;
        font-weight: bold;
        opacity: 0;
        transition: opacity var(--animation-fast);
    }
    
    .checkbox-container input[type="checkbox"]:checked + .checkmark {
        background: var(--primary-gradient);
        border-color: transparent;
    }
    
    .checkbox-container input[type="checkbox"]:checked + .checkmark::after {
        opacity: 1;
    }
    
    .forgot-password {
        color: var(--primary-gradient);
        text-decoration: none;
        font-size: var(--font-size-sm);
        align-self: flex-end;
        margin-top: -8px;
    }
    
    .forgot-password:hover {
        text-decoration: underline;
    }
    
    .terms-link,
    .privacy-link {
        color: var(--primary-gradient);
        text-decoration: none;
    }
    
    .terms-link:hover,
    .privacy-link:hover {
        text-decoration: underline;
    }
    
    .auth-btn {
        padding: 14px 24px;
        border: none;
        border-radius: var(--border-radius-md);
        font-size: var(--font-size-base);
        font-weight: 600;
        cursor: pointer;
        transition: all var(--animation-normal);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        text-decoration: none;
    }
    
    .auth-btn.primary {
        background: var(--primary-gradient);
        color: white;
    }
    
    .auth-btn.primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .auth-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
    
    .auth-divider {
        text-align: center;
        position: relative;
        margin: 1rem 0;
        color: var(--dark-text-secondary);
        font-size: var(--font-size-sm);
    }
    
    .auth-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--glass-border);
    }
    
    .auth-divider span {
        background: var(--glass-bg);
        padding: 0 16px;
    }
    
    .social-login {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .social-btn {
        padding: 12px 20px;
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-md);
        background: transparent;
        color: var(--dark-text);
        font-weight: 500;
        cursor: pointer;
        transition: all var(--animation-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }
    
    .social-btn:hover {
        background: var(--glass-bg);
        transform: translateY(-1px);
    }
    
    .social-btn.google:hover {
        border-color: #db4437;
        color: #db4437;
    }
    
    .social-btn.linkedin:hover {
        border-color: #0077b5;
        color: #0077b5;
    }
    
    .auth-success {
        text-align: center;
        padding: 2rem 0;
    }
    
    .success-icon {
        font-size: 4rem;
        color: var(--success-gradient);
        margin-bottom: 1rem;
    }
    
    .auth-success h3 {
        color: var(--dark-text);
        margin-bottom: 1rem;
        font-size: var(--font-size-xl);
    }
    
    .auth-success p {
        color: var(--dark-text-secondary);
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    /* User Menu Styles */
    .user-menu {
        position: relative;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--animation-fast);
    }
    
    .user-avatar:hover {
        transform: scale(1.05);
    }
    
    .user-avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .user-avatar i {
        color: white;
        font-size: 1.2rem;
    }
    
    .user-dropdown {
        position: absolute;
        top: calc(100% + 8px);
        right: 0;
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-xl);
        min-width: 200px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all var(--animation-fast);
        z-index: 1000;
    }
    
    .user-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .user-info {
        padding: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }
    
    .user-name {
        font-weight: 600;
        color: var(--dark-text);
        margin-bottom: 4px;
    }
    
    .user-plan {
        font-size: var(--font-size-xs);
        color: var(--primary-gradient);
        text-transform: uppercase;
        font-weight: 600;
    }
    
    .user-dropdown a {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        color: var(--dark-text-secondary);
        text-decoration: none;
        transition: all var(--animation-fast);
        font-size: var(--font-size-sm);
    }
    
    .user-dropdown a:hover {
        background: rgba(255, 255, 255, 0.05);
        color: var(--dark-text);
    }
    
    .dropdown-divider {
        height: 1px;
        background: var(--glass-border);
        margin: 8px 0;
    }
    
    /* Dashboard Styles */
    .dashboard-container {
        min-height: 100vh;
        background: var(--dark-bg);
        padding-top: 100px;
    }
    
    .dashboard-header {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border-bottom: 1px solid var(--glass-border);
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .dashboard-welcome h1 {
        color: var(--dark-text);
        font-size: var(--font-size-3xl);
        font-weight: 800;
        margin-bottom: 0.5rem;
    }
    
    .dashboard-welcome p {
        color: var(--dark-text-secondary);
        font-size: var(--font-size-lg);
    }
    
    .dashboard-actions {
        display: flex;
        gap: 1rem;
    }
    
    .dashboard-grid {
        display: grid;
        gap: 2rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all var(--animation-fast);
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
        font-weight: 800;
        color: var(--dark-text);
    }
    
    .stat-label {
        font-size: var(--font-size-sm);
        color: var(--dark-text-secondary);
    }
    
    .dashboard-section {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-xl);
        padding: 2rem;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--glass-border);
    }
    
    .section-header h3 {
        color: var(--dark-text);
        font-size: var(--font-size-xl);
        font-weight: 600;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
    }
    
    .empty-icon {
        font-size: 4rem;
        color: var(--dark-text-secondary);
        margin-bottom: 1rem;
    }
    
    .empty-state h4 {
        color: var(--dark-text);
        font-size: var(--font-size-lg);
        margin-bottom: 0.5rem;
    }
    
    .empty-state p {
        color: var(--dark-text-secondary);
        margin-bottom: 2rem;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .quick-action-btn {
        background: transparent;
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-md);
        padding: 1.5rem 1rem;
        cursor: pointer;
        transition: all var(--animation-fast);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        color: var(--dark-text-secondary);
    }
    
    .quick-action-btn:hover {
        background: var(--glass-bg);
        border-color: var(--primary-gradient);
        color: var(--dark-text);
        transform: translateY(-2px);
    }
    
    .quick-action-btn i {
        font-size: 1.5rem;
        color: var(--primary-gradient);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-modal {
            padding: 1.5rem;
            margin: 1rem;
        }
        
        .dashboard-header .container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .quick-actions {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
    }
`;

// Add styles to document
const authStyleSheet = document.createElement('style');
authStyleSheet.textContent = authStyles;
document.head.appendChild(authStyleSheet);