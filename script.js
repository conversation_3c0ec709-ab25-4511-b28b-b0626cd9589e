// DIGITAL VELOCITY - Main JavaScript

// Global Variables
let currentSection = 'home';
let isLoading = true;
let projectsData = [];

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Start loading sequence
    startLoadingSequence();
    
    // Initialize components after loading
    setTimeout(() => {
        hideLoadingScreen();
        initializeNavigation();
        initializeScrollEffects();
        initializeCounters();
        initializeProjectsData();
        initializeParticles();
        initializeSoundEffects();
        // Initialize carousel after projects data is loaded
        setTimeout(() => {
            if (typeof renderProjectsCarousel === 'function') {
                renderProjectsCarousel();
            } else {
                console.error('❌ renderProjectsCarousel function not found');
                // Fallback: show simple project list
                showFallbackProjects();
            }
        }, 500);
    }, 3000);
}

// Loading Screen
function startLoadingSequence() {
    const loadingProgress = document.querySelector('.loading-progress');
    const loadingText = document.querySelector('.loading-text');
    
    const loadingSteps = [
        'Inicjalizacja systemu...',
        'Ładowanie projektów...',
        'Konfiguracja AI...',
        'Przygotowanie interfejsu...',
        'Gotowe do startu!'
    ];
    
    let currentStep = 0;
    
    const updateLoading = () => {
        if (currentStep < loadingSteps.length) {
            loadingText.textContent = loadingSteps[currentStep];
            currentStep++;
            setTimeout(updateLoading, 600);
        }
    };
    
    updateLoading();
}

function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    loadingScreen.style.opacity = '0';
    setTimeout(() => {
        loadingScreen.style.display = 'none';
        isLoading = false;
    }, 500);
}

// Navigation
function initializeNavigation() {
    const navbar = document.getElementById('navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileToggle = document.querySelector('.mobile-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    // Scroll effect for navbar
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetSection = link.getAttribute('data-section');
            navigateToSection(targetSection);
            updateActiveNavLink(link);
        });
    });
    
    // Mobile menu toggle
    mobileToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        mobileToggle.classList.toggle('active');
    });
    
    // CTA button
    document.querySelector('.nav-cta').addEventListener('click', () => {
        navigateToSection('contact');
    });
}

function navigateToSection(sectionId) {
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        currentSection = sectionId;
    }
}

function updateActiveNavLink(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

// Scroll Effects
function initializeScrollEffects() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe all sections
    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });
    
    // Parallax effect for hero background
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.hero-background');
        
        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });
}

// Counter Animation
function initializeCounters() {
    const counters = document.querySelectorAll('[data-count]');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const updateCounter = () => {
            current += step;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    };
    
    // Intersection Observer for counters
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Projects Data
function initializeProjectsData() {
    projectsData = [
        // AI & Automation Projects
        {
            id: 'ai-chat',
            title: 'AI Chat Support',
            category: 'ai',
            description: 'Inteligentny system obsługi klienta z AI',
            image: 'demos/ai-chat.html',
            technologies: ['AI', 'JavaScript', 'TailwindCSS'],
            features: ['Real-time chat', 'AI responses', 'Analytics'],
            demoUrl: 'demos/ai-chat.html'
        },
        {
            id: 'brandme',
            title: 'BrandMe.pl',
            category: 'web',
            description: 'Platforma budowania osobistej marki z AI',
            image: 'projects/brandme-pl/index.html',
            technologies: ['AI', 'CSS3', 'JavaScript'],
            features: ['AI tools', 'Analytics', 'Brand scoring'],
            demoUrl: 'projects/brandme-pl/index.html'
        },
        {
            id: 'fitgenius',
            title: 'FitGenius AI Coach',
            category: 'ai',
            description: 'Personalny trener AI dostępny 24/7',
            image: 'projects/fitgenius-ai-coach/index.html',
            technologies: ['AI', 'Machine Learning', 'PWA'],
            features: ['AI workouts', 'Progress tracking', 'Nutrition'],
            demoUrl: 'projects/fitgenius-ai-coach/index.html'
        },
        {
            id: 'gastro-ai',
            title: 'GastroAI Optimizer',
            category: 'ai',
            description: 'Inteligentny system zarządzania restauracją',
            image: 'projects/gastro-ai-restaurant-optimizer/index.html',
            technologies: ['AI', 'Analytics', 'Dashboard'],
            features: ['Menu optimization', 'Revenue tracking', 'AI insights'],
            demoUrl: 'projects/gastro-ai-restaurant-optimizer/index.html'
        },
        {
            id: 'smart-card',
            title: 'SmartCard.pl',
            category: 'ai',
            description: 'AI Generator wizytówek z avatarem',
            image: 'projects/card/index.html',
            technologies: ['AI', 'QR Codes', 'NFC'],
            features: ['AI avatars', 'QR generation', 'Analytics'],
            demoUrl: 'projects/card/index.html'
        },
        
        // Web Development Projects
        {
            id: 'dental-dashboard',
            title: 'Dental Practice Manager',
            category: 'dashboard',
            description: 'Kompleksowy system zarządzania praktyką dentystyczną',
            image: 'demos/dashboard-dental.html',
            technologies: ['Dashboard', 'TailwindCSS', 'JavaScript'],
            features: ['Patient management', 'Scheduling', 'Analytics'],
            demoUrl: 'demos/dashboard-dental.html'
        },
        {
            id: 'ecommerce-candles',
            title: 'E-commerce Candles',
            category: 'web',
            description: 'Sklep internetowy ze świecami',
            image: 'demos/ecommerce-candles.html',
            technologies: ['E-commerce', 'CSS3', 'JavaScript'],
            features: ['Product catalog', 'Shopping cart', 'Checkout'],
            demoUrl: 'demos/ecommerce-candles.html'
        },
        {
            id: 'autoexpert',
            title: 'AutoExpert Kraków',
            category: 'web',
            description: 'Serwis samochodowy - strona firmowa',
            image: 'projects/autoexpert-krakow/index.html',
            technologies: ['HTML5', 'CSS3', 'JavaScript'],
            features: ['Service booking', 'Gallery', 'Contact forms'],
            demoUrl: 'projects/autoexpert-krakow/index.html'
        },
        {
            id: 'design-michal',
            title: 'Design by Michał',
            category: 'web',
            description: 'Portfolio designera graficznego',
            image: 'projects/design-by-michal/index.html',
            technologies: ['Portfolio', 'CSS3', 'Animations'],
            features: ['Project showcase', 'Gallery', 'Contact'],
            demoUrl: 'projects/design-by-michal/index.html'
        },
        {
            id: 'style-studio',
            title: 'Style Studio Warszawa',
            category: 'web',
            description: 'Salon piękności - elegancka strona',
            image: 'projects/style-studio-warszawa/index.html',
            technologies: ['Beauty', 'CSS3', 'Booking'],
            features: ['Service booking', 'Gallery', 'Pricing'],
            demoUrl: 'projects/style-studio-warszawa/index.html'
        },
        
        // Mobile & Apps
        {
            id: 'english-express',
            title: 'English Express',
            category: 'mobile',
            description: 'Aplikacja do nauki języka angielskiego',
            image: 'projects/english-express/index.html',
            technologies: ['PWA', 'Learning', 'Interactive'],
            features: ['Lessons', 'Progress tracking', 'Quizzes'],
            demoUrl: 'projects/english-express/index.html'
        },
        {
            id: 'fitlife-pro',
            title: 'FitLife Pro',
            category: 'mobile',
            description: 'Aplikacja fitness z trackingiem',
            image: 'projects/fitlife-pro/index.html',
            technologies: ['Fitness', 'Tracking', 'PWA'],
            features: ['Workout tracking', 'Progress', 'Goals'],
            demoUrl: 'projects/fitlife-pro/index.html'
        },
        {
            id: 'event-planner',
            title: 'Smart Event Planner',
            category: 'mobile',
            description: 'Inteligentny planer wydarzeń',
            image: 'projects/smart-event-planner/index.html',
            technologies: ['Events', 'AI', 'Planning'],
            features: ['Event creation', 'Guest management', 'AI suggestions'],
            demoUrl: 'projects/smart-event-planner/index.html'
        },
        
        // Dashboards & Analytics
        {
            id: 'business-law',
            title: 'Business Law Dashboard',
            category: 'dashboard',
            description: 'Dashboard dla kancelarii prawnej',
            image: 'demos/business-law.html',
            technologies: ['Legal', 'Dashboard', 'Analytics'],
            features: ['Case management', 'Client tracking', 'Reports'],
            demoUrl: 'demos/business-law.html'
        },
        {
            id: 'technomax',
            title: 'TechnoMax Solutions',
            category: 'web',
            description: 'Firma technologiczna - strona korporacyjna',
            image: 'projects/technomax/index.html',
            technologies: ['Corporate', 'Tech', 'Modern'],
            features: ['Services', 'Portfolio', 'Contact'],
            demoUrl: 'projects/technomax/index.html'
        },
        
        // Health & Wellness
        {
            id: 'terapia-dostepna',
            title: 'Terapia Dostępna',
            category: 'web',
            description: 'Platforma terapeutyczna online',
            image: 'projects/terapia-dostepna/index.html',
            technologies: ['Health', 'Booking', 'Therapy'],
            features: ['Therapist booking', 'Online sessions', 'Resources'],
            demoUrl: 'projects/terapia-dostepna/index.html'
        },
        {
            id: 'zmien-zycie',
            title: 'Zmień Życie z Anną',
            category: 'web',
            description: 'Life coaching - transformacja osobista',
            image: 'projects/zmien-zycie-z-anna/index.html',
            technologies: ['Coaching', 'Personal', 'Growth'],
            features: ['Coaching programs', 'Resources', 'Community'],
            demoUrl: 'projects/zmien-zycie-z-anna/index.html'
        },
        
        // Landing Pages
        {
            id: 'saas-landing',
            title: 'SaaS Landing Page',
            category: 'web',
            description: 'Nowoczesna strona dla aplikacji SaaS',
            image: 'demos/landing-saas.html',
            technologies: ['SaaS', 'Landing', 'Conversion'],
            features: ['Feature showcase', 'Pricing', 'CTA optimization'],
            demoUrl: 'demos/landing-saas.html'
        },
        {
            id: 'trainer-landing',
            title: 'Personal Trainer Landing',
            category: 'web',
            description: 'Strona dla trenera personalnego',
            image: 'demos/landing-trainer.html',
            technologies: ['Fitness', 'Personal', 'Booking'],
            features: ['Service showcase', 'Testimonials', 'Booking'],
            demoUrl: 'demos/landing-trainer.html'
        },
        {
            id: 'web3-landing',
            title: 'Web3 Crypto Landing',
            category: 'web',
            description: 'Futurystyczna strona dla projektu crypto',
            image: 'demos/landing-web3.html',
            technologies: ['Web3', 'Crypto', 'Blockchain'],
            features: ['Token info', 'Roadmap', 'Community'],
            demoUrl: 'demos/landing-web3.html'
        }
    ];

    // Make projectsData globally available
    window.projectsData = projectsData;
    console.log('✅ Projects data loaded:', projectsData.length, 'projects');
}

// Hero Interactions
function initializeHeroInteractions() {
    const exploreBtn = document.getElementById('exploreProjects');
    const watchDemoBtn = document.getElementById('watchDemo');
    const projectCards = document.querySelectorAll('.project-card');
    
    exploreBtn.addEventListener('click', () => {
        navigateToSection('projects');
        addGlitchEffect(exploreBtn);
    });
    
    watchDemoBtn.addEventListener('click', () => {
        showDemoModal();
        addGlitchEffect(watchDemoBtn);
    });
    
    projectCards.forEach(card => {
        card.addEventListener('click', () => {
            const projectId = card.getAttribute('data-project');
            showProjectPreview(projectId);
        });
    });
}

// Project Filter
function initializeProjectFilter() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Filter projects
            filterProjects(filter);
            addGlitchEffect(btn);
        });
    });
}

function filterProjects(category) {
    const filteredProjects = category === 'all' 
        ? projectsData 
        : projectsData.filter(project => project.category === category);
    
    if (typeof renderProjectsCarousel === 'function') {
        renderProjectsCarousel(filteredProjects);
    } else {
        showFallbackProjects(filteredProjects);
    }
}

// Fallback Projects Display
function showFallbackProjects(projects = null) {
    const carouselContainer = document.getElementById('projectsCarousel');
    if (!carouselContainer) return;

    const projectsToShow = projects || window.projectsData || [];

    if (projectsToShow.length === 0) {
        carouselContainer.innerHTML = `
            <div class="fallback-projects">
                <h3>🚀 Portfolio Projektów</h3>
                <p>Ładowanie projektów...</p>
                <div class="loading-spinner"></div>
            </div>
        `;
        return;
    }

    const projectsHTML = projectsToShow.map(project => `
        <div class="fallback-project-card">
            <div class="project-icon">
                <i class="fas fa-${getProjectIcon(project.category)}"></i>
            </div>
            <div class="project-info">
                <h4>${project.title}</h4>
                <p class="project-category">${getCategoryName(project.category)}</p>
                <p class="project-desc">${project.description}</p>
                <div class="project-tech">
                    ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                </div>
                <a href="${project.demoUrl}" target="_blank" class="project-link">
                    <i class="fas fa-external-link-alt"></i>
                    Zobacz Demo
                </a>
            </div>
        </div>
    `).join('');

    carouselContainer.innerHTML = `
        <div class="fallback-projects">
            <div class="projects-grid-fallback">
                ${projectsHTML}
            </div>
        </div>
    `;
}

function getProjectIcon(category) {
    const icons = {
        'ai': 'robot',
        'web': 'globe',
        'mobile': 'mobile-alt',
        'dashboard': 'chart-bar'
    };
    return icons[category] || 'code';
}

function getCategoryName(category) {
    const names = {
        'ai': 'AI & Automation',
        'web': 'Web Development',
        'mobile': 'Mobile Apps',
        'dashboard': 'Dashboards'
    };
    return names[category] || 'Development';
}

// Effects
function addGlitchEffect(element) {
    element.classList.add('glitch-effect');
    setTimeout(() => {
        element.classList.remove('glitch-effect');
    }, 300);
}

function addNeonPulse(element) {
    element.classList.add('neon-pulse');
    setTimeout(() => {
        element.classList.remove('neon-pulse');
    }, 1000);
}

// Sound Effects (Optional)
function initializeSoundEffects() {
    // Create audio context for UI sounds
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    
    function playClickSound() {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }
    
    // Add sound to interactive elements
    document.querySelectorAll('button, .nav-link, .project-card').forEach(element => {
        element.addEventListener('click', playClickSound);
    });
}

// Modal Functions
function showDemoModal() {
    // Create and show demo modal
    const modal = document.createElement('div');
    modal.className = 'demo-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Demo Reel - Digital Velocity</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="demo-video">
                    <div class="video-placeholder">
                        <i class="fas fa-play"></i>
                        <p>Demo video będzie dostępne wkrótce</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    modal.querySelector('.modal-close').addEventListener('click', () => {
        modal.remove();
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function showProjectPreview(projectId) {
    const project = projectsData.find(p => p.id === projectId);
    if (!project) return;
    
    // Open project in new tab
    window.open(project.demoUrl, '_blank');
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Contact Form
function initializeContactForm() {
    const contactForm = document.getElementById('contactForm');
    if (!contactForm) return;

    contactForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const submitBtn = contactForm.querySelector('.submit-btn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Wysyłanie...';
        submitBtn.disabled = true;

        // Simulate form submission
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Show success state
        submitBtn.innerHTML = '<i class="fas fa-check"></i> Wysłano!';
        submitBtn.style.background = 'linear-gradient(135deg, #00ff41, #00f5ff)';

        // Show success message
        showNotification('Dziękujemy za wiadomość! Skontaktujemy się z Tobą wkrótce.', 'success');

        // Reset form
        setTimeout(() => {
            contactForm.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            submitBtn.style.background = '';
        }, 3000);
    });
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: ${type === 'success' ? 'linear-gradient(135deg, #00ff41, #00f5ff)' : 'var(--gradient-primary)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 1rem;
        max-width: 400px;
        animation: slideInRight 0.5s ease-out;
    `;

    document.body.appendChild(notification);

    // Auto remove
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => notification.remove(), 500);
    }, 5000);

    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => notification.remove(), 500);
    });
}

// Smooth Section Navigation
function initializeSectionNavigation() {
    // Update active nav link based on scroll position
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[data-section]');

    const observerOptions = {
        threshold: 0.3,
        rootMargin: '-100px 0px -100px 0px'
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.id;

                // Update active nav link
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('data-section') === sectionId) {
                        link.classList.add('active');
                    }
                });

                currentSection = sectionId;
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        sectionObserver.observe(section);
    });
}

// Advanced Scroll Effects
function initializeAdvancedScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        const scrolled = window.pageYOffset;
        const windowHeight = window.innerHeight;

        // Parallax for hero elements
        const heroElements = document.querySelectorAll('.floating-cards .project-card');
        heroElements.forEach((element, index) => {
            const speed = 0.1 + (index * 0.05);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px) ${element.style.transform.replace(/translateY\([^)]*\)/, '')}`;
        });

        // Scale effect for section headers
        const sectionHeaders = document.querySelectorAll('.section-header');
        sectionHeaders.forEach(header => {
            const rect = header.getBoundingClientRect();
            const elementTop = rect.top;
            const elementHeight = rect.height;

            if (elementTop < windowHeight && elementTop > -elementHeight) {
                const progress = Math.max(0, Math.min(1, (windowHeight - elementTop) / (windowHeight + elementHeight)));
                const scale = 0.8 + (progress * 0.2);
                header.style.transform = `scale(${scale})`;
                header.style.opacity = progress;
            }
        });

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// Easter Eggs
function initializeEasterEggs() {
    let konamiCode = [];
    const konamiSequence = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65]; // ↑↑↓↓←→←→BA

    document.addEventListener('keydown', (e) => {
        konamiCode.push(e.keyCode);

        if (konamiCode.length > konamiSequence.length) {
            konamiCode.shift();
        }

        if (konamiCode.length === konamiSequence.length &&
            konamiCode.every((code, index) => code === konamiSequence[index])) {
            activateMatrixMode();
        }
    });

    // Secret click sequence on logo
    let logoClicks = 0;
    const logo = document.querySelector('.nav-logo');
    if (logo) {
        logo.addEventListener('click', () => {
            logoClicks++;
            if (logoClicks === 5) {
                activateNeonMode();
                logoClicks = 0;
            }
            setTimeout(() => logoClicks = 0, 3000);
        });
    }
}

function activateMatrixMode() {
    document.body.classList.add('matrix-mode');
    showNotification('🎉 Matrix Mode Activated! Welcome to the digital realm.', 'success');

    // Add matrix rain to all sections
    document.querySelectorAll('section').forEach(section => {
        section.classList.add('matrix-bg');
        new MatrixRain(section);
    });

    setTimeout(() => {
        document.body.classList.remove('matrix-mode');
        document.querySelectorAll('.matrix-bg').forEach(section => {
            section.classList.remove('matrix-bg');
        });
    }, 30000);
}

function activateNeonMode() {
    document.body.classList.add('neon-mode');
    showNotification('✨ Neon Mode Activated! Feel the glow!', 'success');

    // Add extra neon effects
    document.querySelectorAll('h1, h2, h3, .btn-primary, .nav-link').forEach(element => {
        createNeonGlow(element);
    });

    setTimeout(() => {
        document.body.classList.remove('neon-mode');
    }, 15000);
}

// Performance Monitoring
function initializePerformanceMonitoring() {
    // Monitor FPS
    let fps = 0;
    let lastTime = performance.now();
    let frameCount = 0;

    function calculateFPS() {
        frameCount++;
        const currentTime = performance.now();

        if (currentTime >= lastTime + 1000) {
            fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            frameCount = 0;
            lastTime = currentTime;

            // Log performance if FPS drops below 30
            if (fps < 30) {
                console.warn(`Low FPS detected: ${fps}`);
            }
        }

        requestAnimationFrame(calculateFPS);
    }

    calculateFPS();

    // Monitor page load time
    window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`Page loaded in ${Math.round(loadTime)}ms`);

        if (loadTime > 3000) {
            console.warn('Slow page load detected');
        }
    });
}

// Instant Contact
function initializeInstantContact() {
    const contactToggle = document.getElementById('contactToggle');
    const contactOptions = document.getElementById('contactOptions');

    if (!contactToggle || !contactOptions) return;

    let isOpen = false;

    contactToggle.addEventListener('click', () => {
        isOpen = !isOpen;
        contactOptions.classList.toggle('active', isOpen);

        // Rotate toggle icon
        const icon = contactToggle.querySelector('i');
        icon.style.transform = isOpen ? 'rotate(45deg)' : 'rotate(0deg)';

        // Add glitch effect
        addGlitchEffect(contactToggle);

        // Play sound
        playContactSound();
    });

    // Close when clicking outside
    document.addEventListener('click', (e) => {
        if (!contactToggle.contains(e.target) && !contactOptions.contains(e.target)) {
            isOpen = false;
            contactOptions.classList.remove('active');
            const icon = contactToggle.querySelector('i');
            icon.style.transform = 'rotate(0deg)';
        }
    });

    // Track contact clicks
    const contactLinks = document.querySelectorAll('.contact-option');
    contactLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const platform = link.getAttribute('data-platform');

            // Add click effect
            addNeonPulse(link);

            // Show notification
            showNotification(`Otwieranie ${platform}... 🚀`, 'success');

            // Analytics (if needed)
            console.log(`📞 Contact clicked: ${platform}`);

            // Play success sound
            playSuccessSound();
        });
    });
}

function playContactSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (e) {
        // Audio not supported
    }
}

function playSuccessSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(1000, audioContext.currentTime + 0.2);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (e) {
        // Audio not supported
    }
}

// Initialize all features when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        initializeHeroInteractions();
        initializeProjectFilter();
        initializeContactForm();
        initializeSectionNavigation();
        initializeAdvancedScrollEffects();
        initializeEasterEggs();
        initializePerformanceMonitoring();
        initializeInstantContact();
    }, 3500);
});

// Add notification styles
const notificationStyles = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
    }

    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 50%;
        transition: background 0.3s ease;
    }

    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .matrix-mode {
        filter: hue-rotate(120deg);
    }

    .neon-mode * {
        text-shadow: 0 0 10px currentColor !important;
        box-shadow: 0 0 20px currentColor !important;
    }
`;

// Add styles to document
if (!document.querySelector('#notification-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'notification-styles';
    styleSheet.textContent = notificationStyles;
    document.head.appendChild(styleSheet);
}
