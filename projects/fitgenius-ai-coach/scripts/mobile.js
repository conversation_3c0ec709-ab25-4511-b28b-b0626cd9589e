/**
 * FitGenius.pl - Mobile Optimizations & Touch Interactions
 * Enterprise-grade mobile experience with native-like interactions
 */

const MobileOptimizer = {
    // Mobile device detection
    device: {
        isTouch: 'ontouchstart' in window,
        isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
        isAndroid: /Android/.test(navigator.userAgent),
        isMobile: window.innerWidth <= 768,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
    },
    
    // Touch interaction state
    touchState: {
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0,
        startTime: 0,
        isScrolling: false,
        lastTap: 0
    },
    
    // Performance monitoring
    performance: {
        frameRate: 60,
        lastFrameTime: 0,
        isLowPerformance: false
    },
    
    // Initialize mobile optimizations
    init() {
        this.detectDevice();
        this.setupViewportFixes();
        this.setupTouchInteractions();
        this.setupGestureHandling();
        this.setupMobileNavigation();
        this.setupScrollOptimizations();
        this.setupPerformanceOptimizations();
        this.setupAccessibilityEnhancements();
        this.setupPullToRefresh();
        this.setupOfflineHandling();
        console.log('📱 Mobile Optimizer initialized - Enhanced mobile experience ready!');
    },
    
    detectDevice() {
        // Enhanced device detection
        this.device.pixelRatio = window.devicePixelRatio || 1;
        this.device.isRetina = this.device.pixelRatio > 1;
        this.device.screenSize = {
            width: screen.width,
            height: screen.height
        };
        
        // Detect specific device capabilities
        this.device.hasAccelerometer = 'DeviceMotionEvent' in window;
        this.device.hasGyroscope = 'DeviceOrientationEvent' in window;
        this.device.hasVibration = 'vibrate' in navigator;
        this.device.hasCamera = 'getUserMedia' in navigator.mediaDevices;
        
        // Performance indicators
        this.device.hardwareConcurrency = navigator.hardwareConcurrency || 2;
        this.device.deviceMemory = navigator.deviceMemory || 4;
        this.device.connection = navigator.connection;
        
        // Set performance mode
        this.performance.isLowPerformance = (
            this.device.hardwareConcurrency <= 2 ||
            this.device.deviceMemory <= 2 ||
            (this.device.connection && this.device.connection.effectiveType === 'slow-2g')
        );
        
        // Add device classes to body
        document.body.classList.add(
            this.device.isTouch ? 'touch-device' : 'no-touch',
            this.device.isIOS ? 'ios' : '',
            this.device.isAndroid ? 'android' : '',
            this.device.isMobile ? 'mobile' : 'desktop',
            this.performance.isLowPerformance ? 'low-performance' : 'high-performance'
        );
    },
    
    setupViewportFixes() {
        // iOS Safari viewport height fix
        if (this.device.isIOS) {
            const setViewportHeight = () => {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            };
            
            setViewportHeight();
            window.addEventListener('resize', setViewportHeight);
            window.addEventListener('orientationchange', () => {
                setTimeout(setViewportHeight, 100);
            });
        }
        
        // Prevent zoom on form inputs
        if (this.device.isIOS) {
            document.addEventListener('touchstart', (e) => {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    e.target.style.fontSize = '16px';
                }
            });
        }
        
        // Disable overscroll behavior
        document.body.style.overscrollBehavior = 'none';
    },
    
    setupTouchInteractions() {
        // Enhanced touch event handling
        let touchStartTime = 0;
        let lastTouchEnd = 0;
        
        document.addEventListener('touchstart', (e) => {
            this.touchState.startX = e.touches[0].clientX;
            this.touchState.startY = e.touches[0].clientY;
            this.touchState.startTime = Date.now();
            touchStartTime = Date.now();
            
            // Add touch feedback
            this.addTouchFeedback(e.target);
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
            this.touchState.endX = e.touches[0].clientX;
            this.touchState.endY = e.touches[0].clientY;
            
            // Prevent rubber band scrolling at boundaries
            this.preventRubberBandScrolling(e);
        }, { passive: false });
        
        document.addEventListener('touchend', (e) => {
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - touchStartTime;
            
            // Handle double tap
            if (touchEndTime - lastTouchEnd < 300 && touchDuration < 200) {
                this.handleDoubleTap(e);
            }
            
            // Handle swipe gestures
            this.handleSwipeGestures(e);
            
            // Remove touch feedback
            this.removeTouchFeedback(e.target);
            
            lastTouchEnd = touchEndTime;
        }, { passive: true });
        
        // Haptic feedback for supported devices
        if (this.device.hasVibration) {
            this.setupHapticFeedback();
        }
    },
    
    addTouchFeedback(element) {
        if (element.classList.contains('btn-primary') || 
            element.classList.contains('btn-secondary') ||
            element.classList.contains('feature-cta') ||
            element.classList.contains('nav-link')) {
            
            element.style.transform = 'scale(0.95)';
            element.style.transition = 'transform 0.1s ease';
            
            // Haptic feedback
            if (this.device.hasVibration) {
                navigator.vibrate(10);
            }
        }
    },
    
    removeTouchFeedback(element) {
        if (element.classList.contains('btn-primary') || 
            element.classList.contains('btn-secondary') ||
            element.classList.contains('feature-cta') ||
            element.classList.contains('nav-link')) {
            
            element.style.transform = '';
            element.style.transition = '';
        }
    },
    
    preventRubberBandScrolling(e) {
        const element = e.target.closest('.scrollable-content');
        if (!element) return;
        
        const scrollTop = element.scrollTop;
        const scrollHeight = element.scrollHeight;
        const clientHeight = element.clientHeight;
        
        const deltaY = this.touchState.endY - this.touchState.startY;
        
        // Prevent overscroll at top
        if (scrollTop === 0 && deltaY > 0) {
            e.preventDefault();
        }
        
        // Prevent overscroll at bottom
        if (scrollTop >= scrollHeight - clientHeight && deltaY < 0) {
            e.preventDefault();
        }
    },
    
    handleDoubleTap(e) {
        const target = e.target.closest('.feature-card, .testimonial-card, .pricing-card');
        if (target) {
            // Zoom in on double tap
            target.style.transform = 'scale(1.05)';
            target.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                target.style.transform = '';
            }, 300);
            
            // Track double tap
            FitGenius.analytics.trackEvent('mobile_double_tap', {
                elementType: target.className,
                timestamp: new Date().toISOString()
            });
        }
    },
    
    handleSwipeGestures(e) {
        const deltaX = this.touchState.endX - this.touchState.startX;
        const deltaY = this.touchState.endY - this.touchState.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const duration = Date.now() - this.touchState.startTime;
        
        // Minimum swipe distance and maximum duration
        if (distance < 50 || duration > 300) return;
        
        const angle = Math.atan2(Math.abs(deltaY), Math.abs(deltaX)) * 180 / Math.PI;
        
        // Horizontal swipe
        if (angle < 45) {
            if (deltaX > 0) {
                this.handleSwipeRight(e);
            } else {
                this.handleSwipeLeft(e);
            }
        }
        // Vertical swipe
        else {
            if (deltaY > 0) {
                this.handleSwipeDown(e);
            } else {
                this.handleSwipeUp(e);
            }
        }
    },
    
    handleSwipeRight(e) {
        // Navigate back or open menu
        const workoutGenerator = e.target.closest('.generator-form');
        if (workoutGenerator && window.WorkoutGenerator) {
            WorkoutGenerator.prevStep();
        }
        
        FitGenius.analytics.trackEvent('mobile_swipe', {
            direction: 'right',
            element: e.target.className
        });
    },
    
    handleSwipeLeft(e) {
        // Navigate forward or close menu
        const workoutGenerator = e.target.closest('.generator-form');
        if (workoutGenerator && window.WorkoutGenerator) {
            WorkoutGenerator.nextStep();
        }
        
        FitGenius.analytics.trackEvent('mobile_swipe', {
            direction: 'left',
            element: e.target.className
        });
    },
    
    handleSwipeDown(e) {
        // Pull to refresh
        if (window.scrollY === 0) {
            this.triggerPullToRefresh();
        }
        
        FitGenius.analytics.trackEvent('mobile_swipe', {
            direction: 'down',
            element: e.target.className
        });
    },
    
    handleSwipeUp(e) {
        // Hide/show navigation
        this.toggleNavigationVisibility();
        
        FitGenius.analytics.trackEvent('mobile_swipe', {
            direction: 'up',
            element: e.target.className
        });
    },
    
    setupGestureHandling() {
        // Pinch to zoom handling
        let initialDistance = 0;
        let currentScale = 1;
        
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length === 2) {
                initialDistance = this.getDistance(e.touches[0], e.touches[1]);
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (e.touches.length === 2) {
                e.preventDefault();
                const currentDistance = this.getDistance(e.touches[0], e.touches[1]);
                const scale = currentDistance / initialDistance;
                
                // Handle pinch zoom on charts/images
                const target = e.target.closest('.dashboard-card, .phone-mockup');
                if (target && scale !== currentScale) {
                    this.handlePinchZoom(target, scale);
                    currentScale = scale;
                }
            }
        }, { passive: false });
        
        // Rotation gesture handling
        if (this.device.hasGyroscope) {
            this.setupRotationGestures();
        }
    },
    
    getDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    },
    
    handlePinchZoom(element, scale) {
        if (scale > 1.1 && scale < 3) {
            element.style.transform = `scale(${scale})`;
            element.style.transformOrigin = 'center';
            element.style.transition = 'none';
            
            // Reset after gesture ends
            setTimeout(() => {
                element.style.transform = '';
                element.style.transition = '';
            }, 500);
        }
    },
    
    setupRotationGestures() {
        window.addEventListener('deviceorientation', (e) => {
            const alpha = e.alpha; // Z axis
            const beta = e.beta;   // X axis
            const gamma = e.gamma; // Y axis
            
            // Use rotation for interactive elements
            this.handleDeviceRotation(alpha, beta, gamma);
        });
    },
    
    handleDeviceRotation(alpha, beta, gamma) {
        // Subtle parallax effect based on device orientation
        const parallaxElements = document.querySelectorAll('.phone-mockup, .hero::before');
        
        parallaxElements.forEach(element => {
            const x = gamma / 90 * 10; // Max 10px movement
            const y = beta / 90 * 10;
            
            element.style.transform = `translateX(${x}px) translateY(${y}px)`;
        });
    },
    
    setupMobileNavigation() {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (hamburger && navMenu) {
            hamburger.addEventListener('click', () => {
                const isOpen = navMenu.classList.contains('active');
                
                navMenu.classList.toggle('active');
                hamburger.classList.toggle('active');
                
                // Prevent body scroll when menu is open
                document.body.style.overflow = isOpen ? '' : 'hidden';
                
                // Haptic feedback
                if (this.device.hasVibration) {
                    navigator.vibrate(30);
                }
                
                FitGenius.analytics.trackEvent('mobile_menu_toggle', {
                    action: isOpen ? 'close' : 'open'
                });
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                    navMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
            
            // Close menu on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                    navMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        }
    },
    
    setupScrollOptimizations() {
        let isScrolling = false;
        let scrollTimeout;
        
        // Optimized scroll handling
        window.addEventListener('scroll', () => {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    this.handleOptimizedScroll();
                    isScrolling = false;
                });
            }
            
            // Clear timeout and set new one
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.handleScrollEnd();
            }, 150);
        }, { passive: true });
        
        // Momentum scrolling for iOS
        if (this.device.isIOS) {
            document.body.style.webkitOverflowScrolling = 'touch';
        }
    },
    
    handleOptimizedScroll() {
        const scrollY = window.scrollY;
        
        // Hide/show navbar on scroll
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (scrollY > 100) {
                navbar.classList.add('scrolled');
                if (scrollY > this.lastScrollY && scrollY > 200) {
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    navbar.style.transform = 'translateY(0)';
                }
            } else {
                navbar.classList.remove('scrolled');
                navbar.style.transform = 'translateY(0)';
            }
            
            this.lastScrollY = scrollY;
        }
        
        // Parallax effects (reduced for performance)
        if (!this.performance.isLowPerformance) {
            const parallaxElements = document.querySelectorAll('.phone-mockup');
            parallaxElements.forEach(element => {
                const rate = scrollY * -0.3;
                element.style.transform = `translateY(${rate}px)`;
            });
        }
    },
    
    handleScrollEnd() {
        // Actions to perform when scrolling stops
        this.updateVisibleElements();
    },
    
    updateVisibleElements() {
        // Lazy load content that becomes visible
        const elements = document.querySelectorAll('[data-lazy-load]');
        elements.forEach(element => {
            if (this.isElementVisible(element)) {
                this.loadLazyContent(element);
            }
        });
    },
    
    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },
    
    loadLazyContent(element) {
        // Load content for lazy-loaded elements
        const contentType = element.dataset.lazyLoad;
        
        switch (contentType) {
            case 'chart':
                this.loadChartContent(element);
                break;
            case 'image':
                this.loadImageContent(element);
                break;
            case 'analytics':
                this.loadAnalyticsContent(element);
                break;
        }
        
        element.removeAttribute('data-lazy-load');
    },
    
    setupPerformanceOptimizations() {
        // Monitor frame rate
        this.monitorFrameRate();
        
        // Adjust quality based on performance
        if (this.performance.isLowPerformance) {
            this.enableLowPerformanceMode();
        }
        
        // Battery optimization
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                if (battery.level < 0.2) {
                    this.enableBatterySavingMode();
                }
                
                battery.addEventListener('levelchange', () => {
                    if (battery.level < 0.2) {
                        this.enableBatterySavingMode();
                    }
                });
            });
        }
    },
    
    monitorFrameRate() {
        let frameCount = 0;
        let startTime = performance.now();
        
        const countFrame = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - startTime >= 1000) {
                this.performance.frameRate = Math.round((frameCount * 1000) / (currentTime - startTime));
                
                if (this.performance.frameRate < 30) {
                    this.enableLowPerformanceMode();
                }
                
                frameCount = 0;
                startTime = currentTime;
            }
            
            requestAnimationFrame(countFrame);
        };
        
        requestAnimationFrame(countFrame);
    },
    
    enableLowPerformanceMode() {
        document.body.classList.add('low-performance-mode');
        
        // Disable complex animations
        const style = document.createElement('style');
        style.textContent = `
            .low-performance-mode * {
                animation-duration: 0.1s !important;
                transition-duration: 0.1s !important;
            }
            .low-performance-mode .glass-effect {
                backdrop-filter: none !important;
            }
            .low-performance-mode .phone-mockup {
                transform: none !important;
                animation: none !important;
            }
        `;
        document.head.appendChild(style);
        
        console.log('🔋 Low performance mode enabled');
    },
    
    enableBatterySavingMode() {
        document.body.classList.add('battery-saving-mode');
        
        // Reduce animation frequency
        const style = document.createElement('style');
        style.textContent = `
            .battery-saving-mode body::before {
                animation-duration: 60s !important;
            }
            .battery-saving-mode .hero::before {
                animation: none !important;
            }
        `;
        document.head.appendChild(style);
        
        console.log('🔋 Battery saving mode enabled');
    },
    
    setupAccessibilityEnhancements() {
        // Enhanced focus management for mobile
        this.setupFocusManagement();
        
        // Voice commands (if available)
        if ('webkitSpeechRecognition' in window) {
            this.setupVoiceCommands();
        }
        
        // Screen reader optimizations
        this.setupScreenReaderOptimizations();
    },
    
    setupFocusManagement() {
        // Improve focus visibility on mobile
        const focusableElements = document.querySelectorAll(
            'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
        );
        
        focusableElements.forEach(element => {
            element.addEventListener('focus', () => {
                element.style.outline = '3px solid var(--primary-color)';
                element.style.outlineOffset = '2px';
                
                // Scroll into view if needed
                element.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
            });
            
            element.addEventListener('blur', () => {
                element.style.outline = '';
                element.style.outlineOffset = '';
            });
        });
    },
    
    setupVoiceCommands() {
        const recognition = new webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.lang = 'pl-PL';
        
        const voiceCommands = {
            'rozpocznij trening': () => {
                const startBtn = document.getElementById('startAssessment');
                if (startBtn) startBtn.click();
            },
            'pokaż menu': () => {
                const hamburger = document.querySelector('.hamburger');
                if (hamburger) hamburger.click();
            },
            'przewiń w górę': () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            },
            'przewiń w dół': () => {
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            }
        };
        
        // Add voice command button
        const voiceBtn = document.createElement('button');
        voiceBtn.innerHTML = '🎤';
        voiceBtn.className = 'voice-command-btn';
        voiceBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            border: none;
            font-size: 24px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: var(--shadow-lg);
        `;
        
        voiceBtn.addEventListener('click', () => {
            recognition.start();
            voiceBtn.style.background = 'var(--accent-color)';
        });
        
        recognition.addEventListener('result', (e) => {
            const command = e.results[0][0].transcript.toLowerCase();
            const action = voiceCommands[command];
            
            if (action) {
                action();
                if (this.device.hasVibration) {
                    navigator.vibrate(50);
                }
            }
            
            voiceBtn.style.background = 'var(--primary-color)';
        });
        
        recognition.addEventListener('error', () => {
            voiceBtn.style.background = 'var(--primary-color)';
        });
        
        document.body.appendChild(voiceBtn);
    },
    
    setupPullToRefresh() {
        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        const threshold = 100;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
            if (window.scrollY === 0 && startY > 0) {
                currentY = e.touches[0].clientY;
                pullDistance = currentY - startY;
                
                if (pullDistance > 0) {
                    document.body.classList.add('pulling');
                    
                    // Visual feedback
                    const refreshIndicator = this.getRefreshIndicator();
                    refreshIndicator.style.transform = `translateY(${Math.min(pullDistance * 0.5, 50)}px)`;
                    refreshIndicator.style.opacity = Math.min(pullDistance / threshold, 1);
                    
                    if (pullDistance > threshold) {
                        refreshIndicator.textContent = '↻ Puść aby odświeżyć';
                    } else {
                        refreshIndicator.textContent = '↓ Pociągnij aby odświeżyć';
                    }
                }
            }
        }, { passive: true });
        
        document.addEventListener('touchend', () => {
            if (pullDistance > threshold) {
                this.triggerPullToRefresh();
            }
            
            document.body.classList.remove('pulling');
            this.hideRefreshIndicator();
            startY = 0;
            pullDistance = 0;
        }, { passive: true });
    },
    
    getRefreshIndicator() {
        let indicator = document.querySelector('.refresh-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'refresh-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%) translateY(-100px);
                background: var(--glass-background);
                backdrop-filter: blur(10px);
                padding: var(--spacing-sm) var(--spacing-lg);
                border-radius: var(--radius-lg);
                font-size: var(--font-size-sm);
                font-weight: 600;
                color: var(--text-primary);
                border: 1px solid var(--glass-border);
                z-index: 1001;
                opacity: 0;
                transition: all 0.3s ease;
            `;
            indicator.textContent = '↓ Pociągnij aby odświeżyć';
            document.body.appendChild(indicator);
        }
        return indicator;
    },
    
    hideRefreshIndicator() {
        const indicator = document.querySelector('.refresh-indicator');
        if (indicator) {
            indicator.style.transform = 'translateX(-50%) translateY(-100px)';
            indicator.style.opacity = '0';
        }
    },
    
    triggerPullToRefresh() {
        const indicator = this.getRefreshIndicator();
        indicator.textContent = '⟳ Odświeżanie...';
        indicator.style.transform = 'translateX(-50%) translateY(0)';
        indicator.style.opacity = '1';
        
        // Haptic feedback
        if (this.device.hasVibration) {
            navigator.vibrate([50, 50, 50]);
        }
        
        // Simulate refresh
        setTimeout(() => {
            // Refresh data
            if (window.AnalyticsDashboard) {
                AnalyticsDashboard.refreshDashboard();
            }
            
            indicator.textContent = '✓ Odświeżono';
            
            setTimeout(() => {
                this.hideRefreshIndicator();
            }, 1000);
        }, 1500);
        
        FitGenius.analytics.trackEvent('pull_to_refresh', {
            timestamp: new Date().toISOString()
        });
    },
    
    setupOfflineHandling() {
        // Monitor network status
        window.addEventListener('online', () => {
            this.showNetworkStatus('Połączenie przywrócone', 'success');
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.showNetworkStatus('Brak połączenia', 'warning');
            this.enableOfflineMode();
        });
        
        // Check initial network status
        if (!navigator.onLine) {
            this.enableOfflineMode();
        }
    },
    
    showNetworkStatus(message, type) {
        const notification = document.createElement('div');
        notification.className = `network-status ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100%);
            background: ${type === 'success' ? '#10b981' : '#f59e0b'};
            color: white;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--radius-lg);
            font-size: var(--font-size-sm);
            font-weight: 600;
            z-index: 10002;
            transition: transform 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(-50%) translateY(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(-50%) translateY(-100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    },
    
    enableOfflineMode() {
        document.body.classList.add('offline-mode');
        
        // Cache current page data
        this.cachePageData();
        
        // Show offline indicator
        this.showOfflineIndicator();
    },
    
    cachePageData() {
        const dataToCache = {
            workoutHistory: FitGenius.utils.loadFromLocalStorage('workout_history'),
            userProfile: FitGenius.utils.loadFromLocalStorage('user_profile'),
            analyticsData: FitGenius.utils.loadFromLocalStorage('user_analytics_data'),
            cachedAt: new Date().toISOString()
        };
        
        FitGenius.utils.saveToLocalStorage('offline_cache', dataToCache);
    },
    
    syncOfflineData() {
        const offlineData = FitGenius.utils.loadFromLocalStorage('offline_cache');
        if (offlineData) {
            // In a real app, this would sync with the server
            console.log('Syncing offline data:', offlineData);
            
            // Clear offline cache after sync
            localStorage.removeItem('fitgenius_offline_cache');
        }
    },
    
    showOfflineIndicator() {
        let indicator = document.querySelector('.offline-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'offline-indicator';
            indicator.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--glass-background);
                backdrop-filter: blur(10px);
                padding: var(--spacing-sm) var(--spacing-lg);
                border-radius: var(--radius-lg);
                font-size: var(--font-size-sm);
                color: var(--text-primary);
                border: 1px solid var(--glass-border);
                z-index: 1000;
            `;
            indicator.innerHTML = '📱 Tryb offline - dane zapisane lokalnie';
            document.body.appendChild(indicator);
        }
    },
    
    toggleNavigationVisibility() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            const isHidden = navbar.style.transform === 'translateY(-100%)';
            navbar.style.transform = isHidden ? 'translateY(0)' : 'translateY(-100%)';
        }
    },
    
    setupHapticFeedback() {
        // Enhanced haptic patterns
        const hapticPatterns = {
            light: 10,
            medium: 30,
            heavy: 50,
            success: [10, 100, 10],
            error: [50, 50, 50, 50, 50],
            notification: [30, 100, 30]
        };
        
        // Add haptic feedback to various interactions
        document.addEventListener('achievementUnlocked', () => {
            navigator.vibrate(hapticPatterns.success);
        });
        
        document.addEventListener('workoutCompleted', () => {
            navigator.vibrate(hapticPatterns.heavy);
        });
        
        document.addEventListener('error', () => {
            navigator.vibrate(hapticPatterns.error);
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    MobileOptimizer.init();
});

// Handle orientation changes
window.addEventListener('orientationchange', () => {
    setTimeout(() => {
        MobileOptimizer.device.orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
        MobileOptimizer.device.isMobile = window.innerWidth <= 768;
        
        // Trigger layout recalculation
        window.dispatchEvent(new Event('resize'));
        
        FitGenius.analytics.trackEvent('orientation_change', {
            orientation: MobileOptimizer.device.orientation,
            screenSize: `${window.innerWidth}x${window.innerHeight}`
        });
    }, 100);
});

// Export for global access
window.MobileOptimizer = MobileOptimizer;