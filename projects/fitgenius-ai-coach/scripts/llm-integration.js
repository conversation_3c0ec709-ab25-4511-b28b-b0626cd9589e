/**
 * FitGenius.pl - LLM Integration with Hugging Face
 * Production-ready AI integration with Llama and DeepSeek models
 */

class LLMIntegration {
    constructor() {
        this.config = {
            hfToken: '*************************************',
            llamaModel: 'meta-llama/Meta-Llama-3.1-8B-Instruct',
            deepseekModel: 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
            useLlama: true,
            apiBaseUrl: 'https://api-inference.huggingface.co/models',
            timeout: 30000,
            maxRetries: 3
        };
        
        this.requestQueue = [];
        this.isProcessing = false;
        this.rateLimiter = new Map();
        this.cache = new Map();
        
        this.init();
    }
    
    init() {
        this.setupRateLimiting();
        this.setupErrorHandling();
        this.setupCaching();
        console.log('🤖 LLM Integration initialized with Hugging Face API');
    }
    
    setupRateLimiting() {
        // Rate limiting: max 100 requests per hour
        this.maxRequestsPerHour = 100;
        this.requestWindow = 3600000; // 1 hour in ms
        
        setInterval(() => {
            this.rateLimiter.clear();
        }, this.requestWindow);
    }
    
    setupErrorHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.source === 'llm-integration') {
                console.error('LLM Integration Error:', event.reason);
                this.handleLLMError(event.reason);
            }
        });
    }
    
    setupCaching() {
        // Cache responses for 1 hour to reduce API calls
        this.cacheTimeout = 3600000;
        
        setInterval(() => {
            const now = Date.now();
            for (const [key, value] of this.cache.entries()) {
                if (now - value.timestamp > this.cacheTimeout) {
                    this.cache.delete(key);
                }
            }
        }, 300000); // Clean cache every 5 minutes
    }
    
    async generateText(prompt, options = {}) {
        const cacheKey = this.generateCacheKey(prompt, options);
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            console.log('📋 Using cached LLM response');
            return cached.response;
        }
        
        // Check rate limiting
        if (!this.checkRateLimit()) {
            throw new Error('Rate limit exceeded. Please try again later.');
        }
        
        try {
            const response = await this.makeRequest(prompt, options);
            
            // Cache the response
            this.cache.set(cacheKey, {
                response: response,
                timestamp: Date.now()
            });
            
            return response;
        } catch (error) {
            console.error('LLM Generation Error:', error);
            return this.getFallbackResponse(prompt, options);
        }
    }
    
    async makeRequest(prompt, options) {
        const model = this.config.useLlama ? this.config.llamaModel : this.config.deepseekModel;
        const url = `${this.config.apiBaseUrl}/${model}`;
        
        const payload = {
            inputs: prompt,
            parameters: {
                max_new_tokens: options.maxTokens || 150,
                temperature: options.temperature || 0.7,
                top_p: options.topP || 0.9,
                do_sample: true,
                return_full_text: false
            },
            options: {
                wait_for_model: true,
                use_cache: false
            }
        };
        
        const requestOptions = {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.hfToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        };
        
        let retries = 0;
        while (retries < this.config.maxRetries) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
                
                const response = await fetch(url, {
                    ...requestOptions,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    if (response.status === 503) {
                        // Model loading, wait and retry
                        await this.sleep(2000);
                        retries++;
                        continue;
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (Array.isArray(data) && data.length > 0) {
                    return data[0].generated_text || data[0].text || '';
                } else if (data.generated_text) {
                    return data.generated_text;
                } else {
                    throw new Error('Invalid response format from API');
                }
                
            } catch (error) {
                retries++;
                if (retries >= this.config.maxRetries) {
                    throw error;
                }
                await this.sleep(1000 * retries); // Exponential backoff
            }
        }
    }
    
    checkRateLimit() {
        const now = Date.now();
        const requests = this.rateLimiter.get('requests') || [];
        
        // Remove old requests outside the window
        const validRequests = requests.filter(time => now - time < this.requestWindow);
        
        if (validRequests.length >= this.maxRequestsPerHour) {
            return false;
        }
        
        validRequests.push(now);
        this.rateLimiter.set('requests', validRequests);
        return true;
    }
    
    generateCacheKey(prompt, options) {
        const key = JSON.stringify({ prompt, options });
        return btoa(key).substring(0, 50); // Base64 encode and truncate
    }
    
    getFallbackResponse(prompt, options) {
        const type = options.type || 'general';
        
        const fallbacks = {
            workout_feedback: 'Świetna robota! Kontynuuj ten rytm i pamiętaj o odpowiednim oddychaniu.',
            nutrition_advice: 'Skup się na zrównoważonej diecie z odpowiednią ilością białka, węglowodanów i zdrowych tłuszczów.',
            motivation: 'Każdy dzień to nowa szansa na postęp. Ty możesz to zrobić!',
            form_correction: 'Pamiętaj o prawidłowej postawie i kontrolowanym tempie wykonywania ćwiczeń.',
            goal_setting: 'Ustaw konkretne, mierzalne cele i podziel je na mniejsze, osiągalne kroki.',
            general: 'Dziękuję za pytanie. Kontynuuj swoją fitness journey z determinacją!'
        };
        
        return fallbacks[type] || fallbacks.general;
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    handleLLMError(error) {
        FitGenius.analytics.trackEvent('llm_error', {
            error: error.message,
            timestamp: new Date().toISOString()
        });
        
        // Show user-friendly error message
        if (window.FitGenius && FitGenius.forms) {
            FitGenius.forms.showNotification(
                'AI jest chwilowo niedostępny. Używamy zapasowych odpowiedzi.',
                'info'
            );
        }
    }
    
    // Specialized methods for different use cases
    async generateWorkoutAdvice(userProfile, workoutData) {
        const prompt = `
        Jako ekspert fitness, udziel spersonalizowanej porady treningowej w języku polskim.
        
        Profil użytkownika:
        - Cel: ${userProfile.goal || 'ogólna kondycja'}
        - Poziom: ${userProfile.level || 'początkujący'}
        - Dostępny czas: ${userProfile.timeAvailable || '30'} minut
        
        Dane treningu:
        - Rodzaj: ${workoutData.type || 'mieszany'}
        - Ćwiczenia: ${workoutData.exercises || 'podstawowe'}
        - Czas trwania: ${workoutData.duration || 30} minut
        
        Udziel krótkiej (max 100 słów), motywującej porady treningowej z konkretnymi wskazówkami.
        `;
        
        return await this.generateText(prompt, {
            type: 'workout_feedback',
            maxTokens: 100,
            temperature: 0.7
        });
    }
    
    async generateNutritionAdvice(userGoal, currentMacros, targetMacros) {
        const prompt = `
        Jako dietetyk sportowy, udziel porady żywieniowej w języku polskim.
        
        Cel użytkownika: ${userGoal}
        
        Aktualne makro (dzisiaj):
        - Białko: ${currentMacros.protein}g
        - Węglowodany: ${currentMacros.carbs}g
        - Tłuszcze: ${currentMacros.fat}g
        
        Cel makro:
        - Białko: ${targetMacros.protein}g
        - Węglowodany: ${targetMacros.carbs}g
        - Tłuszcze: ${targetMacros.fat}g
        
        Udziel krótkiej (max 80 słów) porady żywieniowej z konkretnymi zaleceniami produktów.
        `;
        
        return await this.generateText(prompt, {
            type: 'nutrition_advice',
            maxTokens: 80,
            temperature: 0.6
        });
    }
    
    async generateMotivationalMessage(userMood, streakDays, recentProgress) {
        const prompt = `
        Jako coach motywacyjny, napisz inspirującą wiadomość w języku polskim.
        
        Nastrój użytkownika: ${userMood}
        Dni z rzędu: ${streakDays}
        Ostatnie postępy: ${recentProgress}
        
        Napisz krótką (max 60 słów), personalną wiadomość motywującą do kontynuowania treningów.
        `;
        
        return await this.generateText(prompt, {
            type: 'motivation',
            maxTokens: 60,
            temperature: 0.8
        });
    }
    
    async generateFormFeedback(exerciseName, detectedIssues) {
        const prompt = `
        Jako trener personalny, udziel korekty techniki ćwiczenia w języku polskim.
        
        Ćwiczenie: ${exerciseName}
        Wykryte problemy: ${detectedIssues.join(', ')}
        
        Udziel krótkiej (max 50 słów) korekty techniki z konkretnymi wskazówkami.
        `;
        
        return await this.generateText(prompt, {
            type: 'form_correction',
            maxTokens: 50,
            temperature: 0.5
        });
    }
    
    async generateGoalSetting(currentGoals, userProgress, timeframe) {
        const prompt = `
        Jako coach fitness, pomóż ustalić cele treningowe w języku polskim.
        
        Obecne cele: ${currentGoals}
        Postępy: ${userProgress}
        Czas na realizację: ${timeframe}
        
        Zaproponuj konkretny, mierzalny cel na następny okres (max 70 słów).
        `;
        
        return await this.generateText(prompt, {
            type: 'goal_setting',
            maxTokens: 70,
            temperature: 0.6
        });
    }
    
    async generateWorkoutPlan(userProfile) {
        const prompt = `
        Jako ekspert fitness, stwórz plan treningowy w języku polskim.
        
        Profil:
        - Cel: ${userProfile.goal}
        - Poziom: ${userProfile.level}
        - Czas: ${userProfile.time} minut
        - Sprzęt: ${userProfile.equipment}
        
        Stwórz listę 4-6 ćwiczeń z liczbą serii i powtórzeń. Format: "Nazwa ćwiczenia - X serie x Y powtórzeń"
        `;
        
        return await this.generateText(prompt, {
            type: 'workout_plan',
            maxTokens: 200,
            temperature: 0.5
        });
    }
    
    // Health and safety checks
    async validateWorkoutSafety(workoutPlan, userLimitations) {
        const prompt = `
        Jako fizjoterapeuta sportowy, oceń bezpieczeństwo planu treningowego.
        
        Plan treningowy: ${workoutPlan}
        Ograniczenia użytkownika: ${userLimitations}
        
        Oceń bezpieczeństwo i podaj ewentualne ostrzeżenia (max 60 słów).
        `;
        
        return await this.generateText(prompt, {
            type: 'safety_check',
            maxTokens: 60,
            temperature: 0.3
        });
    }
    
    // Analytics and insights
    async generateProgressInsight(progressData) {
        const prompt = `
        Jako analityk fitness, przeanalizuj postępy użytkownika w języku polskim.
        
        Dane postępów: ${JSON.stringify(progressData)}
        
        Podaj główny insight i rekomendację na przyszłość (max 80 słów).
        `;
        
        return await this.generateText(prompt, {
            type: 'progress_insight',
            maxTokens: 80,
            temperature: 0.6
        });
    }
}

// Initialize LLM Integration
const llmIntegration = new LLMIntegration();

// Export for global access
window.LLMIntegration = llmIntegration;

// Integration with existing systems
if (window.FitGenius) {
    FitGenius.llm = llmIntegration;
}