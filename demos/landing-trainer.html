<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitPro Personal Training</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        'accent-glow': '#FF8C42'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%); }
        .glass-effect { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px); }
    </style>
</head>
<body class="gradient-bg">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-white">FitPro</div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#services" class="text-white/80 hover:text-white transition-colors">Services</a>
                    <a href="#about" class="text-white/80 hover:text-white transition-colors">About</a>
                    <a href="#testimonials" class="text-white/80 hover:text-white transition-colors">Reviews</a>
                    <a href="#contact" class="text-white/80 hover:text-white transition-colors">Contact</a>
                </div>
                <button class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                    Book Session
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-16 px-4">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                        Transform Your
                        <span class="text-orange-500 block">Body & Mind</span>
                    </h1>
                    <p class="text-xl text-white/80 mb-8">
                        Achieve your fitness goals with personalized training programs designed just for you. 
                        Get stronger, healthier, and more confident with expert guidance.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                            Start Your Journey
                        </button>
                        <button class="border-2 border-white/30 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/10 transition-colors">
                            Free Consultation
                        </button>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="glass-effect rounded-2xl p-8 border border-white/10">
                        <img 
                            src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop" 
                            alt="Personal Trainer" 
                            class="w-full h-80 object-cover rounded-xl"
                        />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-16 px-4">
        <div class="container mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-white mb-4">Training Services</h2>
                <p class="text-xl text-white/80">Comprehensive fitness solutions tailored to your needs</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="glass-effect rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors">
                    <div class="w-12 h-12 text-orange-500 mb-4">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Personal Training</h3>
                    <p class="text-white/80 mb-4">One-on-one sessions focused on your specific goals and fitness level.</p>
                    <div class="text-orange-500 font-bold text-lg">$80/session</div>
                </div>

                <div class="glass-effect rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors">
                    <div class="w-12 h-12 text-orange-500 mb-4">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Group Classes</h3>
                    <p class="text-white/80 mb-4">High-energy group workouts that motivate and challenge you.</p>
                    <div class="text-orange-500 font-bold text-lg">$25/class</div>
                </div>

                <div class="glass-effect rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors">
                    <div class="w-12 h-12 text-orange-500 mb-4">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Nutrition Coaching</h3>
                    <p class="text-white/80 mb-4">Personalized meal plans and nutrition guidance for optimal results.</p>
                    <div class="text-orange-500 font-bold text-lg">$60/session</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section id="testimonials" class="py-16 px-4 bg-black/20">
        <div class="container mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-white mb-4">Success Stories</h2>
                <p class="text-xl text-white/80">Real results from real people</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass-effect rounded-xl p-6 border border-white/10">
                    <div class="flex items-center mb-4">
                        <span class="text-orange-500">★★★★★</span>
                    </div>
                    <p class="text-white/80 mb-4 italic">"The personalized approach made all the difference. I finally found a sustainable way to stay fit!"</p>
                    <div>
                        <div class="text-white font-semibold">Sarah Johnson</div>
                        <div class="text-orange-500 text-sm">Lost 30 lbs in 4 months</div>
                    </div>
                </div>

                <div class="glass-effect rounded-xl p-6 border border-white/10">
                    <div class="flex items-center mb-4">
                        <span class="text-orange-500">★★★★★</span>
                    </div>
                    <p class="text-white/80 mb-4 italic">"Professional, knowledgeable, and motivating. Exceeded all my expectations."</p>
                    <div>
                        <div class="text-white font-semibold">Mike Chen</div>
                        <div class="text-orange-500 text-sm">Gained 15 lbs muscle</div>
                    </div>
                </div>

                <div class="glass-effect rounded-xl p-6 border border-white/10">
                    <div class="flex items-center mb-4">
                        <span class="text-orange-500">★★★★★</span>
                    </div>
                    <p class="text-white/80 mb-4 italic">"Not just about fitness - learned so much about nutrition and healthy living."</p>
                    <div>
                        <div class="text-white font-semibold">Lisa Rodriguez</div>
                        <div class="text-orange-500 text-sm">Improved overall health</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 px-4">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h2 class="text-4xl font-bold text-white mb-6">Ready to Start?</h2>
                    <p class="text-xl text-white/80 mb-8">
                        Take the first step towards your fitness goals. Book a free consultation today!
                    </p>
                    
                    <div class="space-y-4">
                        <div class="flex items-center gap-3">
                            <span class="text-orange-500">📞</span>
                            <span class="text-white">(555) 123-4567</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="text-orange-500">✉️</span>
                            <span class="text-white"><EMAIL></span>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="text-orange-500">📍</span>
                            <span class="text-white">123 Fitness St, Health City, HC 12345</span>
                        </div>
                    </div>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10">
                    <h3 class="text-2xl font-bold text-white mb-6">Book Free Consultation</h3>
                    <form class="space-y-4">
                        <input
                            type="text"
                            placeholder="Your Name"
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-orange-500"
                        />
                        <input
                            type="email"
                            placeholder="Email Address"
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-orange-500"
                        />
                        <input
                            type="tel"
                            placeholder="Phone Number"
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-orange-500"
                        />
                        <textarea
                            placeholder="Tell us about your fitness goals"
                            rows="4"
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-orange-500 resize-none"
                        ></textarea>
                        <button
                            type="submit"
                            class="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-semibold transition-colors"
                        >
                            Schedule Free Consultation
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-8 px-4 border-t border-white/10">
        <div class="container mx-auto text-center">
            <div class="text-2xl font-bold text-white mb-4">FitPro</div>
            <p class="text-white/60">© 2024 FitPro Personal Training. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
