// Configuration Management System for SmartCard.pl
class ConfigManager {
    constructor() {
        this.config = this.initializeConfig();
        this.validateConfig();
    }

    initializeConfig() {
        return {
            // API Configuration
            api: {
                baseUrl: window.location.origin,
                timeout: 30000,
                retries: 3
            },

            // HuggingFace Configuration
            huggingface: {
                token: '*************************************',
                baseUrl: 'https://api-inference.huggingface.co/models',
                models: {
                    llama: 'meta-llama/Meta-Llama-3.1-8B-Instruct',
                    deepseek: 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
                    avatarGenerator: 'runwayml/stable-diffusion-v1-5',
                    textOptimizer: 'facebook/bart-large-cnn',
                    translator: 'Helsinki-NLP/opus-mt-en-pl'
                },
                useLlama: true,
                useDeepSeek: true,
                maxTokens: 2048,
                temperature: 0.7
            },

            // Feature Flags
            features: {
                aiAvatarGeneration: true,
                realTimePreview: true,
                voiceRecording: true,
                nfcSimulation: true,
                qrGeneration: true,
                analytics: true,
                teamManagement: true,
                exportToPdf: true,
                socialSharing: true,
                autoSave: true,
                multiLanguage: true,
                darkMode: true,
                notifications: true,
                onboarding: true
            },

            // Plan Limits
            plans: {
                free: {
                    maxCards: 1,
                    maxTemplates: 3,
                    aiGenerations: 5,
                    analytics: 'basic',
                    support: 'community',
                    features: ['qrGeneration', 'basicTemplates', 'basicAnalytics']
                },
                pro: {
                    maxCards: -1, // unlimited
                    maxTemplates: -1,
                    aiGenerations: 100,
                    analytics: 'advanced',
                    support: 'priority',
                    features: ['allFeatures']
                },
                business: {
                    maxCards: -1,
                    maxTemplates: -1,
                    aiGenerations: -1,
                    analytics: 'enterprise',
                    support: 'dedicated',
                    features: ['allFeatures', 'teamManagement', 'apiAccess', 'whiteLabel']
                }
            },

            // UI Configuration
            ui: {
                defaultLanguage: 'pl',
                defaultTheme: 'dark',
                animationSpeed: 'normal',
                autoSaveInterval: 30000, // 30 seconds
                maxFileSize: 5 * 1024 * 1024, // 5MB
                supportedImageFormats: ['jpg', 'jpeg', 'png', 'webp', 'gif'],
                supportedExportFormats: ['png', 'jpg', 'pdf', 'svg'],
                maxDescriptionLength: 100,
                maxNameLength: 50
            },

            // Analytics Configuration
            analytics: {
                enabled: true,
                trackingId: 'smartcard-analytics',
                realTimeUpdates: true,
                retentionDays: 365,
                aggregationInterval: 3600000 // 1 hour
            },

            // Storage Configuration
            storage: {
                prefix: 'smartcard-',
                version: '1.0',
                encryption: false,
                compression: true,
                maxStorageSize: 50 * 1024 * 1024 // 50MB
            },

            // Security Configuration
            security: {
                sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
                passwordMinLength: 8,
                requireSpecialChar: true,
                requireNumber: true,
                requireUppercase: true,
                maxLoginAttempts: 5,
                lockoutDuration: 15 * 60 * 1000 // 15 minutes
            },

            // Export Configuration
            export: {
                defaultFormat: 'png',
                defaultSize: 'business',
                quality: 0.92,
                dpi: 300,
                includeQR: true,
                watermark: false
            },

            // Sharing Configuration
            sharing: {
                publicLinkExpiry: 30 * 24 * 60 * 60 * 1000, // 30 days
                enableSocialShare: true,
                enableDirectDownload: true,
                trackShares: true,
                allowedDomains: []
            },

            // Development Configuration
            development: {
                debug: false,
                mockAPI: false,
                logLevel: 'warn',
                enableConsoleCommands: false
            }
        };
    }

    validateConfig() {
        // Validate HuggingFace token
        if (!this.config.huggingface.token) {
            console.warn('HuggingFace token not configured');
        }

        // Validate required features
        const requiredFeatures = ['qrGeneration', 'realTimePreview'];
        requiredFeatures.forEach(feature => {
            if (!this.config.features[feature]) {
                console.warn(`Required feature ${feature} is disabled`);
            }
        });

        // Validate storage limits
        if (this.getStorageUsage() > this.config.storage.maxStorageSize) {
            console.warn('Storage limit exceeded');
            this.cleanupStorage();
        }
    }

    // Getter methods
    get(path) {
        return this.getNestedValue(this.config, path);
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    // Feature flag checking
    isFeatureEnabled(feature) {
        return this.get(`features.${feature}`) === true;
    }

    // Plan checking
    getUserPlanLimits(planName = 'free') {
        return this.get(`plans.${planName}`) || this.get('plans.free');
    }

    canUserAccessFeature(feature, userPlan = 'free') {
        const planConfig = this.getUserPlanLimits(userPlan);
        return planConfig.features.includes('allFeatures') || planConfig.features.includes(feature);
    }

    // Storage management
    getStorageUsage() {
        let totalSize = 0;
        for (let key in localStorage) {
            if (key.startsWith(this.config.storage.prefix)) {
                totalSize += localStorage[key].length;
            }
        }
        return totalSize;
    }

    cleanupStorage() {
        // Remove old data to free up space
        const keys = Object.keys(localStorage).filter(key => 
            key.startsWith(this.config.storage.prefix)
        );
        
        // Sort by last modified and remove oldest
        keys.sort((a, b) => {
            const aData = JSON.parse(localStorage[a] || '{}');
            const bData = JSON.parse(localStorage[b] || '{}');
            return (aData.lastModified || 0) - (bData.lastModified || 0);
        });

        // Remove oldest 25% of items
        const toRemove = keys.slice(0, Math.floor(keys.length * 0.25));
        toRemove.forEach(key => localStorage.removeItem(key));
    }

    // Configuration updates
    updateConfig(path, value) {
        this.setNestedValue(this.config, path, value);
        this.saveConfigToStorage();
        this.validateConfig();
    }

    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key]) current[key] = {};
            return current[key];
        }, obj);
        target[lastKey] = value;
    }

    saveConfigToStorage() {
        try {
            localStorage.setItem(
                `${this.config.storage.prefix}config`,
                JSON.stringify({
                    config: this.config,
                    lastModified: Date.now(),
                    version: this.config.storage.version
                })
            );
        } catch (error) {
            console.error('Failed to save config to storage:', error);
        }
    }

    loadConfigFromStorage() {
        try {
            const stored = localStorage.getItem(`${this.config.storage.prefix}config`);
            if (stored) {
                const data = JSON.parse(stored);
                if (data.version === this.config.storage.version) {
                    this.config = { ...this.config, ...data.config };
                }
            }
        } catch (error) {
            console.error('Failed to load config from storage:', error);
        }
    }

    // Environment detection
    isProduction() {
        return window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1');
    }

    isDevelopment() {
        return !this.isProduction();
    }

    // API configuration
    getApiConfig() {
        return {
            baseUrl: this.config.api.baseUrl,
            timeout: this.config.api.timeout,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.huggingface.token}`
            }
        };
    }

    getHuggingFaceConfig() {
        return {
            token: this.config.huggingface.token,
            baseUrl: this.config.huggingface.baseUrl,
            models: this.config.huggingface.models,
            options: {
                max_tokens: this.config.huggingface.maxTokens,
                temperature: this.config.huggingface.temperature,
                use_cache: true,
                wait_for_model: true
            }
        };
    }

    // Model selection based on configuration
    getActiveModel(type = 'general') {
        const modelMap = {
            general: this.config.huggingface.useLlama ? 'llama' : 'deepseek',
            avatar: 'avatarGenerator',
            text: 'textOptimizer',
            translation: 'translator'
        };
        
        const modelKey = modelMap[type] || 'general';
        return this.config.huggingface.models[modelKey];
    }

    // Performance monitoring
    getPerformanceConfig() {
        return {
            enableMetrics: this.isFeatureEnabled('analytics'),
            sampleRate: this.isDevelopment() ? 1.0 : 0.1,
            maxMetricsAge: 24 * 60 * 60 * 1000 // 24 hours
        };
    }

    // Error handling configuration
    getErrorHandlingConfig() {
        return {
            enableErrorReporting: this.isProduction(),
            maxRetries: this.config.api.retries,
            retryDelay: 1000,
            enableFallbacks: true
        };
    }

    // Export current configuration for debugging
    exportConfig() {
        return {
            timestamp: new Date().toISOString(),
            version: this.config.storage.version,
            environment: this.isProduction() ? 'production' : 'development',
            config: this.config
        };
    }

    // Reset configuration to defaults
    resetConfig() {
        this.config = this.initializeConfig();
        this.saveConfigToStorage();
        this.validateConfig();
    }
}

// Global configuration instance
window.configManager = new ConfigManager();

// Expose configuration helpers globally
window.getConfig = (path) => window.configManager.get(path);
window.isFeatureEnabled = (feature) => window.configManager.isFeatureEnabled(feature);
window.updateConfig = (path, value) => window.configManager.updateConfig(path, value);

// Development helpers
if (window.configManager.isDevelopment()) {
    window.configManager.config.development.enableConsoleCommands = true;
    window.debugConfig = () => console.log(window.configManager.exportConfig());
    window.resetConfig = () => window.configManager.resetConfig();
}

// Export for use in other modules
window.ConfigManager = ConfigManager;