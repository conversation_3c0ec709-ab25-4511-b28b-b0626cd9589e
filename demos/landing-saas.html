<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudSync Pro - Seamless Cloud Integration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #0f0f23 0%, #1a1a3e 50%, #0f0f23 100%); }
        .glass-effect { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px); }
        .animate-float { animation: float 6s ease-in-out infinite; }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body class="gradient-bg text-white">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-black/20 backdrop-blur-md z-50 border-b border-white/10">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg"></div>
                    <span class="text-2xl font-bold">CloudSync Pro</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-white/80 hover:text-white transition-colors">Features</a>
                    <a href="#pricing" class="text-white/80 hover:text-white transition-colors">Pricing</a>
                    <a href="#about" class="text-white/80 hover:text-white transition-colors">About</a>
                    <a href="#contact" class="text-white/80 hover:text-white transition-colors">Contact</a>
                </div>
                <button class="bg-gradient-to-r from-primary to-secondary text-white px-6 py-2 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                    Join Waitlist
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-16 px-4 min-h-screen flex items-center">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="inline-flex items-center px-4 py-2 bg-primary/20 rounded-full text-primary text-sm font-medium mb-6">
                        🚀 Now in Beta - Join 10,000+ users
                    </div>
                    <h1 class="text-5xl md:text-7xl font-bold mb-6">
                        Sync Your
                        <span class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                            Entire Workflow
                        </span>
                    </h1>
                    <p class="text-xl text-white/80 mb-8 leading-relaxed">
                        CloudSync Pro seamlessly integrates all your cloud services, automates workflows, 
                        and keeps your team synchronized across every platform. Experience the future of productivity.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <button class="bg-gradient-to-r from-primary to-secondary text-white px-8 py-4 rounded-lg font-semibold text-lg hover:opacity-90 transition-opacity">
                            Get Early Access
                        </button>
                        <button class="border-2 border-white/30 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/10 transition-colors">
                            Watch Demo
                        </button>
                    </div>
                    <div class="flex items-center gap-8 text-sm text-white/60">
                        <div class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span>Free during beta</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span>No credit card required</span>
                        </div>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="animate-float">
                        <div class="glass-effect rounded-2xl p-8 border border-white/10">
                            <div class="bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl p-6">
                                <div class="grid grid-cols-3 gap-4 mb-6">
                                    <div class="bg-white/10 rounded-lg p-3 text-center">
                                        <div class="text-2xl mb-1">☁️</div>
                                        <div class="text-xs">Google Drive</div>
                                    </div>
                                    <div class="bg-white/10 rounded-lg p-3 text-center">
                                        <div class="text-2xl mb-1">📊</div>
                                        <div class="text-xs">Slack</div>
                                    </div>
                                    <div class="bg-white/10 rounded-lg p-3 text-center">
                                        <div class="text-2xl mb-1">📧</div>
                                        <div class="text-xs">Gmail</div>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="text-primary text-lg font-semibold mb-2">⚡ Syncing...</div>
                                    <div class="w-full bg-white/10 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-primary to-secondary h-2 rounded-full w-3/4"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-16 px-4">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">Powerful Features</h2>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    Everything you need to streamline your workflow and boost productivity
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass-effect rounded-xl p-8 border border-white/10 hover:border-primary/50 transition-colors">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl">🔄</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Real-time Sync</h3>
                    <p class="text-white/80">
                        Instantly synchronize files, messages, and data across all your connected platforms.
                    </p>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10 hover:border-primary/50 transition-colors">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl">🤖</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Smart Automation</h3>
                    <p class="text-white/80">
                        AI-powered workflows that learn your patterns and automate repetitive tasks.
                    </p>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10 hover:border-primary/50 transition-colors">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl">🔒</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Enterprise Security</h3>
                    <p class="text-white/80">
                        Bank-level encryption and security protocols to keep your data safe.
                    </p>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10 hover:border-primary/50 transition-colors">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl">📊</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Analytics Dashboard</h3>
                    <p class="text-white/80">
                        Comprehensive insights into your team's productivity and workflow efficiency.
                    </p>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10 hover:border-primary/50 transition-colors">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl">🌐</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Universal Integration</h3>
                    <p class="text-white/80">
                        Connect with 500+ apps and services through our extensive integration library.
                    </p>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10 hover:border-primary/50 transition-colors">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center mb-6">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Lightning Fast</h3>
                    <p class="text-white/80">
                        Optimized performance with sub-second response times and 99.9% uptime.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Preview -->
    <section id="pricing" class="py-16 px-4 bg-black/20">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">Simple Pricing</h2>
                <p class="text-xl text-white/80">
                    Choose the plan that fits your team size and needs
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div class="glass-effect rounded-xl p-8 border border-white/10">
                    <h3 class="text-xl font-semibold mb-4">Starter</h3>
                    <div class="text-3xl font-bold mb-6">$9<span class="text-lg text-white/60">/month</span></div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Up to 5 integrations</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Basic automation</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Email support</span>
                        </li>
                    </ul>
                    <button class="w-full border border-white/30 text-white py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors">
                        Join Waitlist
                    </button>
                </div>

                <div class="glass-effect rounded-xl p-8 border-2 border-primary relative">
                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <div class="bg-gradient-to-r from-primary to-secondary px-4 py-1 rounded-full text-xs font-semibold">
                            Most Popular
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold mb-4">Professional</h3>
                    <div class="text-3xl font-bold mb-6">$29<span class="text-lg text-white/60">/month</span></div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Unlimited integrations</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Advanced automation</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Priority support</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Analytics dashboard</span>
                        </li>
                    </ul>
                    <button class="w-full bg-gradient-to-r from-primary to-secondary text-white py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                        Join Waitlist
                    </button>
                </div>

                <div class="glass-effect rounded-xl p-8 border border-white/10">
                    <h3 class="text-xl font-semibold mb-4">Enterprise</h3>
                    <div class="text-3xl font-bold mb-6">$99<span class="text-lg text-white/60">/month</span></div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Everything in Pro</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Custom integrations</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>Dedicated support</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="text-green-500">✓</span>
                            <span>SLA guarantee</span>
                        </li>
                    </ul>
                    <button class="w-full border border-white/30 text-white py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Waitlist CTA -->
    <section class="py-16 px-4">
        <div class="container mx-auto text-center">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Transform Your Workflow?</h2>
            <p class="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
                Join thousands of teams already using CloudSync Pro to streamline their operations.
            </p>
            
            <div class="max-w-md mx-auto">
                <form class="flex gap-4">
                    <input
                        type="email"
                        placeholder="Enter your email"
                        class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-primary"
                    />
                    <button
                        type="submit"
                        class="bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity"
                    >
                        Join Waitlist
                    </button>
                </form>
                <p class="text-sm text-white/60 mt-4">
                    Free during beta • No spam, unsubscribe anytime
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-8 px-4 border-t border-white/10">
        <div class="container mx-auto">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="flex items-center space-x-2 mb-4 md:mb-0">
                    <div class="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg"></div>
                    <span class="text-xl font-bold">CloudSync Pro</span>
                </div>
                <div class="text-white/60 text-sm">
                    © 2024 CloudSync Pro. All rights reserved.
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Waitlist form handling
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thanks for joining our waitlist! We\'ll be in touch soon.');
        });
    </script>
</body>
</html>
