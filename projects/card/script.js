// SmartCard.pl - Main JavaScript functionality
class SmartCardApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeAnimations();
        this.setupFormHandlers();
        this.initializeLanguage();
        this.createParticles();
        this.setupIntersectionObserver();
    }

    setupEventListeners() {
        // Card flip functionality
        const businessCard = document.getElementById('businessCardPreview');
        const flipBtn = document.getElementById('flipCard');
        
        if (businessCard) {
            businessCard.addEventListener('click', () => this.flipCard(businessCard));
        }
        
        if (flipBtn) {
            flipBtn.addEventListener('click', () => this.flipCard(businessCard));
        }

        // CTA buttons
        const startCreatingBtn = document.getElementById('startCreating');
        const watchDemoBtn = document.getElementById('watchDemo');
        
        if (startCreatingBtn) {
            startCreatingBtn.addEventListener('click', () => this.scrollToCreator());
        }
        
        if (watchDemoBtn) {
            watchDemoBtn.addEventListener('click', () => this.showDemo());
        }

        // Navigation smooth scroll
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Template selection
        document.querySelectorAll('.template-item').forEach(template => {
            template.addEventListener('click', () => this.selectTemplate(template));
        });

        // Avatar style selection
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.addEventListener('click', () => this.selectAvatarStyle(btn));
        });

        // Photo upload
        const photoUpload = document.getElementById('photoUpload');
        if (photoUpload) {
            photoUpload.addEventListener('change', (e) => this.handlePhotoUpload(e));
        }

        // Pricing buttons
        document.querySelectorAll('.pricing-btn').forEach(btn => {
            btn.addEventListener('click', () => this.handlePricingClick(btn));
        });

        // Feature demos
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', () => this.animateFeatureDemo(card));
        });
    }

    flipCard(card) {
        if (card) {
            card.classList.toggle('flipped');
            
            // Add flip sound effect (simulated with animation)
            card.style.transform = 'scale(1.02)';
            setTimeout(() => {
                card.style.transform = '';
            }, 200);
        }
    }

    scrollToCreator() {
        const creatorSection = document.getElementById('demo');
        if (creatorSection) {
            creatorSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            // Focus on first input
            setTimeout(() => {
                const firstInput = document.getElementById('fullName');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 1000);
        }
    }

    showDemo() {
        // Animate the demo card
        const demoCard = document.getElementById('businessCardPreview');
        if (demoCard) {
            demoCard.style.animation = 'none';
            setTimeout(() => {
                demoCard.style.animation = 'demoShowcase 3s ease-in-out';
            }, 100);
        }
        
        // Show scanning animation
        this.startScanningAnimation();
    }

    startScanningAnimation() {
        const phoneScreen = document.querySelector('.phone-screen');
        if (phoneScreen) {
            phoneScreen.classList.add('scanning-active');
            
            setTimeout(() => {
                phoneScreen.classList.remove('scanning-active');
                this.showScanSuccess();
            }, 3000);
        }
    }

    showScanSuccess() {
        const scanningDiv = document.querySelector('.scanning-animation');
        if (scanningDiv) {
            scanningDiv.innerHTML = `
                <div style="color: #43e97b; animation: successPulse 0.5s ease-out;">
                    <i class="fas fa-check-circle" style="font-size: 2rem;"></i>
                    <p>Kontakt zapisany!</p>
                </div>
            `;
            
            setTimeout(() => {
                scanningDiv.innerHTML = `
                    <div class="scan-line"></div>
                    <i class="fas fa-qrcode"></i>
                    <p data-translate="scanning">Skanowanie...</p>
                `;
            }, 2000);
        }
    }

    setupFormHandlers() {
        // Real-time form updates
        const formInputs = [
            'fullName', 'jobTitle', 'company', 
            'email', 'phone', 'website'
        ];
        
        formInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', () => this.updateLivePreview());
                input.addEventListener('focus', () => this.highlightPreviewSection(inputId));
                input.addEventListener('blur', () => this.removeHighlight());
            }
        });
        
        // Initialize live preview
        this.updateLivePreview();
    }

    updateLivePreview() {
        const liveCard = document.getElementById('liveCard');
        if (!liveCard) return;

        const data = {
            name: document.getElementById('fullName')?.value || 'Jan Kowalski',
            title: document.getElementById('jobTitle')?.value || 'CEO & Founder',
            company: document.getElementById('company')?.value || 'Innovation Tech',
            email: document.getElementById('email')?.value || '<EMAIL>',
            phone: document.getElementById('phone')?.value || '+48 ***********',
            website: document.getElementById('website')?.value || 'innovation.tech'
        };

        // Update card with smooth transition
        liveCard.style.opacity = '0.7';
        
        setTimeout(() => {
            liveCard.innerHTML = this.generateCardHTML(data);
            liveCard.style.opacity = '1';
            
            // Add update animation
            liveCard.style.transform = 'scale(1.02)';
            setTimeout(() => {
                liveCard.style.transform = 'scale(1)';
            }, 200);
        }, 150);
    }

    generateCardHTML(data) {
        const selectedTemplate = document.querySelector('.template-item.active')?.dataset.template || 'modern';
        
        return `
            <div class="card-content ${selectedTemplate}-template">
                <div class="card-header">
                    <div class="card-avatar-container">
                        <div class="avatar-placeholder generated-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="card-qr-container">
                        <div class="qr-code-live">
                            <div class="qr-pattern"></div>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <h3 class="card-name-live">${data.name}</h3>
                    <p class="card-title-live">${data.title}</p>
                    <p class="card-company-live">${data.company}</p>
                </div>
                
                <div class="card-footer">
                    <div class="contact-info">
                        <div class="contact-item-live">
                            <i class="fas fa-envelope"></i>
                            <span>${data.email}</span>
                        </div>
                        <div class="contact-item-live">
                            <i class="fas fa-phone"></i>
                            <span>${data.phone}</span>
                        </div>
                        <div class="contact-item-live">
                            <i class="fas fa-globe"></i>
                            <span>${data.website}</span>
                        </div>
                    </div>
                </div>
                
                <div class="card-tech-badges">
                    <div class="tech-badge nfc-badge">
                        <i class="fas fa-wifi"></i>
                        <span>NFC</span>
                    </div>
                    <div class="tech-badge qr-badge">
                        <i class="fas fa-qrcode"></i>
                        <span>QR</span>
                    </div>
                    <div class="tech-badge ai-badge">
                        <i class="fas fa-robot"></i>
                        <span>AI</span>
                    </div>
                </div>
            </div>
        `;
    }

    highlightPreviewSection(inputId) {
        const liveCard = document.getElementById('liveCard');
        if (!liveCard) return;

        // Remove previous highlights
        this.removeHighlight();

        // Add highlight based on input
        const highlightMap = {
            'fullName': '.card-name-live',
            'jobTitle': '.card-title-live',
            'company': '.card-company-live',
            'email': '.contact-item-live:nth-child(1)',
            'phone': '.contact-item-live:nth-child(2)',
            'website': '.contact-item-live:nth-child(3)'
        };

        const selector = highlightMap[inputId];
        if (selector) {
            const element = liveCard.querySelector(selector);
            if (element) {
                element.classList.add('highlight-active');
            }
        }
    }

    removeHighlight() {
        document.querySelectorAll('.highlight-active').forEach(el => {
            el.classList.remove('highlight-active');
        });
    }

    selectTemplate(templateElement) {
        // Remove active class from all templates
        document.querySelectorAll('.template-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to selected template
        templateElement.classList.add('active');
        
        // Update live preview
        this.updateLivePreview();
        
        // Animate selection
        templateElement.style.transform = 'scale(0.95)';
        setTimeout(() => {
            templateElement.style.transform = 'scale(1)';
        }, 150);
    }

    selectAvatarStyle(button) {
        // Remove active class from all style buttons
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Add active class to selected button
        button.classList.add('active');
        
        // Simulate AI avatar generation
        this.generateAIAvatar(button.dataset.style);
    }

    generateAIAvatar(style) {
        const avatarPlaceholder = document.querySelector('.avatar-placeholder');
        if (!avatarPlaceholder) return;

        // Show loading state
        avatarPlaceholder.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        avatarPlaceholder.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
        
        // Simulate AI processing
        setTimeout(() => {
            const avatarStyles = {
                professional: {
                    background: 'linear-gradient(45deg, #2196F3, #21CBF3)',
                    icon: 'fas fa-user-tie'
                },
                cartoon: {
                    background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
                    icon: 'fas fa-smile'
                },
                abstract: {
                    background: 'linear-gradient(45deg, #A8E6CF, #FFD93D)',
                    icon: 'fas fa-shapes'
                }
            };
            
            const selectedStyle = avatarStyles[style] || avatarStyles.professional;
            avatarPlaceholder.style.background = selectedStyle.background;
            avatarPlaceholder.innerHTML = `<i class="${selectedStyle.icon}"></i>`;
            
            // Success animation
            avatarPlaceholder.style.transform = 'scale(1.1)';
            setTimeout(() => {
                avatarPlaceholder.style.transform = 'scale(1)';
            }, 300);
            
        }, 2000);
    }

    handlePhotoUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showNotification('Proszę wybrać plik obrazu', 'error');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('Plik jest za duży. Maksymalny rozmiar to 5MB', 'error');
            return;
        }

        // Show upload progress
        this.showUploadProgress();

        // Simulate upload and AI processing
        const reader = new FileReader();
        reader.onload = (e) => {
            setTimeout(() => {
                this.processUploadedImage(e.target.result);
            }, 2000);
        };
        reader.readAsDataURL(file);
    }

    showUploadProgress() {
        const uploadBtn = document.querySelector('.upload-btn');
        if (!uploadBtn) return;

        const originalHTML = uploadBtn.innerHTML;
        uploadBtn.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <span>Przetwarzanie...</span>
        `;
        uploadBtn.disabled = true;

        // Reset after processing
        setTimeout(() => {
            uploadBtn.innerHTML = originalHTML;
            uploadBtn.disabled = false;
        }, 3000);
    }

    processUploadedImage(imageSrc) {
        const avatarPlaceholder = document.querySelector('.avatar-placeholder');
        if (!avatarPlaceholder) return;

        // Create avatar with uploaded image
        avatarPlaceholder.style.backgroundImage = `url(${imageSrc})`;
        avatarPlaceholder.style.backgroundSize = 'cover';
        avatarPlaceholder.style.backgroundPosition = 'center';
        avatarPlaceholder.innerHTML = '';

        this.showNotification('Avatar AI wygenerowany pomyślnie!', 'success');
    }

    handlePricingClick(button) {
        const plan = button.closest('.pricing-card');
        const planType = plan.querySelector('h3').textContent.toLowerCase();
        
        // Animate button
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 150);

        // Show pricing modal or redirect
        this.showPricingModal(planType);
    }

    showPricingModal(planType) {
        // Create modal overlay
        const modal = document.createElement('div');
        modal.className = 'pricing-modal-overlay';
        modal.innerHTML = `
            <div class="pricing-modal">
                <div class="pricing-modal-header">
                    <h3>Wybrano plan: ${planType.charAt(0).toUpperCase() + planType.slice(1)}</h3>
                    <button class="close-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="pricing-modal-body">
                    <p>Dziękujemy za zainteresowanie! Wkrótce skontaktujemy się z Tobą w sprawie szczegółów płatności.</p>
                    <div class="contact-form">
                        <input type="email" placeholder="Twój email" class="modal-email-input">
                        <button class="modal-submit-btn">Wyślij</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        modal.querySelector('.modal-submit-btn').addEventListener('click', () => {
            this.showNotification('Dziękujemy! Skontaktujemy się z Tobą wkrótce.', 'success');
            document.body.removeChild(modal);
        });
    }

    animateFeatureDemo(card) {
        const demo = card.querySelector('.feature-demo');
        if (!demo) return;

        // Add specific animations based on feature type
        const featureType = card.querySelector('h3').textContent.toLowerCase();
        
        if (featureType.includes('ai') || featureType.includes('avatar')) {
            this.animateAIDemo(demo);
        } else if (featureType.includes('3d') || featureType.includes('flip')) {
            this.animate3DDemo(demo);
        } else if (featureType.includes('qr')) {
            this.animateQRDemo(demo);
        } else if (featureType.includes('nfc')) {
            this.animateNFCDemo(demo);
        } else if (featureType.includes('analytics')) {
            this.animateAnalyticsDemo(demo);
        }
    }

    animateAIDemo(demo) {
        const transformation = demo.querySelector('.avatar-transformation');
        if (transformation) {
            transformation.style.animation = 'none';
            setTimeout(() => {
                transformation.style.animation = 'aiTransform 2s ease-in-out';
            }, 100);
        }
    }

    animate3DDemo(demo) {
        const flipDemo = demo.querySelector('.flip-demo');
        if (flipDemo) {
            flipDemo.style.animation = 'none';
            setTimeout(() => {
                flipDemo.style.animation = 'demonstrateFlip 2s ease-in-out';
            }, 100);
        }
    }

    animateQRDemo(demo) {
        const qrDemo = demo.querySelector('.qr-demo');
        if (qrDemo) {
            qrDemo.style.animation = 'none';
            setTimeout(() => {
                qrDemo.style.animation = 'qrScanDemo 2s ease-in-out';
            }, 100);
        }
    }

    animateNFCDemo(demo) {
        const nfcWaves = demo.querySelector('.nfc-waves');
        if (nfcWaves) {
            nfcWaves.style.animation = 'none';
            setTimeout(() => {
                nfcWaves.style.animation = 'nfcPulse 1s ease-out infinite';
            }, 100);
        }
    }

    animateAnalyticsDemo(demo) {
        const chartBars = demo.querySelectorAll('.chart-bar');
        chartBars.forEach((bar, index) => {
            bar.style.animation = 'none';
            setTimeout(() => {
                bar.style.animation = `chartGrow 1s ease-out ${index * 0.2}s forwards`;
            }, 100);
        });
    }

    initializeAnimations() {
        // Add CSS animations dynamically
        const style = document.createElement('style');
        style.textContent = `
            @keyframes demoShowcase {
                0% { transform: translateY(0) rotateY(0deg); }
                25% { transform: translateY(-10px) rotateY(15deg); }
                50% { transform: translateY(-5px) rotateY(180deg); }
                75% { transform: translateY(-10px) rotateY(195deg); }
                100% { transform: translateY(0) rotateY(360deg); }
            }
            
            @keyframes successPulse {
                0% { transform: scale(0.8); opacity: 0; }
                50% { transform: scale(1.1); opacity: 1; }
                100% { transform: scale(1); opacity: 1; }
            }
            
            .highlight-active {
                background: rgba(102, 126, 234, 0.2) !important;
                border-radius: 4px !important;
                padding: 2px 4px !important;
                animation: highlightPulse 1s ease-in-out !important;
            }
            
            @keyframes highlightPulse {
                0%, 100% { box-shadow: 0 0 0 rgba(102, 126, 234, 0.4); }
                50% { box-shadow: 0 0 10px rgba(102, 126, 234, 0.6); }
            }
            
            .pricing-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: modalFadeIn 0.3s ease-out;
            }
            
            @keyframes modalFadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            .pricing-modal {
                background: var(--glass-bg);
                backdrop-filter: var(--backdrop-blur);
                border: 1px solid var(--glass-border);
                border-radius: var(--border-radius-xl);
                padding: 2rem;
                max-width: 500px;
                width: 90%;
                animation: modalSlideIn 0.3s ease-out;
            }
            
            @keyframes modalSlideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            
            .pricing-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
                padding-bottom: 1rem;
                border-bottom: 1px solid var(--glass-border);
            }
            
            .close-modal {
                background: none;
                border: none;
                color: var(--dark-text-secondary);
                font-size: 1.5rem;
                cursor: pointer;
                padding: 8px;
                border-radius: 50%;
                transition: all var(--animation-fast);
            }
            
            .close-modal:hover {
                background: var(--glass-bg);
                color: var(--dark-text);
            }
            
            .contact-form {
                display: flex;
                gap: 1rem;
                margin-top: 1rem;
            }
            
            .modal-email-input {
                flex: 1;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid var(--glass-border);
                border-radius: var(--border-radius-md);
                color: var(--dark-text);
            }
            
            .modal-submit-btn {
                background: var(--primary-gradient);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: var(--border-radius-md);
                cursor: pointer;
                transition: all var(--animation-fast);
            }
            
            .modal-submit-btn:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
            }
            
            .card-content {
                width: 100%;
                height: 100%;
                padding: 1.5rem;
                border-radius: var(--border-radius-lg);
                position: relative;
                overflow: hidden;
            }
            
            .modern-template {
                background: var(--glass-bg);
                backdrop-filter: var(--backdrop-blur);
                border: 1px solid var(--glass-border);
            }
            
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 1rem;
            }
            
            .card-avatar-container .avatar-placeholder {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 1.2rem;
            }
            
            .card-qr-container {
                width: 50px;
                height: 50px;
            }
            
            .qr-code-live {
                width: 100%;
                height: 100%;
                background: var(--dark-text);
                border-radius: 4px;
                position: relative;
                overflow: hidden;
            }
            
            .qr-pattern::before,
            .qr-pattern::after {
                content: '';
                position: absolute;
                width: 8px;
                height: 8px;
                background: var(--primary-gradient);
                border-radius: 1px;
            }
            
            .qr-pattern::before {
                top: 4px;
                left: 4px;
            }
            
            .qr-pattern::after {
                bottom: 4px;
                right: 4px;
            }
            
            .card-body {
                margin-bottom: 1rem;
            }
            
            .card-name-live {
                font-size: 1.1rem;
                font-weight: 700;
                color: var(--dark-text);
                margin: 0 0 4px 0;
            }
            
            .card-title-live,
            .card-company-live {
                font-size: 0.85rem;
                color: var(--dark-text-secondary);
                margin: 0 0 2px 0;
            }
            
            .contact-info {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }
            
            .contact-item-live {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 0.75rem;
                color: var(--dark-text-secondary);
            }
            
            .contact-item-live i {
                width: 12px;
                color: var(--primary-gradient);
            }
            
            .card-tech-badges {
                position: absolute;
                bottom: 8px;
                right: 8px;
                display: flex;
                gap: 4px;
            }
            
            .tech-badge {
                background: rgba(102, 126, 234, 0.1);
                border: 1px solid rgba(102, 126, 234, 0.3);
                border-radius: 12px;
                padding: 2px 6px;
                display: flex;
                align-items: center;
                gap: 2px;
                font-size: 0.6rem;
                color: var(--primary-gradient);
            }
            
            .tech-badge i {
                font-size: 0.7rem;
            }
        `;
        document.head.appendChild(style);
    }

    createParticles() {
        // Create floating particles for background effect
        const particleContainer = document.createElement('div');
        particleContainer.className = 'particle-container';
        particleContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        `;

        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 4 + 2}px;
                height: ${Math.random() * 4 + 2}px;
                background: rgba(102, 126, 234, ${Math.random() * 0.3 + 0.1});
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: particleFloat ${Math.random() * 20 + 10}s linear infinite;
            `;
            particleContainer.appendChild(particle);
        }

        document.body.appendChild(particleContainer);

        // Add particle animation CSS
        const particleStyle = document.createElement('style');
        particleStyle.textContent = `
            @keyframes particleFloat {
                0% {
                    transform: translateY(100vh) translateX(0);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100px) translateX(${Math.random() * 200 - 100}px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(particleStyle);
    }

    setupIntersectionObserver() {
        // Animate elements when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe elements that should animate in
        document.querySelectorAll('.feature-card, .template-card, .pricing-card').forEach(el => {
            observer.observe(el);
        });

        // Add animate-in styles
        const animateStyle = document.createElement('style');
        animateStyle.textContent = `
            .feature-card,
            .template-card,
            .pricing-card {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.6s ease-out;
            }
            
            .animate-in {
                opacity: 1 !important;
                transform: translateY(0) !important;
            }
        `;
        document.head.appendChild(animateStyle);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? 'var(--success-gradient)' : type === 'error' ? 'var(--warning-gradient)' : 'var(--primary-gradient)'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-lg);
            z-index: 10001;
            animation: notificationSlideIn 0.3s ease-out;
            max-width: 300px;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'notificationSlideOut 0.3s ease-out forwards';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);

        // Add notification animations
        if (!document.querySelector('#notification-styles')) {
            const notificationStyle = document.createElement('style');
            notificationStyle.id = 'notification-styles';
            notificationStyle.textContent = `
                @keyframes notificationSlideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                
                @keyframes notificationSlideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(notificationStyle);
        }
    }

    initializeLanguage() {
        // Initialize the translation system
        if (window.initializeLanguage) {
            window.initializeLanguage();
        }
    }
}

// Advanced features for WOW factor
class AIFeatures {
    constructor() {
        this.initializeAI();
    }

    initializeAI() {
        this.setupVoiceRecording();
        this.setupARPreview();
        this.setupAnalytics();
        this.setupTeamManagement();
    }

    setupVoiceRecording() {
        // Simulate voice recording functionality
        const recordBtn = document.createElement('button');
        recordBtn.className = 'voice-record-btn';
        recordBtn.innerHTML = '<i class="fas fa-microphone"></i> Nagraj wprowadzenie (15s)';
        
        // Add to a container if it exists
        const voiceSection = document.querySelector('.voice-demo');
        if (voiceSection && voiceSection.parentElement) {
            const container = voiceSection.parentElement;
            if (!container.querySelector('.voice-record-btn')) {
                container.appendChild(recordBtn);
                
                recordBtn.addEventListener('click', () => this.startVoiceRecording());
            }
        }
    }

    startVoiceRecording() {
        const btn = document.querySelector('.voice-record-btn');
        if (!btn) return;

        btn.innerHTML = '<i class="fas fa-stop"></i> Zatrzymaj nagrywanie';
        btn.style.background = 'var(--warning-gradient)';

        // Simulate recording for 15 seconds
        let timeLeft = 15;
        const interval = setInterval(() => {
            btn.innerHTML = `<i class="fas fa-stop"></i> Nagrywanie... ${timeLeft}s`;
            timeLeft--;

            if (timeLeft < 0) {
                clearInterval(interval);
                this.finishVoiceRecording();
            }
        }, 1000);
    }

    finishVoiceRecording() {
        const btn = document.querySelector('.voice-record-btn');
        if (!btn) return;

        btn.innerHTML = '<i class="fas fa-check"></i> Nagranie zapisane!';
        btn.style.background = 'var(--success-gradient)';

        setTimeout(() => {
            btn.innerHTML = '<i class="fas fa-microphone"></i> Nagraj wprowadzenie (15s)';
            btn.style.background = 'var(--primary-gradient)';
        }, 2000);
    }

    setupARPreview() {
        // Simulate AR preview functionality
        const arBtn = document.createElement('button');
        arBtn.className = 'ar-preview-btn';
        arBtn.innerHTML = '<i class="fas fa-camera"></i> Podgląd AR';
        
        const arSection = document.querySelector('.ar-demo');
        if (arSection && arSection.parentElement) {
            const container = arSection.parentElement;
            if (!container.querySelector('.ar-preview-btn')) {
                container.appendChild(arBtn);
                
                arBtn.addEventListener('click', () => this.startARPreview());
            }
        }
    }

    startARPreview() {
        // Create AR modal
        const arModal = document.createElement('div');
        arModal.className = 'ar-modal-overlay';
        arModal.innerHTML = `
            <div class="ar-modal">
                <div class="ar-camera-view">
                    <div class="ar-viewfinder"></div>
                    <div class="ar-business-card">
                        <div class="ar-card-content">
                            <h3>Jan Kowalski</h3>
                            <p>CEO & Founder</p>
                        </div>
                    </div>
                    <div class="ar-controls">
                        <button class="ar-close-btn">
                            <i class="fas fa-times"></i> Zamknij
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(arModal);

        // Add AR styles
        const arStyle = document.createElement('style');
        arStyle.textContent = `
            .ar-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.9);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .ar-camera-view {
                width: 80%;
                height: 80%;
                background: radial-gradient(circle, rgba(0,50,0,0.3) 0%, rgba(0,0,0,0.8) 100%);
                border-radius: var(--border-radius-lg);
                position: relative;
                overflow: hidden;
                border: 2px solid var(--accent-gradient);
            }
            
            .ar-viewfinder {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 200px;
                height: 200px;
                border: 2px solid var(--accent-gradient);
                border-style: dashed;
                border-radius: 10px;
                animation: arScan 2s ease-in-out infinite;
            }
            
            @keyframes arScan {
                0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
                50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
            }
            
            .ar-business-card {
                position: absolute;
                top: 45%;
                left: 50%;
                transform: translate(-50%, -50%) rotateX(15deg) rotateY(10deg);
                width: 180px;
                height: 110px;
                background: var(--glass-bg);
                backdrop-filter: var(--backdrop-blur);
                border: 1px solid var(--glass-border);
                border-radius: 8px;
                box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
                animation: arCardFloat 3s ease-in-out infinite;
            }
            
            @keyframes arCardFloat {
                0%, 100% { transform: translate(-50%, -50%) rotateX(15deg) rotateY(10deg) translateZ(0); }
                50% { transform: translate(-50%, -50%) rotateX(20deg) rotateY(15deg) translateZ(10px); }
            }
            
            .ar-card-content {
                padding: 1rem;
                color: white;
                text-align: center;
            }
            
            .ar-card-content h3 {
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
            }
            
            .ar-card-content p {
                font-size: 0.7rem;
                opacity: 0.8;
            }
            
            .ar-controls {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
            }
            
            .ar-close-btn {
                background: var(--warning-gradient);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: var(--border-radius-full);
                cursor: pointer;
                transition: all var(--animation-fast);
            }
        `;
        document.head.appendChild(arStyle);

        // Close functionality
        arModal.querySelector('.ar-close-btn').addEventListener('click', () => {
            document.body.removeChild(arModal);
            document.head.removeChild(arStyle);
        });
    }

    setupAnalytics() {
        // Create analytics dashboard simulation
        this.analyticsData = {
            views: Math.floor(Math.random() * 1000) + 100,
            scans: Math.floor(Math.random() * 500) + 50,
            contacts: Math.floor(Math.random() * 200) + 20,
            locations: ['Warszawa', 'Kraków', 'Gdańsk', 'Wrocław', 'Poznań']
        };
    }

    setupTeamManagement() {
        // Team management functionality
        this.teamMembers = [
            { name: 'Anna Nowak', role: 'Marketing Manager', status: 'active' },
            { name: 'Piotr Wiśniewski', role: 'Sales Director', status: 'active' },
            { name: 'Maria Kowalczyk', role: 'Designer', status: 'pending' }
        ];
    }

    showAnalyticsDashboard() {
        // Create analytics modal
        const analyticsModal = document.createElement('div');
        analyticsModal.className = 'analytics-modal-overlay';
        analyticsModal.innerHTML = `
            <div class="analytics-modal">
                <div class="analytics-header">
                    <h3><i class="fas fa-chart-line"></i> Analytics Dashboard</h3>
                    <button class="close-analytics">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="analytics-content">
                    <div class="analytics-stats">
                        <div class="stat-card">
                            <div class="stat-icon">👁️</div>
                            <div class="stat-number">${this.analyticsData.views}</div>
                            <div class="stat-label">Wyświetlenia</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📱</div>
                            <div class="stat-number">${this.analyticsData.scans}</div>
                            <div class="stat-label">Skanowania QR</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">👥</div>
                            <div class="stat-number">${this.analyticsData.contacts}</div>
                            <div class="stat-label">Nowe kontakty</div>
                        </div>
                    </div>
                    <div class="analytics-chart">
                        <h4>Aktywność w ostatnim tygodniu</h4>
                        <div class="chart-container">
                            ${this.generateChart()}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(analyticsModal);

        // Close functionality
        analyticsModal.querySelector('.close-analytics').addEventListener('click', () => {
            document.body.removeChild(analyticsModal);
        });
    }

    generateChart() {
        const days = ['Pon', 'Wt', 'Śr', 'Czw', 'Pt', 'Sob', 'Nd'];
        const values = Array.from({length: 7}, () => Math.floor(Math.random() * 100) + 10);
        const maxValue = Math.max(...values);

        return days.map((day, index) => {
            const height = (values[index] / maxValue) * 100;
            return `
                <div class="chart-day">
                    <div class="chart-bar" style="height: ${height}%"></div>
                    <div class="chart-label">${day}</div>
                </div>
            `;
        }).join('');
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    const app = new SmartCardApp();
    const aiFeatures = new AIFeatures();
    
    // Add global styles for additional features
    const globalStyle = document.createElement('style');
    globalStyle.textContent = `
        .voice-record-btn,
        .ar-preview-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--border-radius-md);
            cursor: pointer;
            margin-top: 1rem;
            font-size: var(--font-size-sm);
            transition: all var(--animation-fast);
        }
        
        .voice-record-btn:hover,
        .ar-preview-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .analytics-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }
        
        .analytics-modal {
            background: var(--glass-bg);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-xl);
            padding: 2rem;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--glass-border);
        }
        
        .analytics-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-md);
            padding: 1.5rem;
            text-align: center;
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-text);
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--dark-text-secondary);
        }
        
        .chart-container {
            display: flex;
            align-items: end;
            justify-content: space-around;
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius-md);
            padding: 1rem;
        }
        
        .chart-day {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }
        
        .chart-bar {
            width: 20px;
            background: var(--primary-gradient);
            border-radius: 2px 2px 0 0;
            transition: all var(--animation-fast);
        }
        
        .chart-label {
            font-size: var(--font-size-xs);
            color: var(--dark-text-secondary);
        }
    `;
    document.head.appendChild(globalStyle);
    
    console.log('🚀 SmartCard.pl AI Business Card Generator initialized!');
    console.log('✨ Ready to create amazing business cards with AI!');
});

// Export for global access
window.SmartCardApp = SmartCardApp;
window.AIFeatures = AIFeatures;