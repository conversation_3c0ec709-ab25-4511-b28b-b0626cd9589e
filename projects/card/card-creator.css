/* Card Creator Styles for SmartCard.pl */

/* Creator Modal */
.creator-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.creator-modal-overlay.show {
    opacity: 1;
}

.creator-modal-overlay.hidden {
    display: none;
}

.creator-modal {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    width: 95vw;
    height: 95vh;
    max-width: 1400px;
    max-height: 900px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.creator-modal-overlay.show .creator-modal {
    transform: scale(1);
}

/* Creator Header */
.creator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.02);
}

.creator-title h2 {
    color: var(--dark-text);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.creator-title p {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

.creator-actions {
    display: flex;
    gap: 1rem;
}

.save-btn {
    background: var(--success-gradient);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.save-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.close-creator {
    background: transparent;
    border: 1px solid var(--glass-border);
    color: var(--dark-text-secondary);
    padding: 10px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
}

.close-creator:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
}

/* Creator Content */
.creator-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.creator-sidebar {
    width: 400px;
    background: rgba(255, 255, 255, 0.02);
    border-right: 1px solid var(--glass-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.creator-tabs {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--glass-border);
}

.creator-tab {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    text-align: left;
    font-weight: 500;
}

.creator-tab:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--dark-text);
}

.creator-tab.active {
    background: var(--primary-gradient);
    color: white;
}

.creator-tab i {
    font-size: 1.1rem;
    width: 20px;
}

/* Creator Panels */
.creator-panel {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    display: none;
}

.creator-panel.active {
    display: block;
}

.panel-header {
    margin-bottom: 2rem;
}

.panel-header h3 {
    color: var(--dark-text);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.panel-header p {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Template Selection */
.templates-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.template-option {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    gap: 1rem;
}

.template-option:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.template-option.active {
    border-color: var(--primary-gradient);
    background: rgba(102, 126, 234, 0.1);
}

.template-preview-card {
    width: 80px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.template-preview-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.preview-elements {
    display: flex;
    align-items: center;
    gap: 4px;
}

.preview-avatar {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.preview-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.preview-name,
.preview-title {
    height: 3px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 1px;
}

.preview-name {
    width: 20px;
}

.preview-title {
    width: 15px;
}

.template-info h4 {
    color: var(--dark-text);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.template-info p {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Form Styles */
.card-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-section label {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.form-section input,
.form-section textarea,
.form-section select {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text);
    font-size: var(--font-size-base);
    transition: all var(--animation-fast);
    font-family: var(--font-family);
}

.form-section input:focus,
.form-section textarea:focus,
.form-section select:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-section input.error,
.form-section textarea.error {
    border-color: #ff4757;
    box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.form-section textarea {
    resize: vertical;
    min-height: 80px;
}

.char-counter {
    text-align: right;
    font-size: var(--font-size-xs);
    color: var(--dark-text-secondary);
}

/* Avatar Generator */
.avatar-generator {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.avatar-upload-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.avatar-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 2px dashed var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all var(--animation-fast);
}

.avatar-preview:hover {
    border-color: var(--primary-gradient);
}

.avatar-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--dark-text-secondary);
    text-align: center;
}

.avatar-placeholder i {
    font-size: 2rem;
}

.avatar-placeholder span {
    font-size: var(--font-size-sm);
}

.uploaded-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--animation-fast);
}

.avatar-preview:hover .avatar-overlay {
    opacity: 1;
}

.change-avatar-btn {
    background: white;
    color: #333;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
}

.upload-avatar-btn {
    background: var(--secondary-gradient);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.upload-avatar-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.upload-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--primary-gradient);
}

.upload-progress i {
    font-size: 2rem;
}

/* Avatar Styles */
.avatar-styles h4,
.ai-options h4 {
    color: var(--dark-text);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 1rem 0;
}

.style-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.style-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 1rem;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    color: var(--dark-text-secondary);
}

.style-option:hover,
.style-option.active {
    border-color: var(--primary-gradient);
    background: rgba(102, 126, 234, 0.1);
    color: var(--dark-text);
}

.style-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    position: relative;
}

.style-preview.professional {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
}

.style-preview.cartoon {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
}

.style-preview.abstract {
    background: linear-gradient(45deg, #A8E6CF, #FFD93D);
}

.style-preview.minimal {
    background: linear-gradient(45deg, #f5f7fa, #c3cfe2);
}

.ai-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-control {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--dark-text-secondary);
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.ai-control input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-gradient);
}

.generate-avatar-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 600;
    margin-top: 1rem;
}

.generate-avatar-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.generate-avatar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Customization Options */
.customization-options {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.option-group h4 {
    color: var(--dark-text);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 1rem 0;
}

/* Color Palette */
.color-palette {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.color-preset {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 1rem;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
}

.color-preset:hover,
.color-preset.active {
    border-color: var(--primary-gradient);
    background: rgba(102, 126, 234, 0.1);
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.color-preset span {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
}

.custom-colors {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.custom-colors label {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: 4px;
}

.custom-colors input[type="color"] {
    width: 100%;
    height: 40px;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    background: transparent;
    cursor: pointer;
}

/* Font and Layout Options */
.font-selector,
.animation-selector,
.size-selector,
.qr-type-selector {
    width: 100%;
}

.layout-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.layout-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 1rem;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    color: var(--dark-text-secondary);
}

.layout-option:hover,
.layout-option.active {
    border-color: var(--primary-gradient);
    background: rgba(102, 126, 234, 0.1);
    color: var(--dark-text);
}

.layout-preview {
    width: 40px;
    height: 24px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 4px;
    position: relative;
}

.centered-preview::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-gradient);
}

.split-preview::before,
.split-preview::after {
    content: '';
    position: absolute;
    top: 2px;
    bottom: 2px;
    width: 6px;
    background: var(--primary-gradient);
}

.split-preview::before {
    left: 2px;
}

.split-preview::after {
    right: 2px;
}

.minimal-preview::before {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 2px;
    right: 2px;
    height: 2px;
    background: var(--primary-gradient);
}

/* Toggle Controls */
.toggle-control {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--dark-text-secondary);
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.toggle-slider {
    width: 44px;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    position: relative;
    transition: all var(--animation-fast);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all var(--animation-fast);
}

.toggle-control input[type="checkbox"] {
    display: none;
}

.toggle-control input[type="checkbox"]:checked + .toggle-slider {
    background: var(--primary-gradient);
}

.toggle-control input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.animation-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Export Options */
.export-options {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.export-formats h4,
.export-sizes h4,
.qr-options h4,
.sharing-options h4 {
    color: var(--dark-text);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 1rem 0;
}

.format-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.format-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 1.5rem 1rem;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    color: var(--dark-text-secondary);
    text-align: center;
}

.format-btn:hover,
.format-btn.active {
    border-color: var(--primary-gradient);
    background: rgba(102, 126, 234, 0.1);
    color: var(--dark-text);
}

.format-btn i {
    font-size: 1.5rem;
    color: var(--primary-gradient);
}

.format-btn span {
    font-weight: 600;
    font-size: var(--font-size-base);
}

.format-btn small {
    font-size: var(--font-size-xs);
    opacity: 0.7;
}

.custom-size {
    margin-top: 1rem;
}

.size-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.size-inputs input {
    flex: 1;
    min-width: 0;
}

.size-inputs span {
    color: var(--dark-text-secondary);
    font-weight: bold;
}

.size-inputs select {
    width: 60px;
}

/* QR Options */
.qr-content {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.qr-content.hidden {
    display: none;
}

/* Sharing Options */
.sharing-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    color: var(--dark-text-secondary);
    font-weight: 500;
}

.share-btn:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
    transform: translateY(-1px);
}

.share-btn[data-platform="linkedin"]:hover {
    border-color: #0077b5;
    color: #0077b5;
}

.share-btn[data-platform="twitter"]:hover {
    border-color: #1da1f2;
    color: #1da1f2;
}

.share-btn[data-platform="facebook"]:hover {
    border-color: #4267B2;
    color: #4267B2;
}

.share-btn[data-platform="email"]:hover {
    border-color: #ea4335;
    color: #ea4335;
}

.public-link {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.public-link label {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
}

.link-input-group {
    display: flex;
    gap: 8px;
}

.link-input-group input {
    flex: 1;
    min-width: 0;
}

.copy-link-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--dark-text-secondary);
    padding: 12px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
}

.copy-link-btn:hover:not(:disabled) {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.copy-link-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Export Actions */
.export-actions {
    display: flex;
    gap: 1rem;
}

.export-btn {
    flex: 1;
    padding: 14px 24px;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 600;
}

.export-btn.primary {
    background: var(--primary-gradient);
    color: white;
}

.export-btn.secondary {
    background: transparent;
    border: 1px solid var(--glass-border);
    color: var(--dark-text-secondary);
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-btn.secondary:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
}

/* Live Preview */
.creator-preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.01);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--glass-border);
}

.preview-header h3 {
    color: var(--dark-text);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.preview-controls {
    display: flex;
    gap: 8px;
}

.preview-control {
    width: 36px;
    height: 36px;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-control:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
}

.preview-control.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

/* Preview Container */
.preview-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.preview-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--dark-bg);
    z-index: 10001;
}

.preview-container.mobile-view .business-card-live {
    max-width: 300px;
    transform: scale(0.8);
}

/* Business Card Live */
.business-card-live {
    width: 400px;
    height: 250px;
    perspective: 1000px;
    position: relative;
    cursor: pointer;
}

.card-front-live,
.card-back-live {
    width: 100%;
    height: 100%;
    position: absolute;
    backface-visibility: hidden;
    border-radius: var(--border-radius-lg);
    transition: transform 0.6s;
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.card-back-live {
    transform: rotateY(180deg);
}

.business-card-live.flipped .card-front-live {
    transform: rotateY(-180deg);
}

.business-card-live.flipped .card-back-live {
    transform: rotateY(0deg);
}

/* Card Layout Styles */
.card-layout {
    width: 100%;
    height: 100%;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
}

.card-layout.back {
    padding: 1rem;
}

.card-avatar-section {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.card-avatar-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.card-avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
}

.card-info-section {
    flex: 1;
}

.card-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
}

.card-title {
    font-size: 1rem;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.card-company {
    font-size: 1rem;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.card-description {
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
}

.card-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    opacity: 0.1;
}

.decoration-element {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(30%, -30%);
}

/* Card Back Styles */
.card-contacts {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.875rem;
}

.contact-item i {
    width: 16px;
    text-align: center;
}

.card-qr-section {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.qr-code-placeholder {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-sm);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.qr-code-placeholder i {
    font-size: 1.2rem;
}

.card-social {
    display: flex;
    justify-content: center;
}

.social-icons {
    display: flex;
    gap: 12px;
}

.social-icons i {
    font-size: 1.2rem;
}

/* Template Animations */
.business-card-live.animations-enabled.animation-float {
    animation: cardFloat 3s ease-in-out infinite;
}

.business-card-live.animations-enabled.animation-glow {
    animation: cardGlow 2s ease-in-out infinite alternate;
}

.business-card-live.animations-enabled.animation-pulse {
    animation: cardPulse 2s ease-in-out infinite;
}

.business-card-live.animations-enabled.animation-rotate {
    animation: cardRotate 10s linear infinite;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes cardGlow {
    from { box-shadow: var(--shadow-2xl); }
    to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.5), var(--shadow-2xl); }
}

@keyframes cardPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes cardRotate {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(360deg); }
}

/* Preview Info */
.preview-info {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.02);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
}

.info-value {
    color: var(--dark-text);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* Auto-save Notification */
.autosave-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    z-index: 10002;
    max-width: 350px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.autosave-notification.show {
    transform: translateX(0);
}

.autosave-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.autosave-content i {
    color: var(--warning-gradient);
    font-size: 1.2rem;
}

.autosave-actions {
    display: flex;
    gap: 8px;
}

.btn-restore,
.btn-discard {
    flex: 1;
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--animation-fast);
}

.btn-restore {
    background: var(--success-gradient);
    color: white;
}

.btn-discard {
    background: transparent;
    border: 1px solid var(--glass-border);
    color: var(--dark-text-secondary);
}

.btn-restore:hover,
.btn-discard:hover {
    transform: translateY(-1px);
}

.btn-discard:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
}

/* Preview Modal */
.preview-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 10003;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-modal {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

.preview-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.preview-modal-header h3 {
    color: var(--dark-text);
    margin: 0;
}

.close-preview {
    background: transparent;
    border: 1px solid var(--glass-border);
    color: var(--dark-text-secondary);
    padding: 8px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
}

.close-preview:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
}

.preview-card-container {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.preview-modal-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .creator-modal {
        width: 98vw;
        height: 98vh;
    }
    
    .creator-sidebar {
        width: 350px;
    }
    
    .business-card-live {
        width: 350px;
        height: 220px;
    }
}

@media (max-width: 900px) {
    .creator-content {
        flex-direction: column;
    }
    
    .creator-sidebar {
        width: 100%;
        height: 300px;
        border-right: none;
        border-bottom: 1px solid var(--glass-border);
    }
    
    .creator-tabs {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .creator-tab {
        flex-shrink: 0;
        min-width: 120px;
    }
    
    .creator-panel {
        height: 200px;
        overflow-y: auto;
    }
    
    .creator-preview {
        min-height: 400px;
    }
    
    .business-card-live {
        width: 300px;
        height: 190px;
    }
    
    .format-buttons,
    .style-options {
        grid-template-columns: 1fr;
    }
    
    .layout-options {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 600px) {
    .creator-modal {
        padding: 0;
        border-radius: 0;
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
    }
    
    .creator-header {
        padding: 1rem;
    }
    
    .creator-title h2 {
        font-size: var(--font-size-lg);
    }
    
    .creator-actions {
        gap: 0.5rem;
    }
    
    .save-btn {
        padding: 8px 16px;
        font-size: var(--font-size-sm);
    }
    
    .business-card-live {
        width: 250px;
        height: 160px;
    }
    
    .templates-grid {
        gap: 0.5rem;
    }
    
    .template-option {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .template-preview-card {
        width: 60px;
        height: 40px;
    }
}

/* Hidden class utility */
.hidden {
    display: none !important;
}