<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmileCare Practice Manager - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0ea5e9',
                        secondary: '#06b6d4',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="flex h-screen">
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6 border-b">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                        <span class="text-white text-xl">🦷</span>
                    </div>
                    <div>
                        <div class="font-bold text-gray-900">SmileCare</div>
                        <div class="text-sm text-gray-500">Practice Manager</div>
                    </div>
                </div>
            </div>
            
            <nav class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 bg-primary text-white rounded-lg">
                            <span>📊</span>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <span>👥</span>
                            <span>Patients</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <span>📅</span>
                            <span>Appointments</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <span>🦷</span>
                            <span>Treatments</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <span>💰</span>
                            <span>Billing</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <span>📈</span>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                            <span>⚙️</span>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
                        <p class="text-gray-600">Welcome back, Dr. Smith</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            + New Appointment
                        </button>
                        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600">👨‍⚕️</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Today's Appointments</p>
                                <p class="text-3xl font-bold text-gray-900">12</p>
                            </div>
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                                <span class="text-primary text-xl">📅</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-success text-sm">+2 from yesterday</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Total Patients</p>
                                <p class="text-3xl font-bold text-gray-900">1,247</p>
                            </div>
                            <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center">
                                <span class="text-secondary text-xl">👥</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-success text-sm">+15 this month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Monthly Revenue</p>
                                <p class="text-3xl font-bold text-gray-900">$24,580</p>
                            </div>
                            <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center">
                                <span class="text-success text-xl">💰</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-success text-sm">+8.2% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Pending Bills</p>
                                <p class="text-3xl font-bold text-gray-900">$3,240</p>
                            </div>
                            <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center">
                                <span class="text-warning text-xl">📋</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-warning text-sm">5 overdue invoices</span>
                        </div>
                    </div>
                </div>

                <!-- Today's Schedule & Recent Patients -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Today's Schedule -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b">
                            <h2 class="text-lg font-semibold text-gray-900">Today's Schedule</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-semibold">
                                            JD
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">John Doe</p>
                                            <p class="text-sm text-gray-600">Cleaning & Checkup</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900">9:00 AM</p>
                                        <span class="inline-block px-2 py-1 bg-success text-white text-xs rounded">Confirmed</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center text-white font-semibold">
                                            SM
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Sarah Miller</p>
                                            <p class="text-sm text-gray-600">Root Canal</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900">10:30 AM</p>
                                        <span class="inline-block px-2 py-1 bg-warning text-white text-xs rounded">Pending</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            RJ
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Robert Johnson</p>
                                            <p class="text-sm text-gray-600">Filling</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900">2:00 PM</p>
                                        <span class="inline-block px-2 py-1 bg-success text-white text-xs rounded">Confirmed</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            EB
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Emily Brown</p>
                                            <p class="text-sm text-gray-600">Consultation</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-gray-900">3:30 PM</p>
                                        <span class="inline-block px-2 py-1 bg-success text-white text-xs rounded">Confirmed</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Patients -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b">
                            <h2 class="text-lg font-semibold text-gray-900">Recent Patients</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            MW
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Michael Wilson</p>
                                            <p class="text-sm text-gray-600">Last visit: 2 days ago</p>
                                        </div>
                                    </div>
                                    <button class="text-primary hover:text-blue-700">View</button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            LD
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Lisa Davis</p>
                                            <p class="text-sm text-gray-600">Last visit: 1 week ago</p>
                                        </div>
                                    </div>
                                    <button class="text-primary hover:text-blue-700">View</button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            DT
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">David Thompson</p>
                                            <p class="text-sm text-gray-600">Last visit: 2 weeks ago</p>
                                        </div>
                                    </div>
                                    <button class="text-primary hover:text-blue-700">View</button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            JW
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Jennifer White</p>
                                            <p class="text-sm text-gray-600">Last visit: 3 weeks ago</p>
                                        </div>
                                    </div>
                                    <button class="text-primary hover:text-blue-700">View</button>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-semibold">
                                            CL
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">Christopher Lee</p>
                                            <p class="text-sm text-gray-600">Last visit: 1 month ago</p>
                                        </div>
                                    </div>
                                    <button class="text-primary hover:text-blue-700">View</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 border-b">
                        <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <button class="flex flex-col items-center p-4 bg-primary/10 rounded-lg hover:bg-primary/20 transition-colors">
                                <span class="text-2xl mb-2">👥</span>
                                <span class="text-sm font-medium text-gray-900">Add Patient</span>
                            </button>
                            <button class="flex flex-col items-center p-4 bg-secondary/10 rounded-lg hover:bg-secondary/20 transition-colors">
                                <span class="text-2xl mb-2">📅</span>
                                <span class="text-sm font-medium text-gray-900">Schedule Appointment</span>
                            </button>
                            <button class="flex flex-col items-center p-4 bg-success/10 rounded-lg hover:bg-success/20 transition-colors">
                                <span class="text-2xl mb-2">💰</span>
                                <span class="text-sm font-medium text-gray-900">Process Payment</span>
                            </button>
                            <button class="flex flex-col items-center p-4 bg-warning/10 rounded-lg hover:bg-warning/20 transition-colors">
                                <span class="text-2xl mb-2">📋</span>
                                <span class="text-sm font-medium text-gray-900">Treatment Plan</span>
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Simulate real-time updates
        function updateStats() {
            const appointments = document.querySelector('[data-stat="appointments"]');
            if (appointments) {
                const current = parseInt(appointments.textContent);
                appointments.textContent = current + Math.floor(Math.random() * 2);
            }
        }

        // Update stats every 30 seconds
        setInterval(updateStats, 30000);

        // Add click handlers for navigation
        document.querySelectorAll('nav a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('nav a').forEach(l => {
                    l.classList.remove('bg-primary', 'text-white');
                    l.classList.add('text-gray-700', 'hover:bg-gray-100');
                });
                
                // Add active class to clicked link
                this.classList.add('bg-primary', 'text-white');
                this.classList.remove('text-gray-700', 'hover:bg-gray-100');
            });
        });

        // Quick action handlers
        document.querySelectorAll('[data-action]').forEach(button => {
            button.addEventListener('click', function() {
                const action = this.dataset.action;
                alert(`${action} functionality would be implemented here.`);
            });
        });
    </script>
</body>
</html>
