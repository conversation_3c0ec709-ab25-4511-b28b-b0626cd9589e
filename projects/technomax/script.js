class TechnoMaxStore {
    constructor() {
        this.cart = JSON.parse(localStorage.getItem('technomax-cart')) || [];
        this.products = {
            'iphone-15': { name: 'iPhone 15 Pro', price: 5299, image: 'https://images.unsplash.com/photo-1592899677977-9c10ca588bbd?w=300&h=300&fit=crop' },
            'airpods-pro': { name: 'AirPods Pro 2', price: 1199, image: 'https://images.unsplash.com/photo-1588423771073-b8903fbb85b5?w=300&h=300&fit=crop' },
            'samsung-s24': { name: 'Samsung Galaxy S24', price: 4799, image: 'https://images.unsplash.com/photo-1610945415295-d9bbf067e59c?w=300&h=300&fit=crop' },
            'macbook-air': { name: 'MacBook Air M3', price: 6499, image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300&h=300&fit=crop' },
            'apple-watch': { name: 'Apple Watch Series 9', price: 1899, image: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=300&h=300&fit=crop' },
            'sony-headphones': { name: 'Sony WH-1000XM5', price: 1599, image: 'https://images.unsplash.com/photo-1583394838336-acd977d04bf1?w=300&h=300&fit=crop' }
        };
        
        this.init();
    }

    init() {
        this.updateCartCount();
        this.bindEvents();
        this.initSearch();
    }

    bindEvents() {
        // Cart button toggle
        document.getElementById('cartBtn').addEventListener('click', () => {
            this.toggleCart();
        });

        // Close cart
        document.getElementById('closeCart').addEventListener('click', () => {
            this.closeCart();
        });

        // Close cart when clicking outside
        document.getElementById('cartModal').addEventListener('click', (e) => {
            if (e.target.id === 'cartModal') {
                this.closeCart();
            }
        });

        // Add to cart buttons
        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const productId = e.target.dataset.product;
                const price = parseInt(e.target.dataset.price);
                this.addToCart(productId, price);
                this.showAddedToCartFeedback(e.target);
            });
        });

        // Newsletter form
        document.getElementById('newsletterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleNewsletterSignup();
        });

        // Search functionality
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // Checkout button
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('checkout-btn')) {
                this.handleCheckout();
            }
        });
    }

    addToCart(productId, price) {
        const product = this.products[productId];
        if (!product) return;

        const existingItem = this.cart.find(item => item.id === productId);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                id: productId,
                name: product.name,
                price: price,
                quantity: 1,
                image: product.image
            });
        }

        this.saveCart();
        this.updateCartCount();
        this.updateCartDisplay();
    }

    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.saveCart();
        this.updateCartCount();
        this.updateCartDisplay();
    }

    updateCartCount() {
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        document.getElementById('cartCount').textContent = totalItems;
    }

    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        const cartTotal = document.getElementById('cartTotal');

        if (this.cart.length === 0) {
            cartItems.innerHTML = '<p class="empty-cart">Koszyk jest pusty</p>';
            cartTotal.textContent = '0 zł';
            return;
        }

        let total = 0;
        cartItems.innerHTML = this.cart.map(item => {
            total += item.price * item.quantity;
            return `
                <div class="cart-item">
                    <div class="item-info">
                        <h4>${item.name}</h4>
                        <p class="item-price">${item.price} zł x ${item.quantity}</p>
                    </div>
                    <button class="remove-item" onclick="store.removeFromCart('${item.id}')">
                        Usuń
                    </button>
                </div>
            `;
        }).join('');

        cartTotal.textContent = `${total.toLocaleString('pl-PL')} zł`;
    }

    toggleCart() {
        const modal = document.getElementById('cartModal');
        modal.classList.toggle('active');
        if (modal.classList.contains('active')) {
            this.updateCartDisplay();
        }
    }

    closeCart() {
        document.getElementById('cartModal').classList.remove('active');
    }

    saveCart() {
        localStorage.setItem('technomax-cart', JSON.stringify(this.cart));
    }

    showAddedToCartFeedback(button) {
        const originalText = button.textContent;
        button.textContent = 'Dodano do koszyka!';
        button.style.background = '#10b981';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '';
        }, 1500);
    }

    initSearch() {
        const searchInput = document.getElementById('searchInput');
        let searchTimeout;

        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.liveSearch(e.target.value);
            }, 300);
        });
    }

    liveSearch(query) {
        if (!query.trim()) {
            this.resetProductsDisplay();
            return;
        }

        const products = document.querySelectorAll('.product-card');
        let hasResults = false;

        products.forEach(product => {
            const productName = product.querySelector('h3').textContent.toLowerCase();
            const matchesQuery = productName.includes(query.toLowerCase());
            
            if (matchesQuery) {
                product.style.display = 'block';
                product.style.order = '0';
                hasResults = true;
            } else {
                product.style.display = 'none';
            }
        });

        if (!hasResults) {
            this.showNoResults();
        } else {
            this.hideNoResults();
        }
    }

    performSearch() {
        const query = document.getElementById('searchInput').value;
        if (query.trim()) {
            this.liveSearch(query);
            document.querySelector('.products').scrollIntoView({ behavior: 'smooth' });
        }
    }

    resetProductsDisplay() {
        document.querySelectorAll('.product-card').forEach(product => {
            product.style.display = 'block';
        });
        this.hideNoResults();
    }

    showNoResults() {
        let noResults = document.querySelector('.no-results');
        if (!noResults) {
            noResults = document.createElement('div');
            noResults.className = 'no-results';
            noResults.innerHTML = `
                <div style="text-align: center; padding: 3rem; color: #64748b;">
                    <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <h3>Nie znaleziono produktów</h3>
                    <p>Spróbuj użyć innych słów kluczowych</p>
                </div>
            `;
            document.querySelector('.products-grid').appendChild(noResults);
        }
        noResults.style.display = 'block';
    }

    hideNoResults() {
        const noResults = document.querySelector('.no-results');
        if (noResults) {
            noResults.style.display = 'none';
        }
    }

    handleNewsletterSignup() {
        const email = document.getElementById('emailInput').value;
        const button = document.querySelector('.newsletter-form button');
        const originalText = button.textContent;

        // Simple email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.showNotification('Wprowadź prawidłowy adres email', 'error');
            return;
        }

        // Simulate API call
        button.textContent = 'Zapisywanie...';
        button.disabled = true;

        setTimeout(() => {
            // Reset form
            document.getElementById('emailInput').value = '';
            button.textContent = 'Zapisano! Sprawdź email';
            button.style.background = '#10b981';
            
            // Show success notification
            this.showNotification('Dziękujemy za zapisanie się! Sprawdź swoją skrzynkę email po kod zniżkowy.', 'success');

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '';
                button.disabled = false;
            }, 3000);
        }, 1500);
    }

    handleCheckout() {
        if (this.cart.length === 0) {
            this.showNotification('Koszyk jest pusty', 'error');
            return;
        }

        const total = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        // Simulate checkout process
        this.showNotification('Przekierowywanie do płatności...', 'info');
        
        setTimeout(() => {
            this.showNotification(`Dziękujemy za zamówienie na kwotę ${total.toLocaleString('pl-PL')} zł! Dostawa w ciągu 24h.`, 'success');
            
            // Clear cart after successful order
            setTimeout(() => {
                this.cart = [];
                this.saveCart();
                this.updateCartCount();
                this.updateCartDisplay();
                this.closeCart();
            }, 2000);
        }, 1500);
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelector('.notification');
        if (existing) {
            existing.remove();
        }

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1001;
            background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Close button functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Add notification animation styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .notification-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }

    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        line-height: 1;
    }

    .notification-close:hover {
        opacity: 0.8;
    }
`;
document.head.appendChild(notificationStyles);

// Initialize store when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.store = new TechnoMaxStore();
});

// Add some smooth scrolling for CTA buttons
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('cta-btn') && e.target.textContent.includes('Zamów')) {
        e.preventDefault();
        document.querySelector('.products').scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
});

// Add intersection observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -100px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for scroll animations
document.addEventListener('DOMContentLoaded', () => {
    const animateElements = document.querySelectorAll('.feature-card, .product-card, .testimonial-card');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
});