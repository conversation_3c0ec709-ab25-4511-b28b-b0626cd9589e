// DIGITAL VELOCITY - 3D Projects Carousel

class ProjectsCarousel {
    constructor(container, projects) {
        this.container = container;
        this.projects = projects || [];
        this.currentIndex = 0;
        this.isAnimating = false;
        this.autoplayInterval = null;
        this.touchStartX = 0;
        this.touchEndX = 0;
        
        this.config = {
            autoplay: true,
            autoplayDelay: 5000,
            animationDuration: 800,
            perspective: 1000,
            rotateY: 45,
            translateZ: -200
        };
        
        this.init();
    }
    
    init() {
        this.createCarouselStructure();
        this.renderProjects();
        this.bindEvents();
        this.startAutoplay();
        this.updateCarousel();
    }
    
    createCarouselStructure() {
        this.container.innerHTML = `
            <div class="carousel-wrapper">
                <div class="carousel-track" id="carouselTrack">
                    <!-- Projects will be inserted here -->
                </div>
                <div class="carousel-controls">
                    <button class="carousel-btn prev-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="carousel-btn next-btn" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="carousel-indicators" id="carouselIndicators">
                    <!-- Indicators will be generated -->
                </div>
                <div class="carousel-info" id="carouselInfo">
                    <div class="project-counter">
                        <span class="current-number">1</span>
                        <span class="separator">/</span>
                        <span class="total-number">${this.projects.length}</span>
                    </div>
                    <div class="project-category"></div>
                </div>
            </div>
        `;
        
        this.track = this.container.querySelector('#carouselTrack');
        this.prevBtn = this.container.querySelector('#prevBtn');
        this.nextBtn = this.container.querySelector('#nextBtn');
        this.indicators = this.container.querySelector('#carouselIndicators');
        this.info = this.container.querySelector('#carouselInfo');
    }
    
    renderProjects() {
        // Clear existing content
        this.track.innerHTML = '';
        this.indicators.innerHTML = '';
        
        // Create project cards
        this.projects.forEach((project, index) => {
            const projectCard = this.createProjectCard(project, index);
            this.track.appendChild(projectCard);
            
            // Create indicator
            const indicator = document.createElement('div');
            indicator.className = `carousel-indicator ${index === 0 ? 'active' : ''}`;
            indicator.addEventListener('click', () => this.goToSlide(index));
            this.indicators.appendChild(indicator);
        });
        
        // Update total number
        this.container.querySelector('.total-number').textContent = this.projects.length;
    }
    
    createProjectCard(project, index) {
        const card = document.createElement('div');
        card.className = 'project-carousel-card';
        card.setAttribute('data-index', index);
        
        card.innerHTML = `
            <div class="card-inner">
                <div class="card-front">
                    <div class="project-image">
                        <div class="image-placeholder">
                            <i class="fas fa-${this.getProjectIcon(project.category)}"></i>
                        </div>
                        <div class="project-overlay">
                            <div class="overlay-content">
                                <button class="preview-btn" data-url="${project.demoUrl}">
                                    <i class="fas fa-eye"></i>
                                    <span>Podgląd</span>
                                </button>
                                <button class="demo-btn" data-url="${project.demoUrl}">
                                    <i class="fas fa-external-link-alt"></i>
                                    <span>Demo</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <div class="project-header">
                            <h3 class="project-title">${project.title}</h3>
                            <div class="project-category-badge">${this.getCategoryName(project.category)}</div>
                        </div>
                        <p class="project-description">${project.description}</p>
                        <div class="project-technologies">
                            ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                        </div>
                        <div class="project-features">
                            ${project.features.map(feature => `
                                <div class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span>${feature}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="card-back">
                    <div class="card-back-content">
                        <h4>Szczegóły Techniczne</h4>
                        <div class="tech-details">
                            <div class="tech-stack">
                                <h5>Stack Technologiczny:</h5>
                                <ul>
                                    ${project.technologies.map(tech => `<li>${tech}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="project-stats">
                                <div class="stat">
                                    <span class="stat-label">Czas realizacji:</span>
                                    <span class="stat-value">${this.getRandomDays()} dni</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">Kompleksowość:</span>
                                    <span class="stat-value">${this.getComplexityLevel(project.category)}</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">Responsywność:</span>
                                    <span class="stat-value">100%</span>
                                </div>
                            </div>
                        </div>
                        <button class="launch-project-btn" data-url="${project.demoUrl}">
                            <i class="fas fa-rocket"></i>
                            <span>Uruchom Projekt</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add event listeners
        const previewBtn = card.querySelector('.preview-btn');
        const demoBtn = card.querySelector('.demo-btn');
        const launchBtn = card.querySelector('.launch-project-btn');
        
        [previewBtn, demoBtn, launchBtn].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.openProject(btn.getAttribute('data-url'));
                });
            }
        });
        
        // Card flip on click
        card.addEventListener('click', () => {
            card.classList.toggle('flipped');
        });
        
        return card;
    }
    
    bindEvents() {
        // Navigation buttons
        this.prevBtn.addEventListener('click', () => this.previousSlide());
        this.nextBtn.addEventListener('click', () => this.nextSlide());
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.previousSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
        });
        
        // Touch events
        this.container.addEventListener('touchstart', (e) => {
            this.touchStartX = e.changedTouches[0].screenX;
        });
        
        this.container.addEventListener('touchend', (e) => {
            this.touchEndX = e.changedTouches[0].screenX;
            this.handleSwipe();
        });
        
        // Mouse wheel
        this.container.addEventListener('wheel', (e) => {
            e.preventDefault();
            if (e.deltaY > 0) {
                this.nextSlide();
            } else {
                this.previousSlide();
            }
        });
        
        // Pause autoplay on hover
        this.container.addEventListener('mouseenter', () => this.pauseAutoplay());
        this.container.addEventListener('mouseleave', () => this.startAutoplay());
    }
    
    handleSwipe() {
        const swipeThreshold = 50;
        const diff = this.touchStartX - this.touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                this.nextSlide();
            } else {
                this.previousSlide();
            }
        }
    }
    
    nextSlide() {
        if (this.isAnimating) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.projects.length;
        this.updateCarousel();
    }
    
    previousSlide() {
        if (this.isAnimating) return;
        
        this.currentIndex = this.currentIndex === 0 ? this.projects.length - 1 : this.currentIndex - 1;
        this.updateCarousel();
    }
    
    goToSlide(index) {
        if (this.isAnimating || index === this.currentIndex) return;
        
        this.currentIndex = index;
        this.updateCarousel();
    }
    
    updateCarousel() {
        this.isAnimating = true;
        
        const cards = this.track.querySelectorAll('.project-carousel-card');
        const indicators = this.indicators.querySelectorAll('.carousel-indicator');
        
        cards.forEach((card, index) => {
            const offset = index - this.currentIndex;
            const isActive = index === this.currentIndex;
            
            // Reset flip state
            card.classList.remove('flipped');
            
            // Apply 3D transforms
            let transform = '';
            let zIndex = 0;
            let opacity = 0.4;
            
            if (isActive) {
                transform = 'translateX(0) translateZ(0) rotateY(0deg) scale(1)';
                zIndex = 10;
                opacity = 1;
            } else if (offset === 1 || (offset === -(cards.length - 1))) {
                transform = `translateX(300px) translateZ(-200px) rotateY(-45deg) scale(0.8)`;
                zIndex = 5;
                opacity = 0.6;
            } else if (offset === -1 || (offset === cards.length - 1)) {
                transform = `translateX(-300px) translateZ(-200px) rotateY(45deg) scale(0.8)`;
                zIndex = 5;
                opacity = 0.6;
            } else {
                transform = `translateX(${offset * 400}px) translateZ(-400px) rotateY(${offset * 45}deg) scale(0.6)`;
                zIndex = 1;
                opacity = 0.2;
            }
            
            card.style.transform = transform;
            card.style.zIndex = zIndex;
            card.style.opacity = opacity;
            
            // Update active class
            card.classList.toggle('active', isActive);
        });
        
        // Update indicators
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentIndex);
        });
        
        // Update info
        this.updateCarouselInfo();
        
        // Reset animation flag
        setTimeout(() => {
            this.isAnimating = false;
        }, this.config.animationDuration);
    }
    
    updateCarouselInfo() {
        const currentProject = this.projects[this.currentIndex];
        const currentNumber = this.container.querySelector('.current-number');
        const categoryElement = this.container.querySelector('.project-category');
        
        currentNumber.textContent = this.currentIndex + 1;
        categoryElement.textContent = this.getCategoryName(currentProject.category);
    }
    
    startAutoplay() {
        if (!this.config.autoplay) return;
        
        this.pauseAutoplay();
        this.autoplayInterval = setInterval(() => {
            this.nextSlide();
        }, this.config.autoplayDelay);
    }
    
    pauseAutoplay() {
        if (this.autoplayInterval) {
            clearInterval(this.autoplayInterval);
            this.autoplayInterval = null;
        }
    }
    
    openProject(url) {
        // Add glitch effect
        createGlitchEffect(this.container);
        
        // Open project in new tab
        setTimeout(() => {
            window.open(url, '_blank');
        }, 200);
    }
    
    // Utility methods
    getProjectIcon(category) {
        const icons = {
            'ai': 'robot',
            'web': 'globe',
            'mobile': 'mobile-alt',
            'dashboard': 'chart-bar'
        };
        return icons[category] || 'code';
    }
    
    getCategoryName(category) {
        const names = {
            'ai': 'AI & Automation',
            'web': 'Web Development',
            'mobile': 'Mobile Apps',
            'dashboard': 'Dashboards'
        };
        return names[category] || 'Development';
    }
    
    getRandomDays() {
        return Math.floor(Math.random() * 30) + 7;
    }
    
    getComplexityLevel(category) {
        const levels = {
            'ai': 'Zaawansowany',
            'dashboard': 'Wysoki',
            'web': 'Średni',
            'mobile': 'Wysoki'
        };
        return levels[category] || 'Średni';
    }
    
    destroy() {
        this.pauseAutoplay();
        // Remove event listeners and clean up
    }
}

// Render Projects Carousel
function renderProjectsCarousel(projects = null) {
    const carouselContainer = document.getElementById('projectsCarousel');
    if (!carouselContainer) {
        console.error('❌ Carousel container not found');
        return;
    }

    // Use provided projects or global projectsData
    const projectsToRender = projects || window.projectsData || [];

    console.log('🎮 Rendering carousel with', projectsToRender.length, 'projects');

    if (projectsToRender.length === 0) {
        carouselContainer.innerHTML = `
            <div class="no-projects">
                <i class="fas fa-code"></i>
                <h3>Projekty ładują się...</h3>
                <p>Przygotowujemy niesamowite portfolio dla Ciebie</p>
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        // Retry after a delay
        setTimeout(() => {
            if (window.projectsData && window.projectsData.length > 0) {
                renderProjectsCarousel(window.projectsData);
            }
        }, 1000);
        return;
    }

    // Create new carousel instance
    try {
        new ProjectsCarousel(carouselContainer, projectsToRender);
        console.log('✅ Carousel initialized successfully');
    } catch (error) {
        console.error('❌ Carousel initialization failed:', error);
        carouselContainer.innerHTML = `
            <div class="no-projects">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Błąd ładowania projektów</h3>
                <p>Spróbuj odświeżyć stronę</p>
                <button onclick="location.reload()" class="demo-button">Odśwież</button>
            </div>
        `;
    }
}

// Add CSS for carousel
function addCarouselStyles() {
    const styles = `
        .carousel-wrapper {
            position: relative;
            height: 500px;
            perspective: 1000px;
            overflow: hidden;
        }
        
        .carousel-track {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            transform-style: preserve-3d;
        }
        
        .project-carousel-card {
            position: absolute;
            width: 400px;
            height: 450px;
            transition: all 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
            cursor: pointer;
            transform-style: preserve-3d;
        }
        
        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transition: transform 0.6s;
        }
        
        .project-carousel-card.flipped .card-inner {
            transform: rotateY(180deg);
        }
        
        .card-front,
        .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            background: var(--bg-secondary);
            border: 1px solid rgba(0, 245, 255, 0.2);
            border-radius: 20px;
            overflow: hidden;
        }
        
        .card-back {
            transform: rotateY(180deg);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .project-image {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .image-placeholder {
            font-size: 3rem;
            color: white;
            opacity: 0.8;
        }
        
        .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .project-carousel-card:hover .project-overlay {
            opacity: 1;
        }
        
        .overlay-content {
            display: flex;
            gap: 1rem;
        }
        
        .preview-btn,
        .demo-btn,
        .launch-project-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }
        
        .preview-btn:hover,
        .demo-btn:hover,
        .launch-project-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 245, 255, 0.3);
        }
        
        .project-content {
            padding: 1.5rem;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .project-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }
        
        .project-category-badge {
            background: rgba(0, 245, 255, 0.2);
            color: var(--neon-blue);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .project-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .project-technologies {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .tech-tag {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.8rem;
        }
        
        .project-features {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .feature-item i {
            color: var(--neon-green);
            font-size: 0.8rem;
        }
        
        .carousel-controls {
            position: absolute;
            top: 50%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 2rem;
            pointer-events: none;
            z-index: 20;
        }
        
        .carousel-btn {
            background: rgba(0, 245, 255, 0.2);
            border: 1px solid rgba(0, 245, 255, 0.5);
            color: var(--neon-blue);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: all;
            backdrop-filter: blur(10px);
        }
        
        .carousel-btn:hover {
            background: rgba(0, 245, 255, 0.3);
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(0, 245, 255, 0.3);
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 0.5rem;
            z-index: 20;
        }
        
        .carousel-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .carousel-indicator.active {
            background: var(--neon-blue);
            box-shadow: 0 0 10px var(--neon-blue);
        }
        
        .carousel-info {
            position: absolute;
            top: 2rem;
            right: 2rem;
            text-align: right;
            z-index: 20;
        }
        
        .project-counter {
            font-family: var(--font-primary);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--neon-blue);
            text-shadow: 0 0 10px var(--neon-blue);
        }
        
        .project-category {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .no-projects {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
            text-align: center;
        }
        
        .no-projects i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--neon-blue);
        }

        .loading-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            background: var(--neon-blue);
            border-radius: 50%;
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
        .loading-dots span:nth-child(3) { animation-delay: 0s; }

        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @media (max-width: 768px) {
            .project-carousel-card {
                width: 300px;
                height: 400px;
            }
            
            .carousel-controls {
                padding: 0 1rem;
            }
            
            .carousel-btn {
                width: 40px;
                height: 40px;
            }
        }
    `;
    
    // Add styles to document if not already added
    if (!document.querySelector('#carousel-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'carousel-styles';
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
}

// Initialize carousel styles
document.addEventListener('DOMContentLoaded', () => {
    addCarouselStyles();
});

// Export for global use
window.ProjectsCarousel = ProjectsCarousel;
window.renderProjectsCarousel = renderProjectsCarousel;
