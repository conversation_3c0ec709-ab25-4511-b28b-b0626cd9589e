// Complete User Flow Management for SmartCard.pl
class CompleteFlowManager {
    constructor() {
        this.currentFlow = null;
        this.flowStack = [];
        this.flows = this.initializeFlows();
        this.init();
    }

    init() {
        this.setupFlowRouting();
        this.setupUserInteractionTracking();
        this.loadOnboardingState();
        this.validateUserJourney();
    }

    initializeFlows() {
        return {
            // Landing Page Flow
            landing: {
                name: 'Landing Page Experience',
                steps: [
                    'hero_view',
                    'feature_exploration',
                    'demo_interaction',
                    'pricing_review',
                    'signup_decision'
                ],
                nextFlow: 'authentication'
            },

            // Authentication Flow
            authentication: {
                name: 'User Authentication',
                steps: [
                    'auth_modal_open',
                    'form_completion',
                    'verification',
                    'account_creation',
                    'welcome_setup'
                ],
                nextFlow: 'onboarding'
            },

            // Onboarding Flow
            onboarding: {
                name: 'User Onboarding',
                steps: [
                    'welcome_tour',
                    'profile_setup',
                    'first_card_creation',
                    'feature_introduction',
                    'completion_celebration'
                ],
                nextFlow: 'card_creation'
            },

            // Card Creation Flow
            card_creation: {
                name: 'Business Card Creation',
                steps: [
                    'template_selection',
                    'personal_info_input',
                    'ai_avatar_generation',
                    'customization',
                    'preview_review',
                    'save_and_share'
                ],
                nextFlow: 'card_management'
            },

            // Card Management Flow
            card_management: {
                name: 'Card Management',
                steps: [
                    'dashboard_overview',
                    'card_editing',
                    'analytics_review',
                    'sharing_setup',
                    'export_options'
                ],
                nextFlow: 'advanced_features'
            },

            // Advanced Features Flow
            advanced_features: {
                name: 'Advanced Features',
                steps: [
                    'team_management',
                    'api_integration',
                    'white_label_setup',
                    'enterprise_features'
                ],
                nextFlow: 'retention'
            },

            // Retention Flow
            retention: {
                name: 'User Retention',
                steps: [
                    'regular_usage',
                    'feature_discovery',
                    'upgrade_prompts',
                    'feedback_collection',
                    'referral_program'
                ],
                nextFlow: null
            }
        };
    }

    setupFlowRouting() {
        // Track page navigation
        window.addEventListener('popstate', () => {
            this.handleRouteChange();
        });

        // Track hash changes
        window.addEventListener('hashchange', () => {
            this.handleHashChange();
        });
    }

    setupUserInteractionTracking() {
        // Track all user interactions
        document.addEventListener('click', (e) => {
            this.trackInteraction('click', e.target);
        });

        document.addEventListener('input', (e) => {
            this.trackInteraction('input', e.target);
        });

        document.addEventListener('submit', (e) => {
            this.trackInteraction('submit', e.target);
        });

        // Track scroll behavior
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.trackScrollBehavior();
            }, 100);
        });
    }

    startFlow(flowName, context = {}) {
        console.log(`🚀 Starting flow: ${flowName}`, context);
        
        const flow = this.flows[flowName];
        if (!flow) {
            console.error(`Flow ${flowName} not found`);
            return;
        }

        this.currentFlow = {
            name: flowName,
            config: flow,
            currentStep: 0,
            context: context,
            startTime: Date.now(),
            interactions: []
        };

        this.flowStack.push(this.currentFlow);
        this.executeFlowStep(flow.steps[0]);
        this.trackFlowStart(flowName, context);
    }

    executeFlowStep(stepName) {
        console.log(`📍 Executing step: ${stepName}`);
        
        const stepHandler = this.getStepHandler(stepName);
        if (stepHandler) {
            stepHandler.call(this);
        }

        this.trackStepCompletion(stepName);
    }

    getStepHandler(stepName) {
        const handlers = {
            // Landing page handlers
            hero_view: this.handleHeroView,
            feature_exploration: this.handleFeatureExploration,
            demo_interaction: this.handleDemoInteraction,
            pricing_review: this.handlePricingReview,
            signup_decision: this.handleSignupDecision,

            // Authentication handlers
            auth_modal_open: this.handleAuthModalOpen,
            form_completion: this.handleFormCompletion,
            verification: this.handleVerification,
            account_creation: this.handleAccountCreation,
            welcome_setup: this.handleWelcomeSetup,

            // Onboarding handlers
            welcome_tour: this.handleWelcomeTour,
            profile_setup: this.handleProfileSetup,
            first_card_creation: this.handleFirstCardCreation,
            feature_introduction: this.handleFeatureIntroduction,
            completion_celebration: this.handleCompletionCelebration,

            // Card creation handlers
            template_selection: this.handleTemplateSelection,
            personal_info_input: this.handlePersonalInfoInput,
            ai_avatar_generation: this.handleAIAvatarGeneration,
            customization: this.handleCustomization,
            preview_review: this.handlePreviewReview,
            save_and_share: this.handleSaveAndShare,

            // Card management handlers
            dashboard_overview: this.handleDashboardOverview,
            card_editing: this.handleCardEditing,
            analytics_review: this.handleAnalyticsReview,
            sharing_setup: this.handleSharingSetup,
            export_options: this.handleExportOptions
        };

        return handlers[stepName];
    }

    // Landing Page Flow Handlers
    handleHeroView() {
        this.highlightElement('.hero');
        this.setupHeroInteractions();
        this.startEngagementTimer();
    }

    handleFeatureExploration() {
        this.createFeatureTooltips();
        this.enableFeatureHover();
        this.trackFeatureInterest();
    }

    handleDemoInteraction() {
        this.enableDemoMode();
        this.createInteractiveElements();
        this.setupDemoAnalytics();
    }

    handlePricingReview() {
        this.highlightPricingPlans();
        this.showValueProposition();
        this.trackPricingInteractions();
    }

    handleSignupDecision() {
        this.showSignupIncentives();
        this.createUrgencyElements();
        this.setupSignupTracking();
    }

    // Authentication Flow Handlers
    handleAuthModalOpen() {
        if (!document.getElementById('authModal')?.classList.contains('show')) {
            window.authSystem?.showAuthModal('register');
        }
        this.setupAuthFormValidation();
    }

    handleFormCompletion() {
        this.enableRealTimeValidation();
        this.showFormProgress();
        this.setupFormAnalytics();
    }

    handleVerification() {
        this.showVerificationSteps();
        this.setupEmailVerification();
        this.handleVerificationFlow();
    }

    handleAccountCreation() {
        this.processAccountCreation();
        this.setupUserProfile();
        this.prepareOnboarding();
    }

    handleWelcomeSetup() {
        this.showWelcomeMessage();
        this.collectInitialPreferences();
        this.setupPersonalization();
    }

    // Onboarding Flow Handlers
    handleWelcomeTour() {
        this.createOnboardingTour();
        this.setupTourNavigation();
        this.trackTourProgress();
    }

    handleProfileSetup() {
        this.showProfileSetupWizard();
        this.enableProfileCompletion();
        this.validateProfileData();
    }

    handleFirstCardCreation() {
        this.openCardCreatorGuided();
        this.setupGuidedExperience();
        this.trackFirstCardMetrics();
    }

    handleFeatureIntroduction() {
        this.introduceKeyFeatures();
        this.setupFeatureTour();
        this.trackFeatureAdoption();
    }

    handleCompletionCelebration() {
        this.showCompletionAnimation();
        this.collectFeedback();
        this.setupNextSteps();
    }

    // Card Creation Flow Handlers
    handleTemplateSelection() {
        this.enhanceTemplateSelector();
        this.showTemplatePreview();
        this.trackTemplatePreferences();
    }

    handlePersonalInfoInput() {
        this.setupSmartFormFilling();
        this.enableAutoComplete();
        this.validateFormData();
    }

    handleAIAvatarGeneration() {
        this.setupAIAvatarFlow();
        this.showGenerationProgress();
        this.handleAIResults();
    }

    handleCustomization() {
        this.enableAdvancedCustomization();
        this.setupRealTimePreview();
        this.trackCustomizationChoices();
    }

    handlePreviewReview() {
        this.showPreviewModal();
        this.enablePreviewInteractions();
        this.collectPreviewFeedback();
    }

    handleSaveAndShare() {
        this.setupSaveOptions();
        this.enableSharingFeatures();
        this.trackSharingBehavior();
    }

    // Card Management Flow Handlers
    handleDashboardOverview() {
        this.setupDashboardTour();
        this.showKeyMetrics();
        this.enableDashboardInteractions();
    }

    handleCardEditing() {
        this.setupEditingInterface();
        this.enableVersionControl();
        this.trackEditingPatterns();
    }

    handleAnalyticsReview() {
        this.showAnalyticsInsights();
        this.setupAnalyticsFlow();
        this.enableDataExport();
    }

    handleSharingSetup() {
        this.setupSharingChannels();
        this.enableSocialIntegration();
        this.trackSharingSuccess();
    }

    handleExportOptions() {
        this.showExportFormats();
        this.setupExportFlow();
        this.trackExportUsage();
    }

    // Flow Management Methods
    nextStep() {
        if (!this.currentFlow) return;

        const currentStepIndex = this.currentFlow.currentStep;
        const steps = this.currentFlow.config.steps;

        if (currentStepIndex < steps.length - 1) {
            this.currentFlow.currentStep++;
            this.executeFlowStep(steps[this.currentFlow.currentStep]);
        } else {
            this.completeFlow();
        }
    }

    previousStep() {
        if (!this.currentFlow) return;

        if (this.currentFlow.currentStep > 0) {
            this.currentFlow.currentStep--;
            const steps = this.currentFlow.config.steps;
            this.executeFlowStep(steps[this.currentFlow.currentStep]);
        }
    }

    completeFlow() {
        if (!this.currentFlow) return;

        console.log(`✅ Flow completed: ${this.currentFlow.name}`);
        
        const completedFlow = this.currentFlow;
        this.trackFlowCompletion(completedFlow);
        
        // Start next flow if defined
        const nextFlow = completedFlow.config.nextFlow;
        if (nextFlow) {
            this.startFlow(nextFlow, {
                previousFlow: completedFlow.name,
                context: completedFlow.context
            });
        }

        this.currentFlow = null;
    }

    // User Journey Validation
    validateUserJourney() {
        const user = window.authSystem?.currentUser;
        
        if (!user) {
            this.startFlow('landing');
        } else if (!user.onboarding?.completed) {
            this.startFlow('onboarding');
        } else if (user.cards.length === 0) {
            this.startFlow('card_creation');
        } else {
            this.startFlow('card_management');
        }
    }

    // Onboarding Tour Creation
    createOnboardingTour() {
        if (document.getElementById('onboardingTour')) return;

        const tour = document.createElement('div');
        tour.id = 'onboardingTour';
        tour.className = 'onboarding-tour-overlay';
        tour.innerHTML = `
            <div class="onboarding-tour">
                <div class="tour-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <span class="progress-text">Krok 1 z 5</span>
                </div>
                
                <div class="tour-content">
                    <div class="tour-step active" data-step="1">
                        <div class="step-icon">👋</div>
                        <h3>Witamy w SmartCard.pl!</h3>
                        <p>Poznaj najważniejsze funkcje, które pomogą Ci stworzyć profesjonalną wizytówkę AI w kilka minut.</p>
                        <div class="step-actions">
                            <button class="tour-btn secondary" onclick="skipOnboarding()">Pomiń</button>
                            <button class="tour-btn primary" onclick="nextTourStep()">Zaczynajmy</button>
                        </div>
                    </div>
                    
                    <div class="tour-step" data-step="2">
                        <div class="step-icon">🎨</div>
                        <h3>Kreator wizytówek</h3>
                        <p>Wybierz szablon, wypełnij dane i zobacz swoją wizytówkę w czasie rzeczywistym. AI pomoże Ci w każdym kroku.</p>
                        <div class="step-highlight" data-target=".creator-section"></div>
                        <div class="step-actions">
                            <button class="tour-btn secondary" onclick="previousTourStep()">Wstecz</button>
                            <button class="tour-btn primary" onclick="nextTourStep()">Dalej</button>
                        </div>
                    </div>
                    
                    <div class="tour-step" data-step="3">
                        <div class="step-icon">🤖</div>
                        <h3>Avatar AI</h3>
                        <p>Prześlij swoje zdjęcie, a nasza AI stworzy profesjonalny avatar w różnych stylach dostosowanych do Twojej branży.</p>
                        <div class="step-actions">
                            <button class="tour-btn secondary" onclick="previousTourStep()">Wstecz</button>
                            <button class="tour-btn primary" onclick="nextTourStep()">Dalej</button>
                        </div>
                    </div>
                    
                    <div class="tour-step" data-step="4">
                        <div class="step-icon">📊</div>
                        <h3>Analytics i śledzenie</h3>
                        <p>Zobacz kto i kiedy oglądał Twoją wizytówkę. Otrzymuj szczegółowe statystyki i insights o swoich kontaktach.</p>
                        <div class="step-actions">
                            <button class="tour-btn secondary" onclick="previousTourStep()">Wstecz</button>
                            <button class="tour-btn primary" onclick="nextTourStep()">Dalej</button>
                        </div>
                    </div>
                    
                    <div class="tour-step" data-step="5">
                        <div class="step-icon">🚀</div>
                        <h3>Gotowy do startu!</h3>
                        <p>Teraz możesz stworzyć swoją pierwszą wizytówkę. Wszystkie funkcje są dostępne - eksperymentuj i ciesz się rezultatami!</p>
                        <div class="step-actions">
                            <button class="tour-btn secondary" onclick="previousTourStep()">Wstecz</button>
                            <button class="tour-btn primary" onclick="completeTour()">Stwórz wizytówkę</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(tour);
        this.setupTourInteractions();
        this.showTourStep(1);
    }

    setupTourInteractions() {
        let currentTourStep = 1;
        const totalSteps = 5;

        window.nextTourStep = () => {
            if (currentTourStep < totalSteps) {
                currentTourStep++;
                this.showTourStep(currentTourStep);
            }
        };

        window.previousTourStep = () => {
            if (currentTourStep > 1) {
                currentTourStep--;
                this.showTourStep(currentTourStep);
            }
        };

        window.skipOnboarding = () => {
            this.completeTour();
        };

        window.completeTour = () => {
            this.markOnboardingCompleted();
            this.hideTour();
            this.startFlow('card_creation');
        };
    }

    showTourStep(stepNumber) {
        // Update progress
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        if (progressFill) {
            progressFill.style.width = `${(stepNumber / 5) * 100}%`;
        }
        
        if (progressText) {
            progressText.textContent = `Krok ${stepNumber} z 5`;
        }

        // Show current step
        document.querySelectorAll('.tour-step').forEach(step => {
            step.classList.remove('active');
        });
        
        const currentStep = document.querySelector(`[data-step="${stepNumber}"]`);
        if (currentStep) {
            currentStep.classList.add('active');
        }

        // Handle step-specific highlighting
        this.highlightTourTarget(stepNumber);
    }

    highlightTourTarget(stepNumber) {
        // Remove previous highlights
        document.querySelectorAll('.tour-highlight-active').forEach(el => {
            el.classList.remove('tour-highlight-active');
        });

        const targetMap = {
            2: '.creator-section',
            3: '.features-section .feature-card:first-child',
            4: '.features-section .feature-card:nth-child(5)'
        };

        const target = targetMap[stepNumber];
        if (target) {
            const element = document.querySelector(target);
            if (element) {
                element.classList.add('tour-highlight-active');
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }

    hideTour() {
        const tour = document.getElementById('onboardingTour');
        if (tour) {
            tour.classList.add('hiding');
            setTimeout(() => {
                document.body.removeChild(tour);
            }, 300);
        }
    }

    markOnboardingCompleted() {
        if (window.authSystem?.currentUser) {
            window.authSystem.currentUser.onboarding = {
                completed: true,
                completedAt: new Date().toISOString(),
                steps: 5
            };
            window.authSystem.saveUserToStorage(window.authSystem.currentUser);
        }
    }

    // Interaction Tracking
    trackInteraction(type, target) {
        if (!this.currentFlow) return;

        const interaction = {
            type: type,
            target: this.getElementIdentifier(target),
            timestamp: Date.now(),
            flowStep: this.currentFlow.config.steps[this.currentFlow.currentStep]
        };

        this.currentFlow.interactions.push(interaction);
        
        // Send to analytics
        this.sendInteractionAnalytics(interaction);
    }

    getElementIdentifier(element) {
        if (element.id) return `#${element.id}`;
        if (element.className) return `.${element.className.split(' ')[0]}`;
        return element.tagName.toLowerCase();
    }

    trackScrollBehavior() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        
        if (this.currentFlow) {
            this.currentFlow.scrollBehavior = {
                percent: scrollPercent,
                timestamp: Date.now()
            };
        }
    }

    // Analytics Integration
    trackFlowStart(flowName, context) {
        if (window.analyticsDashboard) {
            window.analyticsDashboard.trackEvent('flow_start', {
                flowName: flowName,
                context: context,
                timestamp: new Date().toISOString()
            });
        }
    }

    trackStepCompletion(stepName) {
        if (window.analyticsDashboard) {
            window.analyticsDashboard.trackEvent('step_completion', {
                stepName: stepName,
                flowName: this.currentFlow?.name,
                timestamp: new Date().toISOString()
            });
        }
    }

    trackFlowCompletion(flow) {
        const duration = Date.now() - flow.startTime;
        
        if (window.analyticsDashboard) {
            window.analyticsDashboard.trackEvent('flow_completion', {
                flowName: flow.name,
                duration: duration,
                interactions: flow.interactions.length,
                timestamp: new Date().toISOString()
            });
        }
    }

    sendInteractionAnalytics(interaction) {
        if (window.analyticsDashboard) {
            window.analyticsDashboard.trackEvent('user_interaction', interaction);
        }
    }

    // Route and Hash Handling
    handleRouteChange() {
        const path = window.location.pathname;
        
        // Handle different routes
        if (path === '/dashboard' || path.includes('dashboard')) {
            this.startFlow('card_management');
        } else if (path === '/create' || path.includes('create')) {
            this.startFlow('card_creation');
        } else if (path === '/analytics' || path.includes('analytics')) {
            this.executeFlowStep('analytics_review');
        }
    }

    handleHashChange() {
        const hash = window.location.hash;
        
        // Handle hash-based navigation
        if (hash === '#demo') {
            this.executeFlowStep('demo_interaction');
        } else if (hash === '#features') {
            this.executeFlowStep('feature_exploration');
        } else if (hash === '#pricing') {
            this.executeFlowStep('pricing_review');
        }
    }

    // Helper Methods
    highlightElement(selector) {
        const element = document.querySelector(selector);
        if (element) {
            element.classList.add('flow-highlight');
            setTimeout(() => {
                element.classList.remove('flow-highlight');
            }, 3000);
        }
    }

    createFeatureTooltips() {
        document.querySelectorAll('.feature-card').forEach((card, index) => {
            card.addEventListener('mouseenter', () => {
                this.showFeatureTooltip(card, index);
            });
        });
    }

    showFeatureTooltip(card, index) {
        const tooltips = [
            'Kliknij aby zobaczyć demo generatora avatarów AI',
            'Zobacz jak działa animacja 3D flip',
            'Sprawdź generator kodów QR w akcji',
            'Symulacja NFC - dotknij aby przetestować',
            'Otwórz szczegółowe analytics'
        ];

        const tooltip = document.createElement('div');
        tooltip.className = 'feature-tooltip';
        tooltip.textContent = tooltips[index] || 'Kliknij aby poznać więcej';
        
        card.appendChild(tooltip);
        
        setTimeout(() => {
            if (card.contains(tooltip)) {
                card.removeChild(tooltip);
            }
        }, 3000);
    }

    setupHeroInteractions() {
        const heroButtons = document.querySelectorAll('.hero .btn-primary, .hero .btn-secondary');
        heroButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.trackInteraction('hero_cta_click', button);
                this.nextStep();
            });
        });
    }

    startEngagementTimer() {
        setTimeout(() => {
            if (this.currentFlow?.name === 'landing') {
                this.showEngagementPrompt();
            }
        }, 30000); // After 30 seconds
    }

    showEngagementPrompt() {
        const prompt = document.createElement('div');
        prompt.className = 'engagement-prompt';
        prompt.innerHTML = `
            <div class="prompt-content">
                <h4>Potrzebujesz pomocy?</h4>
                <p>Widzę, że przeglądasz naszą stronę. Czy chciałbyś zobaczyć szybkie demo?</p>
                <div class="prompt-actions">
                    <button class="prompt-btn secondary" onclick="this.closest('.engagement-prompt').remove()">Nie, dziękuję</button>
                    <button class="prompt-btn primary" onclick="startQuickDemo()">Tak, pokaż demo!</button>
                </div>
            </div>
        `;

        document.body.appendChild(prompt);

        window.startQuickDemo = () => {
            prompt.remove();
            this.executeFlowStep('demo_interaction');
        };
    }

    loadOnboardingState() {
        const user = window.authSystem?.currentUser;
        if (user && user.onboarding?.completed) {
            console.log('✅ User has completed onboarding');
        } else {
            console.log('📝 User needs onboarding');
        }
    }

    // Error Handling and Recovery
    handleFlowError(error) {
        console.error('Flow error:', error);
        
        // Try to recover gracefully
        if (this.currentFlow) {
            this.trackFlowError(error);
            this.showErrorRecovery();
        }
    }

    showErrorRecovery() {
        const recovery = document.createElement('div');
        recovery.className = 'flow-error-recovery';
        recovery.innerHTML = `
            <div class="recovery-content">
                <h4>Ups! Coś poszło nie tak</h4>
                <p>Wystąpił problem. Czy chcesz spróbować ponownie?</p>
                <div class="recovery-actions">
                    <button class="recovery-btn secondary" onclick="this.closest('.flow-error-recovery').remove()">Anuluj</button>
                    <button class="recovery-btn primary" onclick="retryFlow()">Spróbuj ponownie</button>
                </div>
            </div>
        `;

        document.body.appendChild(recovery);

        window.retryFlow = () => {
            recovery.remove();
            if (this.currentFlow) {
                this.executeFlowStep(this.currentFlow.config.steps[this.currentFlow.currentStep]);
            }
        };
    }

    trackFlowError(error) {
        if (window.analyticsDashboard) {
            window.analyticsDashboard.trackEvent('flow_error', {
                flowName: this.currentFlow?.name,
                stepName: this.currentFlow?.config.steps[this.currentFlow?.currentStep],
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    // Flow State Management
    saveFlowState() {
        if (this.currentFlow) {
            localStorage.setItem('smartcard-flow-state', JSON.stringify({
                currentFlow: this.currentFlow,
                timestamp: Date.now()
            }));
        }
    }

    loadFlowState() {
        try {
            const saved = localStorage.getItem('smartcard-flow-state');
            if (saved) {
                const state = JSON.parse(saved);
                
                // Only restore if less than 1 hour old
                if (Date.now() - state.timestamp < 60 * 60 * 1000) {
                    this.currentFlow = state.currentFlow;
                    return true;
                }
            }
        } catch (error) {
            console.error('Failed to load flow state:', error);
        }
        
        return false;
    }

    clearFlowState() {
        localStorage.removeItem('smartcard-flow-state');
    }

    // Public API
    getCurrentFlow() {
        return this.currentFlow;
    }

    getFlowProgress() {
        if (!this.currentFlow) return null;
        
        return {
            flowName: this.currentFlow.name,
            currentStep: this.currentFlow.currentStep + 1,
            totalSteps: this.currentFlow.config.steps.length,
            progress: ((this.currentFlow.currentStep + 1) / this.currentFlow.config.steps.length) * 100
        };
    }

    forceFlow(flowName) {
        this.startFlow(flowName, { forced: true });
    }
}

// Initialize Complete Flow Manager
document.addEventListener('DOMContentLoaded', () => {
    window.completeFlowManager = new CompleteFlowManager();
    
    // Auto-start appropriate flow
    setTimeout(() => {
        window.completeFlowManager.validateUserJourney();
    }, 1000);
});

// Global functions for flow control
window.startFlow = (flowName, context) => window.completeFlowManager?.startFlow(flowName, context);
window.nextFlowStep = () => window.completeFlowManager?.nextStep();
window.previousFlowStep = () => window.completeFlowManager?.previousStep();
window.getFlowProgress = () => window.completeFlowManager?.getFlowProgress();

// Export for global access
window.CompleteFlowManager = CompleteFlowManager;