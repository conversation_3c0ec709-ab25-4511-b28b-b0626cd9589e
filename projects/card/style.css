/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - 2025 Premium */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    /* Glassmorphism Colors */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: rgba(0, 0, 0, 0.1);
    --backdrop-blur: blur(20px);
    
    /* Dark Theme */
    --dark-bg: #0a0a0a;
    --dark-card: rgba(20, 20, 30, 0.8);
    --dark-text: #ffffff;
    --dark-text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Borders */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Animations */
    --animation-fast: 0.2s ease-out;
    --animation-normal: 0.3s ease-out;
    --animation-slow: 0.5s ease-out;
    --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-text);
    background: var(--dark-bg);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* Language Selector */
.language-selector {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 8px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-full);
    padding: 8px;
}

.lang-btn {
    background: transparent;
    border: none;
    color: var(--dark-text-secondary);
    padding: 8px 12px;
    border-radius: var(--border-radius-full);
    cursor: pointer;
    transition: all var(--animation-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.lang-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--dark-text);
}

.lang-btn.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border-bottom: 1px solid var(--glass-border);
    padding: 1rem 0;
    transition: all var(--animation-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--dark-text);
}

.nav-logo i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-menu a {
    color: var(--dark-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--animation-fast);
    position: relative;
}

.nav-menu a:hover {
    color: var(--dark-text);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width var(--animation-fast);
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius-full);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-fast);
    box-shadow: var(--shadow-md);
}

.nav-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 0;
}

.floating-card {
    position: absolute;
    width: 300px;
    height: 180px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    animation: float 6s ease-in-out infinite;
}

.floating-card.card-1 {
    top: 10%;
    left: -10%;
    animation-delay: -2s;
}

.floating-card.card-2 {
    top: 60%;
    right: -10%;
    animation-delay: -4s;
}

.floating-card.card-3 {
    top: 30%;
    left: 50%;
    animation-delay: -1s;
    opacity: 0.3;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    padding: 8px 16px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-text-secondary);
    width: fit-content;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.2); }
    to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.4); }
}

.hero-badge i {
    color: #ffd700;
    animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    color: var(--dark-text);
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.gradient-text::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-gradient);
    border-radius: 2px;
    animation: underline-expand 2s ease-out;
}

@keyframes underline-expand {
    from { width: 0; }
    to { width: 100%; }
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--dark-text-secondary);
    line-height: 1.6;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin: 1rem 0;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--dark-text-secondary);
    font-weight: 500;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-normal);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl);
}

.btn-primary:active {
    transform: translateY(-1px);
}

.btn-secondary {
    background: transparent;
    color: var(--dark-text);
    border: 2px solid var(--glass-border);
    padding: 14px 30px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-normal);
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: var(--backdrop-blur);
}

.btn-secondary:hover {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    transform: translateY(-2px);
}

.hero-social-proof {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.social-proof-avatars {
    display: flex;
    align-items: center;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid white;
    margin-left: -8px;
    position: relative;
    z-index: 1;
}

.avatar:first-child {
    margin-left: 0;
}

.avatar-more {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 2px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--dark-text);
    margin-left: -8px;
}

.hero-social-proof p {
    font-size: var(--font-size-sm);
    color: var(--dark-text-secondary);
    max-width: 250px;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.business-card-preview {
    width: 350px;
    height: 220px;
    position: relative;
    perspective: 1000px;
    cursor: pointer;
}

.card-front,
.card-back {
    width: 100%;
    height: 100%;
    position: absolute;
    backface-visibility: hidden;
    border-radius: var(--border-radius-lg);
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-2xl);
    transition: transform 0.6s;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-back {
    transform: rotateY(180deg);
}

.business-card-preview.flipped .card-front {
    transform: rotateY(-180deg);
}

.business-card-preview.flipped .card-back {
    transform: rotateY(0deg);
}

.card-avatar {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.card-info h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dark-text);
    margin-bottom: 4px;
}

.card-info p {
    font-size: var(--font-size-sm);
    color: var(--dark-text-secondary);
    margin-bottom: 2px;
}

.card-qr {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 60px;
    height: 60px;
}

.qr-code {
    width: 100%;
    height: 100%;
    background: var(--dark-text);
    border-radius: var(--border-radius-sm);
    position: relative;
    overflow: hidden;
}

.qr-code::before,
.qr-code::after {
    content: '';
    position: absolute;
    background: var(--primary-gradient);
}

.qr-code::before {
    top: 8px;
    left: 8px;
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.qr-code::after {
    bottom: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.card-contact {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: var(--font-size-sm);
    color: var(--dark-text-secondary);
}

.contact-item i {
    width: 20px;
    color: var(--primary-gradient);
}

.card-nfc {
    text-align: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: var(--border-radius-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.card-nfc i {
    font-size: 1.5rem;
    color: var(--primary-gradient);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.phone-mockup {
    position: absolute;
    top: -50px;
    right: -100px;
    width: 150px;
    height: 300px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 2px solid var(--glass-border);
    border-radius: 25px;
    padding: 20px 15px;
    opacity: 0.8;
    animation: phoneFloat 4s ease-in-out infinite;
}

@keyframes phoneFloat {
    0%, 100% { transform: translateY(0px) rotate(-5deg); }
    50% { transform: translateY(-15px) rotate(-3deg); }
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: var(--dark-bg);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.scanning-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: var(--primary-gradient);
}

.scan-line {
    width: 80px;
    height: 2px;
    background: var(--accent-gradient);
    animation: scanMove 2s ease-in-out infinite;
}

@keyframes scanMove {
    0%, 100% { transform: translateY(-20px) scaleX(0.5); }
    50% { transform: translateY(20px) scaleX(1); }
}

.scanning-animation i {
    font-size: 2rem;
    animation: scanPulse 2s ease-in-out infinite;
}

@keyframes scanPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Creator Section */
.creator-section {
    padding: 6rem 0;
    background: linear-gradient(180deg, transparent 0%, rgba(102, 126, 234, 0.05) 50%, transparent 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--dark-text);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--dark-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.creator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.creator-form {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 100px;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-text-secondary);
    margin-bottom: 4px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text);
    font-size: var(--font-size-base);
    transition: all var(--animation-fast);
    backdrop-filter: var(--backdrop-blur);
}

.form-group input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-group input::placeholder {
    color: var(--dark-text-secondary);
}

.avatar-generator {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.upload-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: var(--secondary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
    font-weight: 500;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.avatar-styles {
    display: flex;
    gap: 8px;
}

.style-btn {
    flex: 1;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    font-size: var(--font-size-sm);
}

.style-btn:hover,
.style-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.template-selector {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.template-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 1rem;
    background: transparent;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--animation-fast);
}

.template-item:hover,
.template-item.active {
    background: var(--glass-bg);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-2px);
}

.template-preview {
    width: 60px;
    height: 36px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.template-preview.modern {
    background: var(--primary-gradient);
}

.template-preview.elegant {
    background: var(--secondary-gradient);
}

.template-preview.creative {
    background: var(--accent-gradient);
}

.template-item span {
    font-size: var(--font-size-xs);
    color: var(--dark-text-secondary);
    font-weight: 500;
}

/* Live Preview */
.live-preview {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 100px;
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.preview-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-text);
}

.preview-controls {
    display: flex;
    gap: 8px;
}

.flip-btn,
.fullscreen-btn {
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.flip-btn:hover,
.fullscreen-btn:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.card-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    min-height: 250px;
}

.business-card {
    width: 300px;
    height: 190px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    transition: all var(--animation-normal);
}

.business-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
}

.preview-actions {
    display: flex;
    gap: 1rem;
}

.btn-generate,
.btn-download,
.btn-share {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--animation-fast);
    background: transparent;
    color: var(--dark-text-secondary);
}

.btn-generate {
    background: var(--success-gradient);
    color: white;
    border: none;
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-download:hover,
.btn-share:hover {
    background: var(--glass-bg);
    color: var(--dark-text);
}

/* Features Section */
.features-section {
    padding: 6rem 0;
    background: radial-gradient(ellipse at center, rgba(240, 147, 251, 0.1) 0%, transparent 70%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    text-align: center;
    transition: all var(--animation-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(102, 126, 234, 0.3);
}

.wow-feature {
    position: relative;
}

.wow-feature::after {
    content: '✨';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.2rem;
    animation: sparkle 2s ease-in-out infinite;
}

.premium-feature {
    border: 2px solid var(--warning-gradient);
    position: relative;
}

.premium-badge {
    position: absolute;
    top: -10px;
    right: 1rem;
    background: var(--warning-gradient);
    color: white;
    padding: 4px 12px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dark-text);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--dark-text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-demo {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(102, 126, 234, 0.05);
    border-radius: var(--border-radius-md);
    position: relative;
    overflow: hidden;
}

.avatar-transformation {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.5rem;
}

.arrow {
    color: var(--primary-gradient);
    font-weight: bold;
    animation: moveRight 2s ease-in-out infinite;
}

@keyframes moveRight {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(5px); }
}

.flip-demo {
    width: 60px;
    height: 40px;
    perspective: 200px;
}

.demo-card {
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    animation: flipCard 3s ease-in-out infinite;
    transform-style: preserve-3d;
}

@keyframes flipCard {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
}

.qr-demo {
    width: 50px;
    height: 50px;
    position: relative;
}

.qr-square {
    width: 100%;
    height: 100%;
    background: var(--dark-text);
    border-radius: 4px;
    position: relative;
}

.qr-square::before,
.qr-square::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--primary-gradient);
    border-radius: 1px;
}

.qr-square::before {
    top: 4px;
    left: 4px;
}

.qr-square::after {
    bottom: 4px;
    right: 4px;
}

.scan-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid var(--accent-gradient);
    border-radius: 4px;
    animation: scanBorder 2s ease-in-out infinite;
}

@keyframes scanBorder {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.nfc-demo {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.phone-icon {
    font-size: 2rem;
    z-index: 2;
}

.nfc-waves {
    position: absolute;
    width: 40px;
    height: 40px;
    border: 2px solid var(--accent-gradient);
    border-radius: 50%;
    animation: nfcPulse 2s ease-out infinite;
}

.nfc-waves::before,
.nfc-waves::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--accent-gradient);
    border-radius: 50%;
    animation: nfcPulse 2s ease-out infinite;
}

.nfc-waves::before {
    animation-delay: -0.5s;
}

.nfc-waves::after {
    animation-delay: -1s;
}

@keyframes nfcPulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(2);
    }
}

.analytics-demo {
    display: flex;
    align-items: end;
    justify-content: center;
    gap: 8px;
    height: 50px;
}

.chart-bar {
    width: 8px;
    background: var(--primary-gradient);
    border-radius: 2px 2px 0 0;
    animation: chartGrow 2s ease-out infinite;
}

.chart-bar:nth-child(2) { animation-delay: -0.5s; }
.chart-bar:nth-child(3) { animation-delay: -1s; }
.chart-bar:nth-child(4) { animation-delay: -1.5s; }

@keyframes chartGrow {
    0%, 100% { transform: scaleY(0.3); }
    50% { transform: scaleY(1); }
}

.voice-demo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.voice-wave {
    font-size: 1.5rem;
    animation: voiceWave 1.5s ease-in-out infinite;
}

.record-btn {
    font-size: 1.5rem;
    animation: recordPulse 2s ease-in-out infinite;
}

@keyframes voiceWave {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(1.5); }
}

@keyframes recordPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.team-demo {
    display: flex;
    gap: 8px;
    font-size: 1.5rem;
}

.team-member {
    animation: teamBounce 2s ease-in-out infinite;
}

.team-member:nth-child(2) { animation-delay: -0.5s; }
.team-member:nth-child(3) { animation-delay: -1s; }

@keyframes teamBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.calendar-demo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.5rem;
}

.plus-icon {
    color: var(--success-gradient);
    animation: plusGlow 2s ease-in-out infinite;
}

@keyframes plusGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.ar-demo {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.5rem;
}

.camera-frame {
    position: relative;
}

.camera-frame::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid var(--accent-gradient);
    border-radius: 4px;
    animation: cameraFocus 3s ease-in-out infinite;
}

@keyframes cameraFocus {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.ar-card {
    animation: arFloat 3s ease-in-out infinite;
}

@keyframes arFloat {
    0%, 100% { transform: translateY(0) rotateY(0deg); }
    50% { transform: translateY(-10px) rotateY(180deg); }
}

/* Templates Section */
.templates-section {
    padding: 6rem 0;
    background: linear-gradient(180deg, rgba(79, 172, 254, 0.05) 0%, rgba(0, 242, 254, 0.05) 100%);
}

.templates-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.template-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    text-align: center;
    transition: all var(--animation-normal);
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(102, 126, 234, 0.3);
}

.template-preview {
    height: 150px;
    border-radius: var(--border-radius-md);
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-animation {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-md);
}

.modern-anim {
    background: var(--primary-gradient);
    position: relative;
}

.modern-anim::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    right: 20%;
    bottom: 20%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-md);
    animation: glassFloat 3s ease-in-out infinite;
}

@keyframes glassFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-5px) scale(1.05); }
}

.neon-anim {
    background: #000;
    position: relative;
}

.neon-anim::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 20%;
    background: var(--accent-gradient);
    border-radius: var(--border-radius-full);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.5);
    animation: neonGlow 2s ease-in-out infinite alternate;
}

@keyframes neonGlow {
    from { box-shadow: 0 0 20px rgba(79, 172, 254, 0.5); }
    to { box-shadow: 0 0 40px rgba(79, 172, 254, 0.8); }
}

.minimal-anim {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
}

.minimal-anim::before {
    content: '';
    position: absolute;
    top: 30%;
    left: 30%;
    width: 40%;
    height: 2px;
    background: var(--primary-gradient);
    animation: minimalLine 3s ease-in-out infinite;
}

@keyframes minimalLine {
    0%, 100% { width: 40%; opacity: 0.5; }
    50% { width: 60%; opacity: 1; }
}

.creative-anim {
    background: var(--secondary-gradient);
    position: relative;
}

.creative-anim::before,
.creative-anim::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.creative-anim::before {
    top: 20%;
    left: 20%;
    animation: creativeFloat1 4s ease-in-out infinite;
}

.creative-anim::after {
    bottom: 20%;
    right: 20%;
    animation: creativeFloat2 4s ease-in-out infinite reverse;
}

@keyframes creativeFloat1 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(10px, -10px) scale(1.2); }
}

@keyframes creativeFloat2 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(-10px, 10px) scale(1.2); }
}

.template-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 0.5rem;
}

.template-card p {
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
}

/* Pricing Section */
.pricing-section {
    padding: 6rem 0;
    background: radial-gradient(ellipse at center, rgba(250, 112, 154, 0.1) 0%, transparent 70%);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    text-align: center;
    transition: all var(--animation-normal);
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
}

.pricing-card.popular {
    border: 2px solid var(--primary-gradient);
    transform: scale(1.05);
}

.pricing-card.popular:hover {
    transform: scale(1.05) translateY(-5px);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-gradient);
    color: white;
    padding: 4px 16px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.pricing-header {
    margin-bottom: 2rem;
}

.pricing-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--dark-text);
    margin-bottom: 1rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
}

.currency {
    font-size: var(--font-size-lg);
    color: var(--dark-text-secondary);
}

.amount {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.period {
    font-size: var(--font-size-base);
    color: var(--dark-text-secondary);
}

.pricing-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: var(--dark-text-secondary);
}

.pricing-features i {
    color: var(--success-gradient);
    font-size: var(--font-size-sm);
}

.pricing-btn {
    width: 100%;
    padding: 14px 24px;
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-full);
    background: transparent;
    color: var(--dark-text);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-normal);
}

.pricing-btn:hover {
    background: var(--glass-bg);
    transform: translateY(-2px);
}

.pricing-btn.primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
}

.pricing-btn.primary:hover {
    background: var(--secondary-gradient);
    box-shadow: var(--shadow-lg);
}

/* Footer */
.footer {
    background: var(--dark-card);
    backdrop-filter: var(--backdrop-blur);
    border-top: 1px solid var(--glass-border);
    padding: 3rem 0 1rem;
    margin-top: 6rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--dark-text);
}

.footer-logo i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-brand p {
    color: var(--dark-text-secondary);
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-column h4 {
    color: var(--dark-text);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: var(--font-size-base);
}

.footer-column a {
    display: block;
    color: var(--dark-text-secondary);
    text-decoration: none;
    padding: 4px 0;
    transition: color var(--animation-fast);
    font-size: var(--font-size-sm);
}

.footer-column a:hover {
    color: var(--dark-text);
}

.footer-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 2rem;
    border-top: 1px solid var(--glass-border);
    color: var(--dark-text-secondary);
    font-size: var(--font-size-sm);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-text-secondary);
    transition: all var(--animation-fast);
}

.footer-social a:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .creator-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .creator-form,
    .live-preview {
        position: static;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .nav-menu {
        display: none;
    }
}

@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 0 1rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .business-card-preview {
        width: 280px;
        height: 180px;
    }
    
    .phone-mockup {
        display: none;
    }
    
    .template-selector {
        grid-template-columns: 1fr;
    }
    
    .preview-actions {
        flex-direction: column;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .pricing-card.popular {
        transform: none;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles */
button:focus-visible,
input:focus-visible,
a:focus-visible {
    outline: 2px solid var(--primary-gradient);
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.2);
        --glass-border: rgba(255, 255, 255, 0.4);
    }
}

/* Print styles */
@media print {
    .hero-background,
    .phone-mockup,
    .feature-demo,
    .template-animation {
        display: none !important;
    }
    
    .hero-container,
    .creator-container {
        grid-template-columns: 1fr;
    }
}